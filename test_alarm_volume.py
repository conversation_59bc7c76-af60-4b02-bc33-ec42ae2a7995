#!/usr/bin/env python3
"""
测试闹钟音量获取功能
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from pages.base.system_status_checker import SystemStatusChecker
from core.logger import log


def test_alarm_volume():
    """测试闹钟音量获取功能"""
    print("🔊 测试闹钟音量获取功能")
    print("=" * 50)
    
    try:
        # 创建系统状态检查器实例
        checker = SystemStatusChecker()
        
        # 测试获取闹钟音量数值
        print("\n📊 获取闹钟音量数值...")
        alarm_volume = checker.get_alarm_volume()
        
        if alarm_volume != -1:
            print(f"✅ 闹钟音量: {alarm_volume}")
        else:
            print("❌ 获取闹钟音量失败")
        
        # 测试获取闹钟音量百分比
        print("\n📊 获取闹钟音量百分比...")
        alarm_percentage = checker.get_alarm_volume_percentage()
        
        if alarm_percentage != -1:
            print(f"✅ 闹钟音量百分比: {alarm_percentage}%")
        else:
            print("❌ 获取闹钟音量百分比失败")
        
        # 对比通用音量获取方法
        print("\n📊 对比通用音量获取方法...")
        general_alarm_volume = checker.get_system_volume("alarm")
        
        if general_alarm_volume != -1:
            print(f"✅ 通用方法获取闹钟音量: {general_alarm_volume}")
        else:
            print("❌ 通用方法获取闹钟音量失败")
        
        # 结果对比
        if alarm_volume != -1 and general_alarm_volume != -1:
            if alarm_volume == general_alarm_volume:
                print(f"✅ 两种方法结果一致: {alarm_volume}")
            else:
                print(f"⚠️ 两种方法结果不一致: 专用方法={alarm_volume}, 通用方法={general_alarm_volume}")
        
        # 测试其他音量类型作为对比
        print("\n📊 获取其他音量类型作为对比...")
        media_volume = checker.get_system_volume("media")
        ring_volume = checker.get_system_volume("ring")
        
        print(f"媒体音量: {media_volume}")
        print(f"铃声音量: {ring_volume}")
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_alarm_volume()
