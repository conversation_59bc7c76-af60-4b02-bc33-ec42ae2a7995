{"uid": "4b4757e66a1912dae1a509f688f20b0f", "name": "categories", "children": [{"name": "Product defects", "children": [{"name": "AssertionError: 响应未包含期望内容: ['The following event has been added for you.']\nassert False", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "cbf0dcc5d9990cc2", "parentUid": "6ca1a954a23c1af31dbdbbe03bb017f6", "status": "failed", "time": {"start": 1754446668758, "stop": 1754446684884, "duration": 16126}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6ca1a954a23c1af31dbdbbe03bb017f6"}, {"name": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False", "children": [{"name": "测试display the route go company", "uid": "92ee4d46312fe03", "parentUid": "677dc38d3c35216f2db444faeff94fb4", "status": "failed", "time": {"start": 1754446729951, "stop": 1754446745234, "duration": 15283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "677dc38d3c35216f2db444faeff94fb4"}, {"name": "AssertionError: 响应未包含期望内容: ['5 minutes', '10 minutes', '20 minutes']\nassert False", "children": [{"name": "测试open countdown能正常执行", "uid": "c2dada2a8cd202f3", "parentUid": "75d852aa63b861f204bde9067fb234d7", "status": "failed", "time": {"start": 1754446916540, "stop": 1754446930020, "duration": 13480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "75d852aa63b861f204bde9067fb234d7"}, {"name": "AssertionError: 响应未包含期望内容: ['Spotify is not installed yet. Please download the app and try again.']\nassert False", "children": [{"name": "测试play jay chou's music by spotify", "uid": "538bea4536324700", "parentUid": "e5917493b6cc2b85cf7728b003fdff4d", "status": "failed", "time": {"start": 1754447205460, "stop": 1754447221759, "duration": 16299}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e5917493b6cc2b85cf7728b003fdff4d"}, {"name": "AssertionError: 响应未包含期望内容: ['Recording completed']\nassert False", "children": [{"name": "测试record audio for 5 seconds能正常执行", "uid": "b21a04cbb6917b66", "parentUid": "55e4441528c5a8f13d73944990287fec", "status": "failed", "time": {"start": 1754447344785, "stop": 1754447359784, "duration": 14999}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55e4441528c5a8f13d73944990287fec"}, {"name": "AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nassert None", "children": [{"name": "测试start run能正常执行", "uid": "32a5aa19059db7ec", "parentUid": "e032cf8f1c51a33b87abab3ca550fcd9", "status": "failed", "time": {"start": 1754447430794, "stop": 1754447456077, "duration": 25283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e032cf8f1c51a33b87abab3ca550fcd9"}, {"name": "AssertionError: 响应未包含期望内容: ['Calling Mom...']\nassert False", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "f7063b5f8370255c", "parentUid": "defbb428537d958d0deb475c25bce911", "status": "failed", "time": {"start": 1754447622142, "stop": 1754447645193, "duration": 23051}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试video call mom through whatsapp能正常执行", "uid": "334b9b2e3cf14180", "parentUid": "defbb428537d958d0deb475c25bce911", "status": "failed", "time": {"start": 1754448881747, "stop": 1754448903362, "duration": 21615}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "defbb428537d958d0deb475c25bce911"}, {"name": "AssertionError: 响应未包含期望内容: ['Redirecting to']\nassert False", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "548281780cb22e82", "parentUid": "ba6228f3035826c776335924616bf378", "status": "failed", "time": {"start": 1754447721944, "stop": 1754447736646, "duration": 14702}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ba6228f3035826c776335924616bf378"}, {"name": "AssertionError: 响应未包含期望内容: ['Search Information']\nassert False", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "4a731551f351aafd", "parentUid": "9340816f8142803a53cbe42467ec469f", "status": "failed", "time": {"start": 1754447779841, "stop": 1754447794688, "duration": 14847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9340816f8142803a53cbe42467ec469f"}, {"name": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "16c844c54a22bc45", "parentUid": "cd83a8ba9fa1be6164e1ab47cca1e5e3", "status": "failed", "time": {"start": 1754448494705, "stop": 1754448511861, "duration": 17156}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd83a8ba9fa1be6164e1ab47cca1e5e3"}, {"name": "AssertionError: 响应未包含期望内容: ['Premier League Goals Ranking']\nassert False", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "c0c7a1cff2f3771e", "parentUid": "968491c31f7e73b0cd2b15d7bdd63075", "status": "failed", "time": {"start": 1754448526292, "stop": 1754448541648, "duration": 15356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968491c31f7e73b0cd2b15d7bdd63075"}, {"name": "AssertionError: 响应未包含期望内容: [\"You haven't started working out yet\"]\nassert False", "children": [{"name": "测试stop run能正常执行", "uid": "3a66753f7b7cbcdf", "parentUid": "3155e50fb9e321241ff3f34ced210080", "status": "failed", "time": {"start": 1754448626044, "stop": 1754448654749, "duration": 28705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop workout能正常执行", "uid": "1efbda1b523115da", "parentUid": "3155e50fb9e321241ff3f34ced210080", "status": "failed", "time": {"start": 1754448668894, "stop": 1754448697708, "duration": 28814}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3155e50fb9e321241ff3f34ced210080"}, {"name": "AssertionError: 响应未包含期望内容: [\"Here's a joke for you\"]\nassert False", "children": [{"name": "测试tell me a joke能正常执行", "uid": "b6962caf581b09dc", "parentUid": "451b5a50bea9158be02a728e085db107", "status": "failed", "time": {"start": 1754448852645, "stop": 1754448867433, "duration": 14788}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "451b5a50bea9158be02a728e085db107"}, {"name": "AssertionError: 响应未包含期望内容: ['Chong Qing Shi is Fair today. The high is forecast as 37°C and the low as 28°C.']\nassert False", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "c09dbe14527ecc7c", "parentUid": "1ff649f1409abb91bfbcbcab690d9ccb", "status": "failed", "time": {"start": 1754449044387, "stop": 1754449058301, "duration": 13914}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ff649f1409abb91bfbcbcab690d9ccb"}, {"name": "AssertionError: 响应未包含期望内容: [\"I'm <PERSON>\"]\nassert False", "children": [{"name": "测试what's your name？能正常执行", "uid": "bb166634eb4139ad", "parentUid": "c15d6b1f607bf3f93c73e2b7bcbfe744", "status": "failed", "time": {"start": 1754449072637, "stop": 1754449086313, "duration": 13676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c15d6b1f607bf3f93c73e2b7bcbfe744"}, {"name": "AssertionError: 响应未包含期望内容: ['These suggestions are for your reference']\nassert False", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "685a39e7eb19f7df", "parentUid": "83e6ce370469a50cbcd5886ffacbabdd", "status": "failed", "time": {"start": 1754449259853, "stop": 1754449273809, "duration": 13956}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83e6ce370469a50cbcd5886ffacbabdd"}, {"name": "AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nassert None", "children": [{"name": "测试open calculator", "uid": "8b898a5d16f54ad9", "parentUid": "f6bc37fc4666b7b7ea367e5712b55913", "status": "failed", "time": {"start": 1754449288241, "stop": 1754449304334, "duration": 16093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f6bc37fc4666b7b7ea367e5712b55913"}, {"name": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "children": [{"name": "测试check front camera information能正常执行", "uid": "7776530c69781978", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754449405309, "stop": 1754449428775, "duration": 23466}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger能正常执行", "uid": "ff31feb8037e8115", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754451619144, "stop": 1754451632488, "duration": 13344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway能正常执行", "uid": "51c4a2ce03ff9f3b", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754451646749, "stop": 1754451660700, "duration": 13951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "68918f24304aea29", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754452555984, "stop": 1754452570723, "duration": 14739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today in shanghai返回正确的不支持响应", "uid": "886a52d253902aed", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754453003196, "stop": 1754453023848, "duration": 20652}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger返回正确的不支持响应", "uid": "fe333cd2d054813e", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754453627864, "stop": 1754453641483, "duration": 13619}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway返回正确的不支持响应", "uid": "2f730eec0c9ba595", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754453655065, "stop": 1754453668838, "duration": 13773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "a00dfe1c97e10826", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754454148304, "stop": 1754454164863, "duration": 16559}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set languages返回正确的不支持响应", "uid": "8064999615978c60", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1754454635493, "stop": 1754454650849, "duration": 15356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c7fcb7e700713ec9d3081e0b69d1f2e"}, {"name": "AssertionError: 响应未包含期望内容: ['done']\nassert False", "children": [{"name": "测试clear junk files命令", "uid": "2b68b27e39c0eb31", "parentUid": "2b4278c79623ec60452d142f3f58c9b1", "status": "failed", "time": {"start": 1754449442920, "stop": 1754449475136, "duration": 32216}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试memory cleanup能正常执行", "uid": "2ce3a0f3ed9c5dd1", "parentUid": "2b4278c79623ec60452d142f3f58c9b1", "status": "failed", "time": {"start": 1754449821447, "stop": 1754449849617, "duration": 28170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start running能正常执行", "uid": "56d6727ada4cc121", "parentUid": "2b4278c79623ec60452d142f3f58c9b1", "status": "failed", "time": {"start": 1754455185080, "stop": 1754455200076, "duration": 14996}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2b4278c79623ec60452d142f3f58c9b1"}, {"name": "AssertionError: 响应未包含期望内容: ['Bluetooth is turned off now']\nassert False", "children": [{"name": "测试close bluetooth能正常执行", "uid": "acc50f49b95172e6", "parentUid": "06fe8589d700bf98893919e92f835fa3", "status": "failed", "time": {"start": 1754449489597, "stop": 1754449503246, "duration": 13649}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "06fe8589d700bf98893919e92f835fa3"}, {"name": "AssertionError: 响应未包含期望内容: ['Flashlight is turned off now']\nassert False", "children": [{"name": "测试close flashlight能正常执行", "uid": "93ef6d13c079fd1", "parentUid": "dd828ba6d1527c361dd81ff9c32f4ff3", "status": "failed", "time": {"start": 1754449517677, "stop": 1754449532009, "duration": 14332}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dd828ba6d1527c361dd81ff9c32f4ff3"}, {"name": "AssertionError: 响应未包含期望内容: ['Wi-<PERSON> is turned off now']\nassert False", "children": [{"name": "测试close wifi能正常执行", "uid": "fbeb0a397c34b803", "parentUid": "0dd57032afb82675e4a0e71dbb21f186", "status": "failed", "time": {"start": 1754449546506, "stop": 1754449559659, "duration": 13153}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0dd57032afb82675e4a0e71dbb21f186"}, {"name": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "8712b4ac10ccc2e7", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754449574015, "stop": 1754449589731, "duration": 15716}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to default mode能正常执行", "uid": "d95aec3991ba9c49", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754450528080, "stop": 1754450550941, "duration": 22861}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to flash notification能正常执行", "uid": "70607ed82c3ddd8b", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754450593240, "stop": 1754450616621, "duration": 23381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "199c7c5fd883ed42", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754451590134, "stop": 1754451604950, "duration": 14816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Add the images and text on the screen to the note", "uid": "d691924f9e325c36", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754451733535, "stop": 1754451747369, "duration": 13834}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试extend the image能正常执行", "uid": "f022af5522bf7597", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754452877312, "stop": 1754452890938, "duration": 13626}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "a427bf5606042741", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754453599545, "stop": 1754453614460, "duration": 14915}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open whatsapp", "uid": "9fe241edfc82795", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754453906592, "stop": 1754453921460, "duration": 14868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试search whatsapp for me能正常执行", "uid": "c35c0f8a3add0d33", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754454086588, "stop": 1754454104322, "duration": 17734}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start walking能正常执行", "uid": "d66688f8f824d308", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1754455213655, "stop": 1754455228605, "duration": 14950}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a710013a126f24211d2305c197d8623"}, {"name": "AssertionError: 响应未包含期望内容: ['Brightness goes down']\nassert False", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "a9782300c788ad8c", "parentUid": "f81e48840f2cfd5eef5902dcdd149838", "status": "failed", "time": {"start": 1754449604272, "stop": 1754449618034, "duration": 13762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f81e48840f2cfd5eef5902dcdd149838"}, {"name": "AssertionError: 响应未包含期望内容: ['Screen recording started']\nassert False", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "b3bd4eefa66cd793", "parentUid": "bfbac5c98459556de47ee1e7c1ef16ee", "status": "failed", "time": {"start": 1754449632803, "stop": 1754449651634, "duration": 18831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfbac5c98459556de47ee1e7c1ef16ee"}, {"name": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "children": [{"name": "stop  screen recording能正常执行", "uid": "f1b11a5e7a615977", "parentUid": "966a0d2ab4db423786585db8d71e7ace", "status": "failed", "time": {"start": 1754449666049, "stop": 1754449684411, "duration": 18362}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "966a0d2ab4db423786585db8d71e7ace"}, {"name": "AssertionError: 文件不存在！\nassert False", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "7595375f57ce60c8", "parentUid": "b16df34f724c90a3340ae49c743c6a99", "status": "failed", "time": {"start": 1754449699015, "stop": 1754449717676, "duration": 18661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b16df34f724c90a3340ae49c743c6a99"}, {"name": "AssertionError: 响应未包含期望内容: ['Media volume has been set to the maximum']\nassert False", "children": [{"name": "测试maximum volume能正常执行", "uid": "8b9d6241d0992877", "parentUid": "286d5ac7f4cbd883f42a3722fa241b7b", "status": "failed", "time": {"start": 1754449793578, "stop": 1754449806906, "duration": 13328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "286d5ac7f4cbd883f42a3722fa241b7b"}, {"name": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "children": [{"name": "测试smart charge能正常执行", "uid": "409d5e47442fd23e", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754450139671, "stop": 1754450152968, "duration": 13297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch charging modes能正常执行", "uid": "afe93fd71bbebe1b", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754450417620, "stop": 1754450431165, "duration": 13545}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Hyper Charge能正常执行", "uid": "cddd64f0dce015e5", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754450630749, "stop": 1754450644126, "duration": 13377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "9c90db8fe9592caf", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754450658321, "stop": 1754450671918, "duration": 13597}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to smart charge能正常执行", "uid": "a1ae04a8c91793c9", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1754450715220, "stop": 1754450728748, "duration": 13528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "40c8e511bd59d1b6469cd14c6e42d010"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B372250>.wait_for_page_load", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "6513ea7d676f9aee", "parentUid": "5e32c0d86c05b7f8c7c4bb28458c53e2", "status": "failed", "time": {"start": 1754451748972, "stop": 1754451748972, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5e32c0d86c05b7f8c7c4bb28458c53e2"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B490B90>.wait_for_page_load", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "cc4b26a4d2f8fb86", "parentUid": "e5c187c354f4ba6b65b1141184912f20", "status": "failed", "time": {"start": 1754451783661, "stop": 1754451783661, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e5c187c354f4ba6b65b1141184912f20"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B2F30D0>.wait_for_page_load", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "f418a21c3f8bcc0d", "parentUid": "2e33da9f8a92f8a8fa4ea0230de37d88", "status": "failed", "time": {"start": 1754451818320, "stop": 1754451818320, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2e33da9f8a92f8a8fa4ea0230de37d88"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B5EDA90>.wait_for_page_load", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "b6f84103b65be0e9", "parentUid": "343dd505bb17c65f6ba6afde9a84f225", "status": "failed", "time": {"start": 1754451853335, "stop": 1754451853335, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "343dd505bb17c65f6ba6afde9a84f225"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B5E48D0>.wait_for_page_load", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "aff9de2e497af703", "parentUid": "17deeffcd806e022dc141bd8b81a684e", "status": "failed", "time": {"start": 1754451888447, "stop": 1754451888447, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "17deeffcd806e022dc141bd8b81a684e"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B62E450>.wait_for_page_load", "children": [{"name": "测试check my to-do list能正常执行", "uid": "cb9bf1bed63324f0", "parentUid": "b833bc084e0cdefd14f9160171e177e0", "status": "failed", "time": {"start": 1754451923380, "stop": 1754451923380, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b833bc084e0cdefd14f9160171e177e0"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B655110>.wait_for_page_load", "children": [{"name": "测试check rear camera information能正常执行", "uid": "1c1269d5fcc8834f", "parentUid": "185b2013eefa4545924370b5ef4200d8", "status": "failed", "time": {"start": 1754451958469, "stop": 1754451958469, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "185b2013eefa4545924370b5ef4200d8"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E389C42CD0>.wait_for_page_load", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "42624ab66ae39dc7", "parentUid": "f469a3a66da24a76e857f1e29c8ebd5d", "status": "failed", "time": {"start": 1754451993567, "stop": 1754451993567, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f469a3a66da24a76e857f1e29c8ebd5d"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6BA090>.wait_for_page_load", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "e27150fcafd03433", "parentUid": "78309b96a64928c152bafb608be83f06", "status": "failed", "time": {"start": 1754452028631, "stop": 1754452028631, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "78309b96a64928c152bafb608be83f06"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6AF210>.wait_for_page_load", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "c298b828e0b31f92", "parentUid": "a8f552a19bd49f23947274644642bb37", "status": "failed", "time": {"start": 1754452063624, "stop": 1754452063624, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a8f552a19bd49f23947274644642bb37"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B692F50>.wait_for_page_load", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "2e9c0e779971b6f2", "parentUid": "077eb682ee22b8c068bf33eedf6e0a19", "status": "failed", "time": {"start": 1754452098668, "stop": 1754452098668, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "077eb682ee22b8c068bf33eedf6e0a19"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E3894F0D50>.wait_for_page_load", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "70071cedc08d13cb", "parentUid": "2c114ee00d15a51704dd628bd4c27ec1", "status": "failed", "time": {"start": 1754452133677, "stop": 1754452133677, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2c114ee00d15a51704dd628bd4c27ec1"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B690C10>.wait_for_page_load", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "740c63b3a902e9b8", "parentUid": "2a4ba54c3ec5e8adbb3cad78023b9dc7", "status": "failed", "time": {"start": 1754452168780, "stop": 1754452168780, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a4ba54c3ec5e8adbb3cad78023b9dc7"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B594090>.wait_for_page_load", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "36e36fc2e197126e", "parentUid": "9d5ff0e995aa6847f874aa0aa3e1c71a", "status": "failed", "time": {"start": 1754452203747, "stop": 1754452203747, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d5ff0e995aa6847f874aa0aa3e1c71a"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B52D3D0>.wait_for_page_load", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "fc814aaf55d3b79b", "parentUid": "5f0b12093e94e0e963f39f792da124cf", "status": "failed", "time": {"start": 1754452238952, "stop": 1754452238952, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5f0b12093e94e0e963f39f792da124cf"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B24FBD0>.wait_for_page_load", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "f9695098892ae325", "parentUid": "aa50283d17c4545f6de08c7ef6cb1455", "status": "failed", "time": {"start": 1754452274094, "stop": 1754452274094, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "aa50283d17c4545f6de08c7ef6cb1455"}, {"name": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B31BB10>.wait_for_page_load", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "de97c82562aa7b51", "parentUid": "d6b3caba2099c70e41dbb013c6636308", "status": "failed", "time": {"start": 1754452308838, "stop": 1754452308838, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d6b3caba2099c70e41dbb013c6636308"}, {"name": "AssertionError: 响应未包含期望内容: [\"Sorry, I can't help with that\"]\nassert False", "children": [{"name": "测试fly to the moon返回正确的不支持响应", "uid": "ae09c15284e87680", "parentUid": "eb89e599f40d2e9b82a006afcf847d87", "status": "failed", "time": {"start": 1754452904530, "stop": 1754452932582, "duration": 28052}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb89e599f40d2e9b82a006afcf847d87"}, {"name": "AssertionError: 响应未包含期望内容: [\"I'm glad to open Youtube Music for you\", 'You need to select music to play.']\nassert False", "children": [{"name": "测试play love sotry", "uid": "142906c3e59f6be2", "parentUid": "33f9b6823802f19bfbede7570868c210", "status": "failed", "time": {"start": 1754453740436, "stop": 1754453761597, "duration": 21161}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play taylor swift‘s song love story", "uid": "21e4c9e08559b2f2", "parentUid": "33f9b6823802f19bfbede7570868c210", "status": "failed", "time": {"start": 1754453775856, "stop": 1754453797346, "duration": 21490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play the album", "uid": "1787807a0d09bf93", "parentUid": "33f9b6823802f19bfbede7570868c210", "status": "failed", "time": {"start": 1754453811752, "stop": 1754453833090, "duration": 21338}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33f9b6823802f19bfbede7570868c210"}, {"name": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', 'No call record', '', '']'\nassert False", "children": [{"name": "测试redial", "uid": "ef7a049e326a2e58", "parentUid": "01bcb1980db63d9b59ac80b27cea42b1", "status": "failed", "time": {"start": 1754453935890, "stop": 1754453958417, "duration": 22527}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "01bcb1980db63d9b59ac80b27cea42b1"}, {"name": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', \"12:20 Dialogue Explore <PERSON><PERSON><PERSON> at UFC 108 What is Ask About Screen? <PERSON> Backs <PERSON>'s Ad, Critiques Swift Search for addresses on the screen I am sorry, I am unable to search for addresses on the screen. Generated by AI, for reference only Artemis program timeline SpaceX lunar missions Moon mission costs analysis DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert None", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "aec6e1a96ee9d4f5", "parentUid": "978b2c3f0bb5bd3b5d33c0cbbf1939de", "status": "failed", "time": {"start": 1754454028008, "stop": 1754454043073, "duration": 15065}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "978b2c3f0bb5bd3b5d33c0cbbf1939de"}, {"name": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', \"Dialogue Explore Refresh What is Ask About Screen? De<PERSON><PERSON>'s $228M Spurs Deal Storm Floris: Plane's 145 KM/H Landing search the address in the image I am sorry, I am unable to search for the address in the image. Generated by AI, for reference only Moon mission costs Artemis program timeline SpaceX lunar plans DeepSeek-R1 Feel free to ask me any questions… 12:21\"]'\nassert None", "children": [{"name": "测试search the address in the image能正常执行", "uid": "2df96fbe56ee275e", "parentUid": "906025a0ab469997652731d8f3049391", "status": "failed", "time": {"start": 1754454057296, "stop": 1754454072495, "duration": 15199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "906025a0ab469997652731d8f3049391"}, {"name": "AssertionError: 响应未包含期望内容: [\"Oops, out of my reach, ask me again after I've learned it\"]\nassert False", "children": [{"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "537462e9458a1a8a", "parentUid": "b2d645b75ac8b18e7fd523b9768a002d", "status": "failed", "time": {"start": 1754454118397, "stop": 1754454134176, "duration": 15779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2d645b75ac8b18e7fd523b9768a002d"}, {"name": "AssertionError: 响应未包含期望内容: ['Please tell me the name or number to call.']\nassert False", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "d8b9051cd846e87c", "parentUid": "7ad888348d60b1970761d14c96264acf", "status": "failed", "time": {"start": 1754455518029, "stop": 1754455540486, "duration": 22457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7ad888348d60b1970761d14c96264acf"}, {"name": "AssertionError: 响应未包含期望内容: [\"Sorry, I couldn't locate the setting\"]\nassert False", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "7f98b929b3063854", "parentUid": "8ff07dba203999edda2225da8d5e791d", "status": "failed", "time": {"start": 1754455553984, "stop": 1754455569893, "duration": 15909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8ff07dba203999edda2225da8d5e791d"}], "uid": "8fb3a91ba5aaf9de24cc8a92edc82b5d"}, {"name": "Test defects", "children": [{"name": "TypeError: a bytes-like object is required, not 'dict'", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "cc30defe424c2046", "parentUid": "018bca9f4d9ac8e9d50a0bff138a0cdf", "status": "broken", "time": {"start": 1754447591529, "stop": 1754447607164, "duration": 15635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "018bca9f4d9ac8e9d50a0bff138a0cdf"}, {"name": "ValueError: too many values to unpack (expected 3)", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "1cbe1f329bda06d2", "parentUid": "8e6c858299af90aba28521bd81ad3b69", "status": "broken", "time": {"start": 1754448465615, "stop": 1754448480470, "duration": 14855}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试whatsapp能正常执行", "uid": "aaf2e2359901cf45", "parentUid": "8e6c858299af90aba28521bd81ad3b69", "status": "broken", "time": {"start": 1754451704338, "stop": 1754451719424, "duration": 15086}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the first address in the image能正常执行", "uid": "b7f1fa9085faaa27", "parentUid": "8e6c858299af90aba28521bd81ad3b69", "status": "broken", "time": {"start": 1754453512052, "stop": 1754453526933, "duration": 14881}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8e6c858299af90aba28521bd81ad3b69"}], "uid": "bdbf199525818fae7a8651db9eafe741"}]}