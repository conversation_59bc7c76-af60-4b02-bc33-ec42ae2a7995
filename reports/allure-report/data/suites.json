{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "testcases.test_ella.component_coupling", "children": [{"name": "test_close_aivana", "children": [{"name": "TestEllaCloseAivana", "children": [{"name": "测试close aivana能正常执行", "uid": "465733cfe6bd0a7f", "parentUid": "dc766e0abf993d1226d15b68c77f4f69", "status": "passed", "time": {"start": 1754446474549, "stop": 1754446508948, "duration": 34399}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dc766e0abf993d1226d15b68c77f4f69"}], "uid": "2326186ebd37334731865ec8b5679891"}, {"name": "test_close_ella", "children": [{"name": "TestEllaClose<PERSON>lla", "children": [{"name": "测试close ella能正常执行", "uid": "2d3253e0c7e1606d", "parentUid": "cec0309765c155f4f8740ed45c580c80", "status": "passed", "time": {"start": 1754446522764, "stop": 1754446555185, "duration": 32421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cec0309765c155f4f8740ed45c580c80"}], "uid": "989d936277ca7c8b51dc874f7afac7c1"}, {"name": "test_close_folax", "children": [{"name": "TestEllaCloseFolax", "children": [{"name": "测试close folax能正常执行", "uid": "9fdf4fc0d91493b8", "parentUid": "7ae1e31d59d51e63408ae29d2c8c926e", "status": "passed", "time": {"start": 1754446569173, "stop": 1754446601540, "duration": 32367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7ae1e31d59d51e63408ae29d2c8c926e"}], "uid": "d7b55da797c782b6c2cc729406440a10"}, {"name": "test_close_phonemaster", "children": [{"name": "TestEllaClosePhonemaster", "children": [{"name": "测试close phonemaster能正常执行", "uid": "d9283c555a0dfffd", "parentUid": "976102e81bb88d3807a1e25c3ddeca37", "status": "passed", "time": {"start": 1754446615376, "stop": 1754446629166, "duration": 13790}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "976102e81bb88d3807a1e25c3ddeca37"}], "uid": "e1a42dca75ede54847ef42fc018b38d7"}, {"name": "test_continue_music", "children": [{"name": "TestEllaContinueMusic", "children": [{"name": "测试continue music能正常执行", "uid": "1a47f528b9b87329", "parentUid": "1006aa648a1d87005804dfc1cb937d40", "status": "passed", "time": {"start": 1754446642140, "stop": 1754446655684, "duration": 13544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1006aa648a1d87005804dfc1cb937d40"}], "uid": "091d4456268247868ef8ba9cc951a6e9"}, {"name": "test_create_a_metting_schedule_at_tomorrow", "children": [{"name": "TestEllaCreateMettingScheduleTomorrow", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "cbf0dcc5d9990cc2", "parentUid": "2723183c861afd907653f50d6bfc622c", "status": "failed", "time": {"start": 1754446668758, "stop": 1754446684884, "duration": 16126}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2723183c861afd907653f50d6bfc622c"}], "uid": "e4b3f0da2dd18af340a32e173acf4400"}, {"name": "test_delete_the_8_o_clock_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "d8177e2366c3da89", "parentUid": "75d733f64143d69107fc406cc5a4bb5d", "status": "passed", "time": {"start": 1754446698704, "stop": 1754446716817, "duration": 18113}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "75d733f64143d69107fc406cc5a4bb5d"}], "uid": "5ac49ae4fb77ab653eda0b303f9fd551"}, {"name": "test_display_the_route_go_company", "children": [{"name": "TestEllaOpenMaps", "children": [{"name": "测试display the route go company", "uid": "92ee4d46312fe03", "parentUid": "fae47b3031d4ccce961b95f3d652edcf", "status": "failed", "time": {"start": 1754446729951, "stop": 1754446745234, "duration": 15283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fae47b3031d4ccce961b95f3d652edcf"}], "uid": "a2b2fbfd344f33b51bd68e5977e57dae"}, {"name": "test_my_phone_is_too_slow", "children": [{"name": "TestEllaMyPhoneIsTooSlow", "children": [{"name": "测试my phone is too slow能正常执行", "uid": "c08f5d98556cacbd", "parentUid": "a0a6853a187713f8a5ac0e869d619853", "status": "passed", "time": {"start": 1754446758280, "stop": 1754446772851, "duration": 14571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0a6853a187713f8a5ac0e869d619853"}], "uid": "1a3ac4d4348e23076891e9f62ee93107"}, {"name": "test_next_channel", "children": [{"name": "TestEllaNextChannel", "children": [{"name": "测试next channel能正常执行", "uid": "7a434d777ce8aa77", "parentUid": "79fe0adb48b7a7e540c509bbc071945d", "status": "passed", "time": {"start": 1754446786785, "stop": 1754446800286, "duration": 13501}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79fe0adb48b7a7e540c509bbc071945d"}], "uid": "bf525e696763b8e3f2fb093dc5260aa2"}, {"name": "test_open_camera", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open camera能正常执行", "uid": "c80bc96ce82f076e", "parentUid": "e03396d1fe1c4deef0c8cf91f821a468", "status": "passed", "time": {"start": 1754446813335, "stop": 1754446831354, "duration": 18019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e03396d1fe1c4deef0c8cf91f821a468"}], "uid": "92e88ab08159dfaf49a78312925760b7"}, {"name": "test_open_clock", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "open clock", "uid": "1f050226c3e963c2", "parentUid": "b2210d96f4ac8230585a75115cd1d8c2", "status": "passed", "time": {"start": 1754446845200, "stop": 1754446865875, "duration": 20675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2210d96f4ac8230585a75115cd1d8c2"}], "uid": "d636d9848e1c7419e6f84e7e49c07171"}, {"name": "test_open_contact", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "650adc0ae13a6b5f", "parentUid": "39d05c6acdceec56c3c12fd23896f56f", "status": "passed", "time": {"start": 1754446879576, "stop": 1754446902465, "duration": 22889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "39d05c6acdceec56c3c12fd23896f56f"}], "uid": "cd82497cee692a7c9a65a6184e27c221"}, {"name": "test_open_countdown", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open countdown能正常执行", "uid": "c2dada2a8cd202f3", "parentUid": "e10c6770504fedd71bbf3e383f94dc48", "status": "failed", "time": {"start": 1754446916540, "stop": 1754446930020, "duration": 13480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e10c6770504fedd71bbf3e383f94dc48"}], "uid": "f0d78da692838ddb314b12cc4febc20b"}, {"name": "test_open_dialer", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open dialer能正常执行", "uid": "17b298548e69b1bf", "parentUid": "068eb91b79bee6accc0bcab4a6367e76", "status": "passed", "time": {"start": 1754446944051, "stop": 1754446966739, "duration": 22688}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "068eb91b79bee6accc0bcab4a6367e76"}], "uid": "3c3dd50d3fd335b243796d4c0c9bfe64"}, {"name": "test_open_ella", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "c305a7aff70443", "parentUid": "907f87c6914ea345d20478681f484e8d", "status": "passed", "time": {"start": 1754446980560, "stop": 1754446993024, "duration": 12464}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "907f87c6914ea345d20478681f484e8d"}], "uid": "6b30479a0405c27782687611167a8e3d"}, {"name": "test_open_folax", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open folax能正常执行", "uid": "2333c514af23b85d", "parentUid": "031c2cd337cd7647fd7b60d0d75ba16e", "status": "passed", "time": {"start": 1754447006883, "stop": 1754447019377, "duration": 12494}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "031c2cd337cd7647fd7b60d0d75ba16e"}], "uid": "fb1e65e2e60ee7c63b5831633cb5005c"}, {"name": "test_open_phone", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "2c63a150dba92152", "parentUid": "147ffaef9b3cdea572ba5e4caea69dbe", "status": "passed", "time": {"start": 1754447033146, "stop": 1754447056127, "duration": 22981}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "147ffaef9b3cdea572ba5e4caea69dbe"}], "uid": "75ded0b626adc3c7c8dbefcba1c0b5f0"}, {"name": "test_pause_fm", "children": [{"name": "TestEllaPauseFm", "children": [{"name": "测试pause fm能正常执行", "uid": "db0ee351a160247a", "parentUid": "88ca4f005094f55c66457c720f6cbb47", "status": "passed", "time": {"start": 1754447070116, "stop": 1754447083848, "duration": 13732}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "88ca4f005094f55c66457c720f6cbb47"}], "uid": "d9c43a396815c7e853000c20e47b8fa8"}, {"name": "test_pause_music", "children": [{"name": "TestEllaPauseMusic", "children": [{"name": "测试pause music能正常执行", "uid": "f2f0addf6616c69", "parentUid": "1f79feb59e2eb0bf0fb7e1387903bfee", "status": "passed", "time": {"start": 1754447097680, "stop": 1754447111256, "duration": 13576}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1f79feb59e2eb0bf0fb7e1387903bfee"}], "uid": "8a3b16d13b6ccc8492638853c9ba04aa"}, {"name": "test_play_afro_strut", "children": [{"name": "TestEllaOpenPlayAfroStrut", "children": [{"name": "测试play afro strut", "uid": "8db2598fd16f9037", "parentUid": "9988b4042f5d7026e75fc8fcb98f5519", "status": "passed", "time": {"start": 1754447125376, "stop": 1754447154145, "duration": 28769}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9988b4042f5d7026e75fc8fcb98f5519"}], "uid": "3aef4f1ab476b7c11ee7ec75e072c264"}, {"name": "test_play_jay_chou_s_music", "children": [{"name": "TestEllaOpenMusic", "children": [{"name": "测试play jay chou's music", "uid": "46a3ccf5a87f0c13", "parentUid": "a2f6d87f760b2cc31c5c6aad0f136dd1", "status": "passed", "time": {"start": 1754447167871, "stop": 1754447191593, "duration": 23722}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a2f6d87f760b2cc31c5c6aad0f136dd1"}], "uid": "5ac2a1940b596719fc1fe3160a450962"}, {"name": "test_play_jay_chou_s_music_by_spotify", "children": [{"name": "TestEllaOpenMusic", "children": [{"name": "测试play jay chou's music by spotify", "uid": "538bea4536324700", "parentUid": "c8aebf1f28528d18214bd1a9e19bfca7", "status": "failed", "time": {"start": 1754447205460, "stop": 1754447221759, "duration": 16299}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c8aebf1f28528d18214bd1a9e19bfca7"}], "uid": "4c3a9736ba488ccfdfd13dbb9608ebde"}, {"name": "test_play_music", "children": [{"name": "TestEllaOpen<PERSON>a", "children": [{"name": "测试play music", "uid": "8a51f504d5b44159", "parentUid": "de473794f9027034dfc0e767cb0a1fc8", "status": "passed", "time": {"start": 1754447235725, "stop": 1754447258400, "duration": 22675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de473794f9027034dfc0e767cb0a1fc8"}], "uid": "755e0b08ce2f0d843df6c102fcc9d49b"}, {"name": "test_play_rock_music", "children": [{"name": "TestEllaOpen<PERSON>a", "children": [{"name": "测试play rock music", "uid": "b9b7e92debdfb117", "parentUid": "b0f90c45c8c8e64fb5c2a98f923bc6fa", "status": "passed", "time": {"start": 1754447272254, "stop": 1754447294725, "duration": 22471}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0f90c45c8c8e64fb5c2a98f923bc6fa"}], "uid": "735fc1adab6496548b5a97d70b865131"}, {"name": "test_play_sun_be_song_of_jide_chord", "children": [{"name": "TestEllaOpenPlaySunBeSongOfJideChord", "children": [{"name": "测试play sun be song of jide chord", "uid": "4722e4f5730d45cb", "parentUid": "5c987d2d0d756e70bc56eda09e92e854", "status": "passed", "time": {"start": 1754447308540, "stop": 1754447331031, "duration": 22491}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5c987d2d0d756e70bc56eda09e92e854"}], "uid": "020b88bee0045afa929d84639829645d"}, {"name": "test_record_audio_for_seconds", "children": [{"name": "TestEllaRecordAudioSeconds", "children": [{"name": "测试record audio for 5 seconds能正常执行", "uid": "b21a04cbb6917b66", "parentUid": "964e536f897371e5579180ce760d9610", "status": "failed", "time": {"start": 1754447344785, "stop": 1754447359784, "duration": 14999}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "964e536f897371e5579180ce760d9610"}], "uid": "b622f700f99c575c72cb1944c3e3c02e"}, {"name": "test_resume_music", "children": [{"name": "TestEllaResumeMusic", "children": [{"name": "测试resume music能正常执行", "uid": "d5a5e14540c4d424", "parentUid": "cd0e4afc91d2a02372d847be235bede4", "status": "passed", "time": {"start": 1754447374047, "stop": 1754447387656, "duration": 13609}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "cd0e4afc91d2a02372d847be235bede4"}], "uid": "b281dddb4da708a18c1bdad0e1402ec1"}, {"name": "test_set_an_alarm_at_8_am", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set an alarm at 8 am", "uid": "62928b601638239b", "parentUid": "33bfbdda545b50afa47fc22456e93557", "status": "passed", "time": {"start": 1754447401605, "stop": 1754447416889, "duration": 15284}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33bfbdda545b50afa47fc22456e93557"}], "uid": "a3607188923688aad3a2ecec54f9a7c1"}, {"name": "test_start_run", "children": [{"name": "TestEllaStartRun", "children": [{"name": "测试start run能正常执行", "uid": "32a5aa19059db7ec", "parentUid": "bdc41cc2423781a1d612f77309dd2eda", "status": "failed", "time": {"start": 1754447430794, "stop": 1754447456077, "duration": 25283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdc41cc2423781a1d612f77309dd2eda"}], "uid": "cbf2197081de1ab36182bedc656b8192"}, {"name": "test_stop_playing", "children": [{"name": "TestEllaOpenYoutube", "children": [{"name": "测试stop playing", "uid": "1ddf4917c0fa3e0f", "parentUid": "04a0cd56790b683054ed72365f59d2e7", "status": "passed", "time": {"start": 1754447470350, "stop": 1754447485362, "duration": 15012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "04a0cd56790b683054ed72365f59d2e7"}], "uid": "8960b75623e553fca190959e7f507f14"}, {"name": "test_take_a_screenshot", "children": [{"name": "TestEllaTakeScreenshot", "children": [{"name": "测试take a screenshot能正常执行", "uid": "6443b855f11fc58", "parentUid": "ac5545075b0e4292eed508b41e08ab8c", "status": "passed", "time": {"start": 1754447499429, "stop": 1754447514675, "duration": 15246}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ac5545075b0e4292eed508b41e08ab8c"}], "uid": "0daa64e90efed9ce03db463cca6bb5b1"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "93130e85f011449", "parentUid": "de146ab7aca490ae6653437938175de4", "status": "passed", "time": {"start": 1754447528618, "stop": 1754447549421, "duration": 20803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de146ab7aca490ae6653437938175de4"}], "uid": "4a586ea49df80d8628d524f64c639079"}], "uid": "5948c7c27387d214d4b5e1b876d4cb27"}, {"name": "testcases.test_ella.dialogue", "children": [{"name": "test_appeler_maman", "children": [{"name": "Test<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试appeler maman能正常执行", "uid": "a99c73fcc462928a", "parentUid": "f8fcd4a1c0a83d1c3db334295a60b72e", "status": "passed", "time": {"start": 1754447563860, "stop": 1754447577353, "duration": 13493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8fcd4a1c0a83d1c3db334295a60b72e"}], "uid": "97ecf501f8b24237ae07b2178fff9b3f"}, {"name": "test_book_a_flight_to_paris", "children": [{"name": "TestEllaBookFlightParis", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "cc30defe424c2046", "parentUid": "d9246a10b1546e33724912523509ec95", "status": "broken", "time": {"start": 1754447591529, "stop": 1754447607164, "duration": 15635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9246a10b1546e33724912523509ec95"}], "uid": "09531ecb34a029327b70c5fe7633b7f3"}, {"name": "test_call_mom_through_whatsapp", "children": [{"name": "TestEllaCallMomThroughWhatsapp", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "f7063b5f8370255c", "parentUid": "b2bb86980dd84d36c5c8df2b8200871f", "status": "failed", "time": {"start": 1754447622142, "stop": 1754447645193, "duration": 23051}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2bb86980dd84d36c5c8df2b8200871f"}], "uid": "b093744c9f16724e1a2928dcb99b362b"}, {"name": "test_can_you_give_me_a_coin", "children": [{"name": "TestEllaCanYouGiveMeCoin", "children": [{"name": "测试can you give me a coin能正常执行", "uid": "d8d80c1a8a7aa2ba", "parentUid": "4c5d38b6a82090208170044db256ae60", "status": "passed", "time": {"start": 1754447660529, "stop": 1754447677746, "duration": 17217}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4c5d38b6a82090208170044db256ae60"}], "uid": "804d60782c903cc7f780a62383e53a73"}, {"name": "test_cannot_login_in_google_email_box", "children": [{"name": "TestEllaCannotLoginGoogleEmailBox", "children": [{"name": "测试cannot login in google email box能正常执行", "uid": "2eba593f4cb7e56a", "parentUid": "9d85ac6932670633773061ce5b33ae53", "status": "passed", "time": {"start": 1754447692028, "stop": 1754447706712, "duration": 14684}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d85ac6932670633773061ce5b33ae53"}], "uid": "4a7c1215b1a8ca8a7a3c6f82e99131c2"}, {"name": "test_check_status_updates_on_whatsapp", "children": [{"name": "TestEllaCheckStatusUpdatesWhatsapp", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "548281780cb22e82", "parentUid": "8e354564384b35c7b587be597ea49fad", "status": "failed", "time": {"start": 1754447721944, "stop": 1754447736646, "duration": 14702}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8e354564384b35c7b587be597ea49fad"}], "uid": "5ecd3e5cb8ea680501b5eea40dc4f984"}, {"name": "test_close_whatsapp", "children": [{"name": "TestEllaCloseWhatsapp", "children": [{"name": "测试close whatsapp能正常执行", "uid": "9583fdfb28ae5b4d", "parentUid": "c32b137a79c22ae5d9f6dcb3c7a455e4", "status": "passed", "time": {"start": 1754447750854, "stop": 1754447765708, "duration": 14854}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c32b137a79c22ae5d9f6dcb3c7a455e4"}], "uid": "9ab207a1a50592cfc97f93c48ff061e9"}, {"name": "test_could_you_please_search_an_for_me", "children": [{"name": "TestEllaCouldYouPleaseSearchAnMe", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "4a731551f351aafd", "parentUid": "05954a2ceae7fd66d1757bbd59b47ed6", "status": "failed", "time": {"start": 1754447779841, "stop": 1754447794688, "duration": 14847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05954a2ceae7fd66d1757bbd59b47ed6"}], "uid": "18eeff7a50c6893d6191e5b7834fb912"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "80279af86354810e", "parentUid": "130a6b251dee0788fb7cea69ff620c68", "status": "passed", "time": {"start": 1754447808985, "stop": 1754447822724, "duration": 13739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "130a6b251dee0788fb7cea69ff620c68"}], "uid": "d6678d1bee0dacb44840d09dee8fdbd2"}, {"name": "test_give_me_some_money", "children": [{"name": "TestEllaGiveMeSomeMoney", "children": [{"name": "测试give me some money能正常执行", "uid": "f137a3f44d2dcec2", "parentUid": "112c6b7b50b46672fc16704c6fc8c8ea", "status": "passed", "time": {"start": 1754447836770, "stop": 1754447853314, "duration": 16544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "112c6b7b50b46672fc16704c6fc8c8ea"}], "uid": "cef13fbaa76291ab7ffe428fd9b3ff0a"}, {"name": "test_global_gdp_trends", "children": [{"name": "TestEllaGlobalGdpTrends", "children": [{"name": "测试global gdp trends能正常执行", "uid": "46ba013c32f0a867", "parentUid": "831198a93942962c147b23029d2ab9c8", "status": "passed", "time": {"start": 1754447867234, "stop": 1754447884006, "duration": 16772}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "831198a93942962c147b23029d2ab9c8"}], "uid": "fff3da9e7aaa932288c845a77dbb22ae"}, {"name": "test_hello_hello", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试hello hello能正常执行", "uid": "2d413365c9e8c32f", "parentUid": "ba652584bc7b97ea54da02ced4645ba5", "status": "passed", "time": {"start": 1754447898223, "stop": 1754447913407, "duration": 15184}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ba652584bc7b97ea54da02ced4645ba5"}], "uid": "ce7aabb9954dd4026695310de1d225b1"}, {"name": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "children": [{"name": "TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit", "children": [{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "6a31892470e25970", "parentUid": "f60519e8aeef426bc3a93755c572d6fa", "status": "passed", "time": {"start": 1754447927479, "stop": 1754447941296, "duration": 13817}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f60519e8aeef426bc3a93755c572d6fa"}], "uid": "e821be54d27a42f20fe55cd5d6203630"}, {"name": "test_hi", "children": [{"name": "TestEllaHi", "children": [{"name": "测试hi能正常执行", "uid": "2cd8741868006483", "parentUid": "1223273ba8f6a08b5ed65920246c9e1b", "status": "passed", "time": {"start": 1754447955355, "stop": 1754447970221, "duration": 14866}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1223273ba8f6a08b5ed65920246c9e1b"}], "uid": "29e62e7f369af137801d22a9b259e4ec"}, {"name": "test_how_is_the_weather_today", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试how is the weather today能正常执行", "uid": "ca70ed4533183a70", "parentUid": "34ffa36d6317bc520ea4d2709c2ddf11", "status": "passed", "time": {"start": 1754447984022, "stop": 1754448004637, "duration": 20615}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34ffa36d6317bc520ea4d2709c2ddf11"}], "uid": "9cf284f12ed28b2464e982b240e1a734"}, {"name": "test_how_is_the_wheather_today", "children": [{"name": "TestEllaHowIsWheatherToday", "children": [{"name": "测试how is the wheather today能正常执行", "uid": "fedcf4300a6c2948", "parentUid": "d29828ee15804f262c19aa832269e66a", "status": "passed", "time": {"start": 1754448018652, "stop": 1754448032473, "duration": 13821}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d29828ee15804f262c19aa832269e66a"}], "uid": "23a13ba389e295288e7459ad4f05d4d2"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "TestEllaHowSWeatherToday", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "f8d2ce2b39404252", "parentUid": "862e57348213f2baefa0f1dca30e6cac", "status": "passed", "time": {"start": 1754448046663, "stop": 1754448067498, "duration": 20835}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "862e57348213f2baefa0f1dca30e6cac"}], "uid": "e9f1cfec1579f5ee85e18450ed232a94"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试how's the weather today in shanghai能正常执行", "uid": "74841191efeb3379", "parentUid": "6278997b6a5cd9b65afdbc6961c1f923", "status": "passed", "time": {"start": 1754448081733, "stop": 1754448102779, "duration": 21046}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6278997b6a5cd9b65afdbc6961c1f923"}], "uid": "0de8d8ddf42930e480591e30731da5d4"}, {"name": "test_how_to_say_hello_in_french", "children": [{"name": "TestEllaHowSayHelloFrench", "children": [{"name": "测试how to say hello in french能正常执行", "uid": "97384b55cfa58ab3", "parentUid": "5667c881ac1997f398af2c0cc0dc170c", "status": "passed", "time": {"start": 1754448117033, "stop": 1754448129631, "duration": 12598}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5667c881ac1997f398af2c0cc0dc170c"}], "uid": "594bdcb49fa434818b399bc139ed191f"}, {"name": "test_how_to_say_i_love_you_in_french", "children": [{"name": "TestEllaHowSayILoveYouFrench", "children": [{"name": "测试how to say i love you in french能正常执行", "uid": "50ac8cebb18698b6", "parentUid": "9966bad905bebb015e3efc2cbe564d9a", "status": "passed", "time": {"start": 1754448143739, "stop": 1754448155894, "duration": 12155}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9966bad905bebb015e3efc2cbe564d9a"}], "uid": "403c67e2055acbe3d575b2a0bb5e3e20"}, {"name": "test_i_wanna_be_rich", "children": [{"name": "TestEllaIWannaBeRich", "children": [{"name": "测试i wanna be rich能正常执行", "uid": "63a2a142ebf8c9ee", "parentUid": "c7de20f7cd05ebdbc63478eed16102dc", "status": "passed", "time": {"start": 1754448170003, "stop": 1754448186489, "duration": 16486}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7de20f7cd05ebdbc63478eed16102dc"}], "uid": "ec547ced2ed20bbbff5a2043b9e6319c"}, {"name": "test_i_want_to_make_a_call", "children": [{"name": "TestEllaIWantMakeCall", "children": [{"name": "测试i want to make a call能正常执行", "uid": "9f342cefac9039d4", "parentUid": "503f251f10534ae24ea758298ae39419", "status": "passed", "time": {"start": 1754448200490, "stop": 1754448222977, "duration": 22487}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "503f251f10534ae24ea758298ae39419"}], "uid": "9643231b759e5225f7987ba2484e3f41"}, {"name": "test_i_want_to_watch_fireworks", "children": [{"name": "TestEllaIWantWatchFireworks", "children": [{"name": "测试i want to watch fireworks能正常执行", "uid": "c178e4faa8523e73", "parentUid": "a62423648b24979e9d41076cc7fc4719", "status": "passed", "time": {"start": 1754448236986, "stop": 1754448253582, "duration": 16596}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a62423648b24979e9d41076cc7fc4719"}], "uid": "0c36eef7e897f644d53145d2fdea584f"}, {"name": "test_introduce_yourself", "children": [{"name": "TestEllaIntroduceYourself", "children": [{"name": "测试introduce yourself能正常执行", "uid": "d7d0df09dd27c956", "parentUid": "af550fbfe44b78ae5ecace3f2e6c80de", "status": "passed", "time": {"start": 1754448267716, "stop": 1754448282495, "duration": 14779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "af550fbfe44b78ae5ecace3f2e6c80de"}], "uid": "fd2fbebbb7b47e097121b9ff95e07e74"}, {"name": "test_make_a_call", "children": [{"name": "TestEllaMakeCall", "children": [{"name": "测试make a call能正常执行", "uid": "c8046c28bc1e5fe8", "parentUid": "41b39c88fd91c3d3a8968837a65fbbfa", "status": "passed", "time": {"start": 1754448296498, "stop": 1754448319012, "duration": 22514}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41b39c88fd91c3d3a8968837a65fbbfa"}], "uid": "4c53e2e35617ddca6d4c7ed877b594a7"}, {"name": "test_open_app", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "cab4b6ad694cf9cc", "parentUid": "ef90c8fa468d5ca1174d3ecae675d779", "status": "passed", "time": {"start": 1754448332869, "stop": 1754448346643, "duration": 13774}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef90c8fa468d5ca1174d3ecae675d779"}], "uid": "13e5ce3e437fc3eba1f54c55b67226eb"}, {"name": "test_play_news", "children": [{"name": "TestEllaOpenPlayNews", "children": [{"name": "测试play news", "uid": "e14cda5e76258dcf", "parentUid": "bfe747e7270f83431a268436bcbe9627", "status": "passed", "time": {"start": 1754448360692, "stop": 1754448378327, "duration": 17635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfe747e7270f83431a268436bcbe9627"}], "uid": "f2cee0c061bba4cacfb9848756bf2374"}, {"name": "test_play_political_news", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play political news", "uid": "c9ad78195ec09a80", "parentUid": "31db74418c8cacb31ff13ffbb519e314", "status": "passed", "time": {"start": 1754448392398, "stop": 1754448422888, "duration": 30490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "31db74418c8cacb31ff13ffbb519e314"}], "uid": "f61bac7c6bed9fe60b44871a005ea908"}, {"name": "test_say_hello", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试say hello能正常执行", "uid": "d0f25b9e4d6dc09d", "parentUid": "312e23dd2efc18a08c69b14303b1e4e8", "status": "passed", "time": {"start": 1754448436849, "stop": 1754448451674, "duration": 14825}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "312e23dd2efc18a08c69b14303b1e4e8"}], "uid": "32d4a645d938aa29017eb210eb4b611b"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "TestEllaSearchingForAMethodOfViolentMurder", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "1cbe1f329bda06d2", "parentUid": "cbc32a4217884f390f94d0aa2f25e4bf", "status": "broken", "time": {"start": 1754448465615, "stop": 1754448480470, "duration": 14855}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cbc32a4217884f390f94d0aa2f25e4bf"}], "uid": "190f97aef35915433ff6090f9e1e18d1"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "16c844c54a22bc45", "parentUid": "0d050c8fb5d5c2955b5389a59f902029", "status": "failed", "time": {"start": 1754448494705, "stop": 1754448511861, "duration": 17156}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d050c8fb5d5c2955b5389a59f902029"}], "uid": "42e4c350f9cb5b725ff9a0629fd707eb"}, {"name": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking", "children": [{"name": "TestEllaShowMePremierLeaguageGoalRanking", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "c0c7a1cff2f3771e", "parentUid": "b676a8b132f27f862cb2d5f96e1e6e5b", "status": "failed", "time": {"start": 1754448526292, "stop": 1754448541648, "duration": 15356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b676a8b132f27f862cb2d5f96e1e6e5b"}], "uid": "0d9eb6607628afa8c2895d0b987c95d9"}, {"name": "test_show_scores_between_livepool_and_manchester_city", "children": [{"name": "TestEllaShowScoresBetweenLivepoolManchesterCity", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "8ee2f0917f4d02b6", "parentUid": "00794312e310e31963198853503abb78", "status": "passed", "time": {"start": 1754448555853, "stop": 1754448583869, "duration": 28016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "00794312e310e31963198853503abb78"}], "uid": "5f0fdb9ab1509aa4e4013922ca24fdb8"}, {"name": "test_stop_music", "children": [{"name": "TestEllaStopMusic", "children": [{"name": "测试stop music能正常执行", "uid": "4f13cb22f11a7ed0", "parentUid": "7b0d317bdb65f00d77c3ccf77052f972", "status": "passed", "time": {"start": 1754448597918, "stop": 1754448611893, "duration": 13975}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "7b0d317bdb65f00d77c3ccf77052f972"}], "uid": "92323b24a83f37088059a15dd2e70612"}, {"name": "test_stop_run", "children": [{"name": "TestEllaStopRun", "children": [{"name": "测试stop run能正常执行", "uid": "3a66753f7b7cbcdf", "parentUid": "c3342d9dce68675b0ffe24e49dc3ccbb", "status": "failed", "time": {"start": 1754448626044, "stop": 1754448654749, "duration": 28705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c3342d9dce68675b0ffe24e49dc3ccbb"}], "uid": "224850ea60208f7ca8a6e6ce15d4c0eb"}, {"name": "test_stop_workout", "children": [{"name": "TestEllaStopWorkout", "children": [{"name": "测试stop workout能正常执行", "uid": "1efbda1b523115da", "parentUid": "d09dcb3a4549f5f7f1d86fc4e386f4bf", "status": "failed", "time": {"start": 1754448668894, "stop": 1754448697708, "duration": 28814}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d09dcb3a4549f5f7f1d86fc4e386f4bf"}], "uid": "d886488db4cad9621ae720cb85d9a863"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "TestEllaSummarizeContentThisPage", "children": [{"name": "测试summarize content on this page能正常执行", "uid": "73152c33f6262dbc", "parentUid": "98b21f71a48feae9b99edfcfa187ba23", "status": "passed", "time": {"start": 1754448712091, "stop": 1754448726036, "duration": 13945}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98b21f71a48feae9b99edfcfa187ba23"}], "uid": "8ca3f16dcd6c82e9d2735b11b5d0cef8"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "TestEllaSummarizeWhatIMReading", "children": [{"name": "测试summarize what i'm reading能正常执行", "uid": "b3f6ff1329da975e", "parentUid": "83af552ea5df666561ceadf107b37508", "status": "passed", "time": {"start": 1754448740309, "stop": 1754448753925, "duration": 13616}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83af552ea5df666561ceadf107b37508"}], "uid": "34ae398183d59ded56dd80b7bf08775a"}, {"name": "test_take_a_joke", "children": [{"name": "TestEllaTakeJoke", "children": [{"name": "测试take a joke能正常执行", "uid": "8f31162e952b18d0", "parentUid": "3acb54c6ceb30b54183a40536a4da62b", "status": "passed", "time": {"start": 1754448767966, "stop": 1754448782951, "duration": 14985}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3acb54c6ceb30b54183a40536a4da62b"}], "uid": "c6397f27fa8b05537072fa113b8c3fc8"}, {"name": "test_take_a_note_on_how_to_build_a_treehouse", "children": [{"name": "TestEllaTakeNoteHowBuildTreehouse", "children": [{"name": "测试take a note on how to build a treehouse能正常执行", "uid": "765be39b94c2f21d", "parentUid": "36906abdedd73fbaf2f1118b2b081f40", "status": "passed", "time": {"start": 1754448797093, "stop": 1754448810624, "duration": 13531}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "36906abdedd73fbaf2f1118b2b081f40"}], "uid": "dd58ab9d8bbd4cb87632d02b4aaab257"}, {"name": "test_take_notes_on_how_to_build_a_treehouse", "children": [{"name": "TestEllaTakeNotesHowBuildTreehouse", "children": [{"name": "测试take notes on how to build a treehouse能正常执行", "uid": "c6a43475c0c2675", "parentUid": "521cf47a0da1175d33dabbff6602fa38", "status": "passed", "time": {"start": 1754448824871, "stop": 1754448838812, "duration": 13941}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "521cf47a0da1175d33dabbff6602fa38"}], "uid": "e2b5971fa35ed9c2de1613d7fffb9817"}, {"name": "test_tell_me_a_joke", "children": [{"name": "TestEllaTellMeJoke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "b6962caf581b09dc", "parentUid": "f4f36a0e5ab72d1f1ffe809d3354f0d7", "status": "failed", "time": {"start": 1754448852645, "stop": 1754448867433, "duration": 14788}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f4f36a0e5ab72d1f1ffe809d3354f0d7"}], "uid": "9dabb9d428e867eabdc2d3a69afdd429"}, {"name": "test_video_call_mom_through_whatsapp", "children": [{"name": "TestEllaVideoCallMomThroughWhatsapp", "children": [{"name": "测试video call mom through whatsapp能正常执行", "uid": "334b9b2e3cf14180", "parentUid": "e9a671f0e7f7a5d9dd84e36b17c5b9d6", "status": "failed", "time": {"start": 1754448881747, "stop": 1754448903362, "duration": 21615}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9a671f0e7f7a5d9dd84e36b17c5b9d6"}], "uid": "52150962482f63ecf4ad855e891df94e"}, {"name": "test_what_is_apec", "children": [{"name": "TestEllaWhatIsApec", "children": [{"name": "测试what is apec?能正常执行", "uid": "e5968f58913b35a0", "parentUid": "1e37b5c5f74c73b4b983a5a9a1939ee9", "status": "passed", "time": {"start": 1754448917614, "stop": 1754448933320, "duration": 15706}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e37b5c5f74c73b4b983a5a9a1939ee9"}], "uid": "c36e05a3608c90521de87174b028a2f3"}, {"name": "test_what_languages_do_you_support", "children": [{"name": "TestEllaWhatLanguagesDoYouSupport", "children": [{"name": "测试What languages do you support能正常执行", "uid": "d9d41f3b82b40b56", "parentUid": "0aa09f1aa9e12279998425a7031b2afb", "status": "passed", "time": {"start": 1754448947539, "stop": 1754448961326, "duration": 13787}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0aa09f1aa9e12279998425a7031b2afb"}], "uid": "53246026784adbe982eaaaa893c66882"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试what's the weather like in shanghai today能正常执行", "uid": "7e37de99bed9dbf8", "parentUid": "abdf90845d6ece7e59853b1633b70976", "status": "passed", "time": {"start": 1754448975280, "stop": 1754448995678, "duration": 20398}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "abdf90845d6ece7e59853b1633b70976"}], "uid": "8d57bd429c231f391c68fd5f296df48d"}, {"name": "test_what_s_the_weather_today", "children": [{"name": "TestEllaWhatSWeatherToday", "children": [{"name": "测试what·s the weather today？能正常执行", "uid": "44244c2a4bfe7443", "parentUid": "1aa11a1add75d7b42e4f5f940b55235c", "status": "passed", "time": {"start": 1754449009816, "stop": 1754449030221, "duration": 20405}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1aa11a1add75d7b42e4f5f940b55235c"}], "uid": "e75c3dcc6971391aa4cea847d0c3df26"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "TestEllaWhatSWheatherToday", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "c09dbe14527ecc7c", "parentUid": "b0d78f07238a99d14c63a09a132b62ac", "status": "failed", "time": {"start": 1754449044387, "stop": 1754449058301, "duration": 13914}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0d78f07238a99d14c63a09a132b62ac"}], "uid": "df9827022acfcacb6cb91f3eade022ae"}, {"name": "test_what_s_your_name", "children": [{"name": "TestEllaWhatSYourName", "children": [{"name": "测试what's your name？能正常执行", "uid": "bb166634eb4139ad", "parentUid": "3d8e08ff740579156ca205ed22a4756d", "status": "failed", "time": {"start": 1754449072637, "stop": 1754449086313, "duration": 13676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3d8e08ff740579156ca205ed22a4756d"}], "uid": "5920d33b40456d715947fca7a27a7717"}, {"name": "test_what_time_is_it_now", "children": [{"name": "TestEllaWhatTimeIsItNow", "children": [{"name": "测试what time is it now能正常执行", "uid": "53b8bc8221a23244", "parentUid": "164a179950ff8bd6ebd07029c9858807", "status": "passed", "time": {"start": 1754449100344, "stop": 1754449114223, "duration": 13879}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "164a179950ff8bd6ebd07029c9858807"}], "uid": "809dcfb95d068444fb46fbf13d07f70d"}, {"name": "test_whats_the_weather_today", "children": [{"name": "TestEllaWhatsWeatherToday", "children": [{"name": "测试whats the weather today能正常执行", "uid": "80bca04c580f2e26", "parentUid": "6cc45efb201d9209b1a6f8794f17a6ed", "status": "passed", "time": {"start": 1754449128369, "stop": 1754449149343, "duration": 20974}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6cc45efb201d9209b1a6f8794f17a6ed"}], "uid": "82328665eb7ef47d6e8c2fd0919234af"}, {"name": "test_who_is_harry_potter", "children": [{"name": "TestEllaWhoIsHarry<PERSON>otter", "children": [{"name": "测试who is harry potter能正常执行", "uid": "b030e1b3bc64bf48", "parentUid": "b5f5061dbe131241d2697c1336ee4d0f", "status": "passed", "time": {"start": 1754449163121, "stop": 1754449179047, "duration": 15926}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5f5061dbe131241d2697c1336ee4d0f"}], "uid": "acd61d814ff9a72ba57d12465068a2b4"}, {"name": "test_who_is_j_k_rowling", "children": [{"name": "TestEllaWhoIsJKRowling", "children": [{"name": "测试who is j k rowling能正常执行", "uid": "745e2ba6171983d5", "parentUid": "bfb9c688e10f1ae6ddcd570a4f050325", "status": "passed", "time": {"start": 1754449193146, "stop": 1754449209426, "duration": 16280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfb9c688e10f1ae6ddcd570a4f050325"}], "uid": "cb98f3056a0e33f548dde9b60e938b6a"}, {"name": "test_why_is_my_phone_not_ringing_on_incoming_calls", "children": [{"name": "TestEllaWhyIsMyPhoneNotRingingIncomingCalls", "children": [{"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "19b5d1c854db43c5", "parentUid": "cdaa83f7b575bd82cf726da90db761c8", "status": "passed", "time": {"start": 1754449223326, "stop": 1754449245649, "duration": 22323}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cdaa83f7b575bd82cf726da90db761c8"}], "uid": "a302ef9f7c0c93aaaa0b2857fe578fbe"}, {"name": "test_why_my_charging_is_so_slow", "children": [{"name": "TestEllaWhyMyChargingIsSoSlow", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "685a39e7eb19f7df", "parentUid": "3750969afddcdbdadfd545f67aca7706", "status": "failed", "time": {"start": 1754449259853, "stop": 1754449273809, "duration": 13956}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3750969afddcdbdadfd545f67aca7706"}], "uid": "48e6e701074c76202e2d7a86eeb1f71a"}], "uid": "0a5f897bb744ec2f8b960fc5954cddf6"}, {"name": "testcases.test_ella.open_app", "children": [{"name": "test_open_calculator", "children": [{"name": "TestEllaOpenCalculator", "children": [{"name": "测试open calculator", "uid": "8b898a5d16f54ad9", "parentUid": "3529c9f04e7b1abd02e1106cb825e806", "status": "failed", "time": {"start": 1754449288241, "stop": 1754449304334, "duration": 16093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3529c9f04e7b1abd02e1106cb825e806"}], "uid": "7ab3d99c22e7fced5e4fc2816cff03a4"}, {"name": "test_open_camera", "children": [{"name": "TestEllaOpenCamera", "children": [{"name": "测试open camera", "uid": "9bc8f1759283437a", "parentUid": "69c34b6e1ef0e0146273e12eaada58d5", "status": "passed", "time": {"start": 1754449318689, "stop": 1754449336910, "duration": 18221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69c34b6e1ef0e0146273e12eaada58d5"}], "uid": "6e0a17a71edb82c1a70055256084672b"}], "uid": "6d079dc82513aed148d199a00f967765"}, {"name": "testcases.test_ella.system_coupling", "children": [{"name": "test_adjustment_the_brightness_to", "children": [{"name": "TestEllaAdjustmentBrightness", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "2847f36547f4565c", "parentUid": "13406d3249e45c3f0440290f62a0a585", "status": "passed", "time": {"start": 1754449351011, "stop": 1754449364997, "duration": 13986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "13406d3249e45c3f0440290f62a0a585"}], "uid": "169492529a068768aa4c236be730e885"}, {"name": "test_change_your_language_to_chinese", "children": [{"name": "TestEllaChangeYourLanguageChinese", "children": [{"name": "测试change your language to chinese能正常执行", "uid": "e655393d16373534", "parentUid": "667250dfa9bb5f1cdb6758ac0cf296c4", "status": "passed", "time": {"start": 1754449379042, "stop": 1754449391214, "duration": 12172}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "667250dfa9bb5f1cdb6758ac0cf296c4"}], "uid": "1d67b1adb2c056e5340bbadd4c0bffaf"}, {"name": "test_check_front_camera_information", "children": [{"name": "TestEllaCheckFrontCameraInformation", "children": [{"name": "测试check front camera information能正常执行", "uid": "7776530c69781978", "parentUid": "12ceb3f2ca52b0e705dbde45ff23637e", "status": "failed", "time": {"start": 1754449405309, "stop": 1754449428775, "duration": 23466}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12ceb3f2ca52b0e705dbde45ff23637e"}], "uid": "900d85dd19a0727e9711d6cd307efe61"}, {"name": "test_clear_junk_files", "children": [{"name": "TestEllaClearJunkFiles", "children": [{"name": "测试clear junk files命令", "uid": "2b68b27e39c0eb31", "parentUid": "d96f4287167e7edbf6a73f9393984a7d", "status": "failed", "time": {"start": 1754449442920, "stop": 1754449475136, "duration": 32216}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d96f4287167e7edbf6a73f9393984a7d"}], "uid": "4c6f4bc8bf308c30c67ce68d84340b5b"}, {"name": "test_close_bluetooth", "children": [{"name": "TestEllaCloseBluetooth", "children": [{"name": "测试close bluetooth能正常执行", "uid": "acc50f49b95172e6", "parentUid": "1d722ccfa222e03cef528f38b7d2fac2", "status": "failed", "time": {"start": 1754449489597, "stop": 1754449503246, "duration": 13649}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1d722ccfa222e03cef528f38b7d2fac2"}], "uid": "aa4164fbbe23d76eaa9669ca854678b0"}, {"name": "test_close_flashlight", "children": [{"name": "TestEllaCloseFlashlight", "children": [{"name": "测试close flashlight能正常执行", "uid": "93ef6d13c079fd1", "parentUid": "d82496a02dca69564564d1692d50a7e6", "status": "failed", "time": {"start": 1754449517677, "stop": 1754449532009, "duration": 14332}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d82496a02dca69564564d1692d50a7e6"}], "uid": "82ec45c00676757f05c50c06f422bf20"}, {"name": "test_close_wifi", "children": [{"name": "TestEllaCloseWifi", "children": [{"name": "测试close wifi能正常执行", "uid": "fbeb0a397c34b803", "parentUid": "008777689d082e9a2e48620bded15bc5", "status": "failed", "time": {"start": 1754449546506, "stop": 1754449559659, "duration": 13153}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "008777689d082e9a2e48620bded15bc5"}], "uid": "eee3cdf5a14de75128923fc136f73cc1"}, {"name": "test_countdown_min", "children": [{"name": "TestEllaCountdownMin", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "8712b4ac10ccc2e7", "parentUid": "45aee82a630fdec483043c1cf3f74bf1", "status": "failed", "time": {"start": 1754449574015, "stop": 1754449589731, "duration": 15716}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "45aee82a630fdec483043c1cf3f74bf1"}], "uid": "f6b105e104089e68c280ff5c4fa1d393"}, {"name": "test_decrease_the_brightness", "children": [{"name": "TestEllaDecreaseBrightness", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "a9782300c788ad8c", "parentUid": "375849c4ecfe2bbf01bcecaaef84c8db", "status": "failed", "time": {"start": 1754449604272, "stop": 1754449618034, "duration": 13762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "375849c4ecfe2bbf01bcecaaef84c8db"}], "uid": "c7d1062159fa1a8529d01de2304b5ef9"}, {"name": "test_end_screen_recording", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "b3bd4eefa66cd793", "parentUid": "cb8ba3420a3f42d8dc07018eb937d89c", "status": "failed", "time": {"start": 1754449632803, "stop": 1754449651634, "duration": 18831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "f1b11a5e7a615977", "parentUid": "cb8ba3420a3f42d8dc07018eb937d89c", "status": "failed", "time": {"start": 1754449666049, "stop": 1754449684411, "duration": 18362}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb8ba3420a3f42d8dc07018eb937d89c"}], "uid": "80f3221929ed1bf33f33827613bd2441"}, {"name": "test_help_me_take_a_long_screenshot", "children": [{"name": "TestEllaHelpMeTakeLongScreenshot", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "7595375f57ce60c8", "parentUid": "eddf309b25a6b37f2ead731622b025cd", "status": "failed", "time": {"start": 1754449699015, "stop": 1754449717676, "duration": 18661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eddf309b25a6b37f2ead731622b025cd"}], "uid": "2f4262c05e172e74fc2c7093e52387d3"}, {"name": "test_help_me_take_a_screenshot", "children": [{"name": "TestEllaHelpMeTakeScreenshot", "children": [{"name": "测试help me take a screenshot能正常执行", "uid": "3814b093ab5b35ad", "parentUid": "1b701ed7fafa2b81e3b507e55819c327", "status": "passed", "time": {"start": 1754449732216, "stop": 1754449748925, "duration": 16709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b701ed7fafa2b81e3b507e55819c327"}], "uid": "b0951018c5f4d74c567ddd6d37b5afe7"}, {"name": "test_long_screenshot", "children": [{"name": "TestEllaLongScreenshot", "children": [{"name": "测试long screenshot能正常执行", "uid": "46957ed2e17cc77b", "parentUid": "10001a71b5e68735481b40decacdb66b", "status": "passed", "time": {"start": 1754449763285, "stop": 1754449779835, "duration": 16550}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "10001a71b5e68735481b40decacdb66b"}], "uid": "fd90b40ba6ffaaec61bd1bf8a6dfb743"}, {"name": "test_maximum_volume", "children": [{"name": "TestEllaMaximumVolume", "children": [{"name": "测试maximum volume能正常执行", "uid": "8b9d6241d0992877", "parentUid": "6dba0e91979783ed4ec530b3d80f6368", "status": "failed", "time": {"start": 1754449793578, "stop": 1754449806906, "duration": 13328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6dba0e91979783ed4ec530b3d80f6368"}], "uid": "42069d56eafe8c2317edc83b4e050135"}, {"name": "test_memory_cleanup", "children": [{"name": "TestEllaMemoryCleanup", "children": [{"name": "测试memory cleanup能正常执行", "uid": "2ce3a0f3ed9c5dd1", "parentUid": "402a46f59edfad113bb75993c3b1ee0a", "status": "failed", "time": {"start": 1754449821447, "stop": 1754449849617, "duration": 28170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "402a46f59edfad113bb75993c3b1ee0a"}], "uid": "b4111ca5d9e6c15c7f70fd341fca204a"}, {"name": "test_minimum_volume", "children": [{"name": "TestEllaMinimumVolume", "children": [{"name": "测试minimum volume能正常执行", "uid": "743b780e831d526", "parentUid": "8ea78a3baf10b4390053ed171e147a99", "status": "passed", "time": {"start": 1754449863875, "stop": 1754449878772, "duration": 14897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8ea78a3baf10b4390053ed171e147a99"}], "uid": "5f5ae3dc5f88288d2dab05294be5ba53"}, {"name": "test_open_bluetooth", "children": [{"name": "TestEllaOpenBluetooth", "children": [{"name": "测试open bluetooth", "uid": "96f699579976c495", "parentUid": "ce97beca6f71d794ad4aa2488c2a51cc", "status": "passed", "time": {"start": 1754449892699, "stop": 1754449906211, "duration": 13512}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ce97beca6f71d794ad4aa2488c2a51cc"}], "uid": "ff64db128f121f9873fe4471e4dffdb3"}, {"name": "test_open_bt", "children": [{"name": "TestEllaOpenBluetooth", "children": [{"name": "测试open bt", "uid": "84f9ea91bdf0b93d", "parentUid": "cff090d0294d065259343d3c0be3e870", "status": "passed", "time": {"start": 1754449920126, "stop": 1754449933865, "duration": 13739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "cff090d0294d065259343d3c0be3e870"}], "uid": "889f90fb090c14dc6df34ad6387bda95"}, {"name": "test_open_flashlight", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open flashlight", "uid": "7b11c4ac97004f21", "parentUid": "bc7827d0911898d37df7fcf78b433e01", "status": "passed", "time": {"start": 1754449948163, "stop": 1754449964590, "duration": 16427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc7827d0911898d37df7fcf78b433e01"}], "uid": "dbd0fd96c304d0311b6a78f5c9f6d7a2"}, {"name": "test_open_wifi", "children": [{"name": "TestEllaOpenWifi", "children": [{"name": "测试open wifi", "uid": "f883a50d936e4a49", "parentUid": "4f8addb21351f5f5c147c6f32359e6f8", "status": "passed", "time": {"start": 1754449978649, "stop": 1754449993380, "duration": 14731}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4f8addb21351f5f5c147c6f32359e6f8"}], "uid": "732e1a744ff2476f886f4f9095220be6"}, {"name": "test_power_saving", "children": [{"name": "TestEllaPowerSaving", "children": [{"name": "测试power saving能正常执行", "uid": "45a77846489fea0", "parentUid": "cca844da29af41e39df266a7ebd5b861", "status": "passed", "time": {"start": 1754450007334, "stop": 1754450022955, "duration": 15621}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "cca844da29af41e39df266a7ebd5b861"}], "uid": "b3608e6af09c4f99081f13cadb0a5d44"}, {"name": "test_screen_record", "children": [{"name": "TestEllaScreenRecord", "children": [{"name": "测试screen record能正常执行", "uid": "1cb174fb389581dd", "parentUid": "26f506cb56eae899b201e1de8c38bd6f", "status": "passed", "time": {"start": 1754450037016, "stop": 1754450055449, "duration": 18433}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "5e085943d42f8b0c", "parentUid": "26f506cb56eae899b201e1de8c38bd6f", "status": "passed", "time": {"start": 1754450069731, "stop": 1754450088254, "duration": 18523}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "26f506cb56eae899b201e1de8c38bd6f"}], "uid": "e1332233b220a65d2fdc0ae00a593bdc"}, {"name": "test_set_battery_saver_setting", "children": [{"name": "TestEllaSetBatterySaverSetting", "children": [{"name": "测试set Battery Saver setting能正常执行", "uid": "21072eabbe5acafe", "parentUid": "b61ff8d9b726452f92d852b371aa6a90", "status": "passed", "time": {"start": 1754450102193, "stop": 1754450125586, "duration": 23393}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b61ff8d9b726452f92d852b371aa6a90"}], "uid": "28b4581513f8589b378bc346015a9490"}, {"name": "test_smart_charge", "children": [{"name": "TestEllaSmartCharge", "children": [{"name": "测试smart charge能正常执行", "uid": "409d5e47442fd23e", "parentUid": "41f573a06c0e20aa83d29445f512857c", "status": "failed", "time": {"start": 1754450139671, "stop": 1754450152968, "duration": 13297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41f573a06c0e20aa83d29445f512857c"}], "uid": "0669c1c9bf3fff5019832bc329d03550"}, {"name": "test_start_record", "children": [{"name": "TestEllaStartRecord", "children": [{"name": "测试start record能正常执行", "uid": "803b6376429b640e", "parentUid": "fad9085499f3f4042c08d5be02eb6b0a", "status": "passed", "time": {"start": 1754450167254, "stop": 1754450185189, "duration": 17935}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "78e2205d4e4f206c", "parentUid": "fad9085499f3f4042c08d5be02eb6b0a", "status": "passed", "time": {"start": 1754450199489, "stop": 1754450218292, "duration": 18803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "fad9085499f3f4042c08d5be02eb6b0a"}], "uid": "0d2acbf80a2bbd78f39946ab4d920635"}, {"name": "test_start_screen_recording", "children": [{"name": "TestEllaStartScreenRecording", "children": [{"name": "测试start screen recording能正常执行", "uid": "51910e205a82d03e", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1754450232215, "stop": 1754450250336, "duration": 18121}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "36b8c2f8e2765835", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1754450264491, "stop": 1754450280445, "duration": 15954}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "11613cef42c356cf", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1754450294402, "stop": 1754450310643, "duration": 16241}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "e068de8b2837d67", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1754450325189, "stop": 1754450343632, "duration": 18443}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "94605bd37b622971e2f6d1306d537ac9"}], "uid": "9210a41e638d65f3b50b4de9468577cb"}, {"name": "test_stop_recording", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "f46e11db85416dc1", "parentUid": "6d9aa92a00ed0065f1758dce8f814a21", "status": "passed", "time": {"start": 1754450357435, "stop": 1754450375374, "duration": 17939}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "262d606376ce8700", "parentUid": "6d9aa92a00ed0065f1758dce8f814a21", "status": "passed", "time": {"start": 1754450389334, "stop": 1754450403774, "duration": 14440}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "6d9aa92a00ed0065f1758dce8f814a21"}], "uid": "12d9ec9f3661e4a6cbb3393c17a142c0"}, {"name": "test_switch_charging_modes", "children": [{"name": "TestEllaSwitchChargingModes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "afe93fd71bbebe1b", "parentUid": "9788e3a9971a6e6142c9edebcd8d6df5", "status": "failed", "time": {"start": 1754450417620, "stop": 1754450431165, "duration": 13545}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9788e3a9971a6e6142c9edebcd8d6df5"}], "uid": "9df29768698803a92c70ca28b324bcd6"}, {"name": "test_switch_magic_voice_to_grace", "children": [{"name": "TestEllaSwitchMagicVoiceGrace", "children": [{"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "f9f860dc66e4a117", "parentUid": "a68eab3c6aa1e1b791a77b31c94dd2f2", "status": "passed", "time": {"start": 1754450445439, "stop": 1754450459010, "duration": 13571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a68eab3c6aa1e1b791a77b31c94dd2f2"}], "uid": "ee64f637be288a86beb1fb3a41d0f889"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "TestEllaSwitchMagicVoiceToMango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "bb94c3185f87279a", "parentUid": "f890978c47c61f26513c6a80d7cd0497", "status": "passed", "time": {"start": 1754450472924, "stop": 1754450486952, "duration": 14028}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f890978c47c61f26513c6a80d7cd0497"}], "uid": "04bb98199ad164d25c90308e2b594563"}, {"name": "test_switch_to_barrage_notification", "children": [{"name": "TestEllaSwitchBarrageNotification", "children": [{"name": "测试Switch to Barrage Notification能正常执行", "uid": "987fe19014f79729", "parentUid": "4e744fe5421b1f85b2b48648f841c620", "status": "passed", "time": {"start": 1754450500684, "stop": 1754450514283, "duration": 13599}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4e744fe5421b1f85b2b48648f841c620"}], "uid": "81d75a279e51195a6bdcc1b7476061cc"}, {"name": "test_switch_to_default_mode", "children": [{"name": "TestEllaSwitchToDefaultMode", "children": [{"name": "测试switch to default mode能正常执行", "uid": "d95aec3991ba9c49", "parentUid": "7b329a8fe9abef3ed9e84e7ba123812d", "status": "failed", "time": {"start": 1754450528080, "stop": 1754450550941, "duration": 22861}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b329a8fe9abef3ed9e84e7ba123812d"}], "uid": "4c06db8cd2466afbff036b96a7b29084"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchToEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "42ccbac9fc7e7802", "parentUid": "76faa1b8f60ce1117990613102818bb9", "status": "passed", "time": {"start": 1754450565314, "stop": 1754450579258, "duration": 13944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "76faa1b8f60ce1117990613102818bb9"}], "uid": "a631118d3a602f7957b9df1f61e89d8b"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "TestEllaSwitchToFlashNotification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "70607ed82c3ddd8b", "parentUid": "28cdac4aed197c9ad9daa4c4660b871b", "status": "failed", "time": {"start": 1754450593240, "stop": 1754450616621, "duration": 23381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "28cdac4aed197c9ad9daa4c4660b871b"}], "uid": "199a594d9e7fb8316cc2c05c01918204"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "TestEllaSwitchToHyperCharge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "cddd64f0dce015e5", "parentUid": "65ad6f571b805fa05f9b55669abf1de1", "status": "failed", "time": {"start": 1754450630749, "stop": 1754450644126, "duration": 13377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "65ad6f571b805fa05f9b55669abf1de1"}], "uid": "231bfdf13683acaff012f9cc475bdd12"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "TestEllaSwitchToLowtempCharge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "9c90db8fe9592caf", "parentUid": "c943e664fb1e3d8f2e7feabc9d26cf9b", "status": "failed", "time": {"start": 1754450658321, "stop": 1754450671918, "duration": 13597}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c943e664fb1e3d8f2e7feabc9d26cf9b"}], "uid": "f274d94636e10c97050c0bced04829d1"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchToPowerSavingMode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "28a1196086132c45", "parentUid": "fa491a2fccbb92902b5fea6a0125d0dd", "status": "passed", "time": {"start": 1754450686274, "stop": 1754450701168, "duration": 14894}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "fa491a2fccbb92902b5fea6a0125d0dd"}], "uid": "276d2d0724438fcbfe51444d46cecee8"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "TestEllaSwitchToSmartCharge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "a1ae04a8c91793c9", "parentUid": "f0ac9e1d670781c7273288548b5e912c", "status": "failed", "time": {"start": 1754450715220, "stop": 1754450728748, "duration": 13528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0ac9e1d670781c7273288548b5e912c"}], "uid": "5dbf89ce49106a865cad1e16016f18aa"}, {"name": "test_switched_to_data_mode", "children": [{"name": "TestEllaSwitchedDataMode", "children": [{"name": "测试switched to data mode能正常执行", "uid": "4ce79f74a58dc18e", "parentUid": "ee2a0ba313d9f55972191c0fc58a643b", "status": "passed", "time": {"start": 1754450743050, "stop": 1754450758408, "duration": 15358}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ee2a0ba313d9f55972191c0fc58a643b"}], "uid": "7f43f619728469165153ad2076591cb3"}, {"name": "test_take_a_photo", "children": [{"name": "TestEllaTakePhoto", "children": [{"name": "测试take a photo能正常执行", "uid": "c8517a0d9fe0ff9f", "parentUid": "491b8997a74c0d96f03949ae2b53cb5a", "status": "passed", "time": {"start": 1754450772328, "stop": 1754450802490, "duration": 30162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "491b8997a74c0d96f03949ae2b53cb5a"}], "uid": "cf46440006651ab12729bd35922e17b6"}, {"name": "test_take_a_selfie", "children": [{"name": "TestEllaTakeSelfie", "children": [{"name": "测试take a selfie能正常执行", "uid": "7de2c6f37b638397", "parentUid": "4605cd6537361d8c73216c0b79b6bdcd", "status": "passed", "time": {"start": 1754450816757, "stop": 1754450847988, "duration": 31231}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4605cd6537361d8c73216c0b79b6bdcd"}], "uid": "458be1e68e81f7e0a5f24b2b8e711037"}, {"name": "test_the_battery_of_the_mobile_phone_is_too_low", "children": [{"name": "TestEllaBatteryMobilePhoneIsTooLow", "children": [{"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "2c78a1a091cf6fe5", "parentUid": "e9e63c79cb2e897c6c2ba74546e55ab8", "status": "passed", "time": {"start": 1754450861763, "stop": 1754450877680, "duration": 15917}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e9e63c79cb2e897c6c2ba74546e55ab8"}], "uid": "7fec87272bec0f0286d76fc5e499fde9"}, {"name": "test_turn_down_ring_volume", "children": [{"name": "TestEllaTurnDownRingVolume", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "81616d50f4291bda", "parentUid": "c650a0dd4c418a43693046a1806d4660", "status": "passed", "time": {"start": 1754450891563, "stop": 1754450905225, "duration": 13662}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c650a0dd4c418a43693046a1806d4660"}], "uid": "b8d3945e930e3eeebfc2c83a67a45522"}, {"name": "test_turn_off_flashlight", "children": [{"name": "TestEllaTurnOffFlashlight", "children": [{"name": "测试turn off flashlight能正常执行", "uid": "3f7579b798a46d85", "parentUid": "11b19c6407e932f014698c24c4c16b7b", "status": "passed", "time": {"start": 1754450919306, "stop": 1754450935420, "duration": 16114}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "11b19c6407e932f014698c24c4c16b7b"}], "uid": "83abfdb472ff182fcf9c9f70441da7db"}, {"name": "test_turn_off_wifi", "children": [{"name": "TestEllaTurnOffWifi", "children": [{"name": "测试turn off wifi能正常执行", "uid": "cd943b7f9abecf97", "parentUid": "bce952c785fb87091c7e5b46722d0083", "status": "passed", "time": {"start": 1754450949206, "stop": 1754450963531, "duration": 14325}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "bce952c785fb87091c7e5b46722d0083"}], "uid": "3f1a34c868c0ccf5b088895d0f46707d"}, {"name": "test_turn_on_bluetooth", "children": [{"name": "TestEllaTurnBluetooth", "children": [{"name": "测试turn on bluetooth能正常执行", "uid": "1e11879944dfe296", "parentUid": "50b13d10c6f48bcd3b710698be278545", "status": "passed", "time": {"start": 1754450977331, "stop": 1754450990939, "duration": 13608}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "50b13d10c6f48bcd3b710698be278545"}], "uid": "73521b2a406c05e4ea6775f8ac550b11"}, {"name": "test_turn_on_do_not_disturb_mode", "children": [{"name": "TestEllaTurnDoNotDisturbMode", "children": [{"name": "测试turn on do not disturb mode能正常执行", "uid": "780ea6e88536084c", "parentUid": "b24c19041d3913a347b45ddb7794d996", "status": "passed", "time": {"start": 1754451004913, "stop": 1754451019638, "duration": 14725}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b24c19041d3913a347b45ddb7794d996"}], "uid": "25300102bed6a3f35a171590779f5760"}, {"name": "test_turn_on_light_theme", "children": [{"name": "TestEllaTurnLightTheme", "children": [{"name": "测试turn on light theme能正常执行", "uid": "9204d83b6b8f7fd2", "parentUid": "33d9ea5586f4fc177aea2dcfffa56adf", "status": "passed", "time": {"start": 1754451033561, "stop": 1754451047165, "duration": 13604}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "9aee619060e4ff8", "parentUid": "33d9ea5586f4fc177aea2dcfffa56adf", "status": "passed", "time": {"start": 1754451061346, "stop": 1754451074571, "duration": 13225}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "33d9ea5586f4fc177aea2dcfffa56adf"}], "uid": "491955b9c16082a7c444ca2d26b7637c"}, {"name": "test_turn_on_location_services", "children": [{"name": "TestEllaTurnLocationServices", "children": [{"name": "测试turn on location services能正常执行", "uid": "90e2769c8ce04c27", "parentUid": "b0fdf160b2ecb52332e6867a9d6becde", "status": "passed", "time": {"start": 1754451088417, "stop": 1754451103456, "duration": 15039}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b0fdf160b2ecb52332e6867a9d6becde"}], "uid": "cf19f0ebd19888cf46f82f4e0bd4bfac"}, {"name": "test_turn_on_the_flashlight", "children": [{"name": "TestEllaTurnFlashlight", "children": [{"name": "测试turn on the flashlight能正常执行", "uid": "f6dd481761067622", "parentUid": "a46ae606b16354d85bcc730dcdcbe96e", "status": "passed", "time": {"start": 1754451117198, "stop": 1754451133008, "duration": 15810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a46ae606b16354d85bcc730dcdcbe96e"}], "uid": "5ff8e41b3404a9b3f6cce0bee037eb96"}, {"name": "test_turn_on_the_screen_record", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "bbf642526ea09a6", "parentUid": "678c4f3bd7c3cae1c7216386baae6d8b", "status": "passed", "time": {"start": 1754451146850, "stop": 1754451164847, "duration": 17997}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "fe60a039ac946371", "parentUid": "678c4f3bd7c3cae1c7216386baae6d8b", "status": "passed", "time": {"start": 1754451179149, "stop": 1754451198065, "duration": 18916}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "678c4f3bd7c3cae1c7216386baae6d8b"}], "uid": "62efa7d4bd969e35ac386aa7097579c5"}, {"name": "test_turn_on_wifi", "children": [{"name": "TestEllaTurnWifi", "children": [{"name": "测试turn on wifi能正常执行", "uid": "bc9a3f7b0d5c458d", "parentUid": "6628c4e31fae719ad94c679a246d743e", "status": "passed", "time": {"start": 1754451212018, "stop": 1754451226679, "duration": 14661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "6628c4e31fae719ad94c679a246d743e"}], "uid": "8a973098444298c6ff004df8ce42b561"}, {"name": "test_wake_me_up_at_am_tomorrow", "children": [{"name": "TestEllaWakeMeUpAmTomorrow", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "af6499461f251805", "parentUid": "a95ef973249db47161b0248e62a09176", "status": "passed", "time": {"start": 1754451240559, "stop": 1754451254442, "duration": 13883}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a95ef973249db47161b0248e62a09176"}], "uid": "19346142b5a8f43c02ff8d5890db9067"}, {"name": "test_where_is_the_carlcare_service_outlet", "children": [{"name": "TestEllaWhereIsCarlcareServiceOutlet", "children": [{"name": "测试where is the carlcare service outlet能正常执行", "uid": "d2547e36da3e5bf7", "parentUid": "ca0e45e31e627eb4f9d8c39d6a6bf138", "status": "passed", "time": {"start": 1754451268361, "stop": 1754451284343, "duration": 15982}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ca0e45e31e627eb4f9d8c39d6a6bf138"}], "uid": "7d4ef22a8f804451209374dfe8bf64ad"}], "uid": "4159dc35ce06d1422bb1b7c5665d834a"}, {"name": "testcases.test_ella.third_coupling", "children": [{"name": "test_download_app", "children": [{"name": "TestEllaDownloadApp", "children": [{"name": "测试download app能正常执行", "uid": "eb1da234ac691e7c", "parentUid": "3437a290da1a678bac85f4666839d881", "status": "passed", "time": {"start": 1754451298290, "stop": 1754451312878, "duration": 14588}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3437a290da1a678bac85f4666839d881"}], "uid": "ddb6cf5770aedc759f08823b3e6d8b65"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball能正常执行", "uid": "46a76feb8efb0c84", "parentUid": "588cc0c6328e2e8a712130edc3aed897", "status": "passed", "time": {"start": 1754451326748, "stop": 1754451341823, "duration": 15075}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "588cc0c6328e2e8a712130edc3aed897"}], "uid": "1d4ceb2f76f4a7e0f4095f5c1f138613"}, {"name": "test_download_qq", "children": [{"name": "TestEllaDownloadQq", "children": [{"name": "测试download qq能正常执行", "uid": "ddc18dfac4b48d27", "parentUid": "510be9da11cf469597d4e78dac5a719b", "status": "passed", "time": {"start": 1754451355833, "stop": 1754451372839, "duration": 17006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "510be9da11cf469597d4e78dac5a719b"}], "uid": "e47ca49712e495f1dd67541fd74682ac"}, {"name": "test_find_a_restaurant_near_me", "children": [{"name": "TestEllaFindRestaurantNearMe", "children": [{"name": "测试find a restaurant near me能正常执行", "uid": "33c5442f97273add", "parentUid": "2a2a6349a33f526f9659f59faee83c46", "status": "passed", "time": {"start": 1754451386783, "stop": 1754451411771, "duration": 24988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a2a6349a33f526f9659f59faee83c46"}], "uid": "6ecd9aa1e47da9fc316c168ef75730c9"}, {"name": "test_navigate_from_beijing_to_shanghai", "children": [{"name": "TestEllaNavigateFromBeijingShanghai", "children": [{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "be1c07cf401ff3e1", "parentUid": "d7122eb175471ed23416d503cb147f88", "status": "passed", "time": {"start": 1754451425856, "stop": 1754451444106, "duration": 18250}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d7122eb175471ed23416d503cb147f88"}], "uid": "7b053180829d8e59af204f55867fbbbe"}, {"name": "test_navigate_from_to_red_square", "children": [{"name": "TestEllaNavigateFromRedSquare", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "6480df8a6c46a6db", "parentUid": "e6c951f1c462cc23dddd03e98f8d3a27", "status": "passed", "time": {"start": 1754451457990, "stop": 1754451478299, "duration": 20309}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e6c951f1c462cc23dddd03e98f8d3a27"}], "uid": "f52e70464a63502b53d063b021cf198a"}, {"name": "test_navigate_to_shanghai_disneyland", "children": [{"name": "TestEllaNavigateShanghaiDisneyland", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "df371b5ba678804", "parentUid": "5ae1d6975695489eb2d69ce256cf164f", "status": "passed", "time": {"start": 1754451492189, "stop": 1754451512550, "duration": 20361}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5ae1d6975695489eb2d69ce256cf164f"}], "uid": "1dbef048a8c2c8a81b66d201495c6825"}, {"name": "test_navigation_to_the_lucky", "children": [{"name": "TestEllaNavigationToTheLucky", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "8ba01c509e185f21", "parentUid": "719ca28cb458f784122e2efc4135056f", "status": "passed", "time": {"start": 1754451526706, "stop": 1754451543802, "duration": 17096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "719ca28cb458f784122e2efc4135056f"}], "uid": "de0db7b40aca346332d2b0972719957a"}, {"name": "test_open_facebook", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open facebook能正常执行", "uid": "253154c273af4b74", "parentUid": "7b0b3aea4bca28c8189ca6a4be130237", "status": "passed", "time": {"start": 1754451557799, "stop": 1754451575932, "duration": 18133}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "7b0b3aea4bca28c8189ca6a4be130237"}], "uid": "e4224d3534251a2780344938a85b3c06"}, {"name": "test_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试open whatsapp", "uid": "199c7c5fd883ed42", "parentUid": "e65b3469b422d0d9909fee2c8bfa0960", "status": "failed", "time": {"start": 1754451590134, "stop": 1754451604950, "duration": 14816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e65b3469b422d0d9909fee2c8bfa0960"}], "uid": "5aeebca39d474445d6f5ba2265081895"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试order a burger能正常执行", "uid": "ff31feb8037e8115", "parentUid": "43a31fc74a106d981123a4cb94e40290", "status": "failed", "time": {"start": 1754451619144, "stop": 1754451632488, "duration": 13344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43a31fc74a106d981123a4cb94e40290"}], "uid": "3e2cc02d0935d06e8fb6f72bae4c8c83"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderATakeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "51c4a2ce03ff9f3b", "parentUid": "d9e81a960347bb8bb7bfbcc70b663c4b", "status": "failed", "time": {"start": 1754451646749, "stop": 1754451660700, "duration": 13951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9e81a960347bb8bb7bfbcc70b663c4b"}], "uid": "24271799b32debb7d5f05bce65733a10"}, {"name": "test_pls_open_the_newest_whatsapp_activity", "children": [{"name": "TestEllaOpenPlsNewestWhatsappActivity", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "ad34c9352740304a", "parentUid": "5022c6b03e034f068f4ddbc7533f4c29", "status": "passed", "time": {"start": 1754451675001, "stop": 1754451690219, "duration": 15218}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5022c6b03e034f068f4ddbc7533f4c29"}], "uid": "9b7c89d46b9b721d197765453f1675de"}, {"name": "test_whatsapp", "children": [{"name": "TestEllaW<PERSON>sapp", "children": [{"name": "测试whatsapp能正常执行", "uid": "aaf2e2359901cf45", "parentUid": "edf22212c64c387b5f0b05436a17000b", "status": "broken", "time": {"start": 1754451704338, "stop": 1754451719424, "duration": 15086}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "edf22212c64c387b5f0b05436a17000b"}], "uid": "0438f804905f59a497b4803048f51319"}], "uid": "2660f6320a566ad526d6ea679fb2528f"}, {"name": "testcases.test_ella.unsupported_commands", "children": [{"name": "test_Add_the_images_and_text_on_the_screen_to_the_note", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "d691924f9e325c36", "parentUid": "433b944b4500addcf62906018e50a6af", "status": "failed", "time": {"start": 1754451733535, "stop": 1754451747369, "duration": 13834}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "433b944b4500addcf62906018e50a6af"}], "uid": "3d0102c3a98807edcea83109224019cc"}, {"name": "test_change_female_tone_name_voice", "children": [{"name": "TestEllaChangeFemaleToneNameVoice", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "6513ea7d676f9aee", "parentUid": "b565d06b86fa223901766faddeda207a", "status": "failed", "time": {"start": 1754451748972, "stop": 1754451748972, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b565d06b86fa223901766faddeda207a"}], "uid": "66562456c81152cc1fa9d09032ae884d"}, {"name": "test_check_battery_information", "children": [{"name": "TestEllaCheckBatteryInformation", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "cc4b26a4d2f8fb86", "parentUid": "d28e0e4ff0e4838a6027dc47e3b3a670", "status": "failed", "time": {"start": 1754451783661, "stop": 1754451783661, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d28e0e4ff0e4838a6027dc47e3b3a670"}], "uid": "4c906a8c1578240415aebe31f410f234"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "TestEllaCheckMobileDataBalanceSim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "f418a21c3f8bcc0d", "parentUid": "2e50c8269684c4a73bb5b020614c2221", "status": "failed", "time": {"start": 1754451818320, "stop": 1754451818320, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2e50c8269684c4a73bb5b020614c2221"}], "uid": "ff56d23044bd5d499190e5ca173df4d3"}, {"name": "test_check_model_information", "children": [{"name": "TestEllaCheckModelInformation", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "b6f84103b65be0e9", "parentUid": "f7e3ca8a9343a9ed054471fe3e0356b4", "status": "failed", "time": {"start": 1754451853335, "stop": 1754451853335, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f7e3ca8a9343a9ed054471fe3e0356b4"}], "uid": "bf2cc0fa355876e3be500d0c5ba7a5f1"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "TestEllaCheckMyBalanceSim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "aff9de2e497af703", "parentUid": "f9995b28dafd2b1ed8762fedcb92c132", "status": "failed", "time": {"start": 1754451888447, "stop": 1754451888447, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f9995b28dafd2b1ed8762fedcb92c132"}], "uid": "ffd79a31b3c86841ac0cc6d5fa40f471"}, {"name": "test_check_my_to_do_list", "children": [{"name": "TestEllaCheckMyDoList", "children": [{"name": "测试check my to-do list能正常执行", "uid": "cb9bf1bed63324f0", "parentUid": "43310d5383f9f3391eb3ba14577fd4d7", "status": "failed", "time": {"start": 1754451923380, "stop": 1754451923380, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43310d5383f9f3391eb3ba14577fd4d7"}], "uid": "20383a2eb3e29a6c6ccd53f0bce07fe1"}, {"name": "test_check_rear_camera_information", "children": [{"name": "TestEllaCheckRearCameraInformation", "children": [{"name": "测试check rear camera information能正常执行", "uid": "1c1269d5fcc8834f", "parentUid": "89f05870118719c157701595fa592ca6", "status": "failed", "time": {"start": 1754451958469, "stop": 1754451958469, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "89f05870118719c157701595fa592ca6"}], "uid": "3e2d1f7b0da0ab237b35604822208008"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "TestEllaCloseEquilibriumMode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "42624ab66ae39dc7", "parentUid": "2ca6423e5b9b0595a667d74916b87ae8", "status": "failed", "time": {"start": 1754451993567, "stop": 1754451993567, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ca6423e5b9b0595a667d74916b87ae8"}], "uid": "cf0d3a245849cc3d2fb49b5919a2ca70"}, {"name": "test_close_performance_mode", "children": [{"name": "TestEllaClosePerformanceMode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "e27150fcafd03433", "parentUid": "bfc98a3e31cf71b24d2c2664874d8f35", "status": "failed", "time": {"start": 1754452028631, "stop": 1754452028631, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfc98a3e31cf71b24d2c2664874d8f35"}], "uid": "921c03c2af17c0dd8bb300d05ebc1435"}, {"name": "test_close_power_saving_mode", "children": [{"name": "TestEllaClosePowerSavingMode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "c298b828e0b31f92", "parentUid": "d7c06ff8b4a17dd9ae62ae957d96f702", "status": "failed", "time": {"start": 1754452063624, "stop": 1754452063624, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7c06ff8b4a17dd9ae62ae957d96f702"}], "uid": "de4693d49200ef3897dfa7e2a5aa8dbc"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "TestEllaDisableAccelerateDialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "2e9c0e779971b6f2", "parentUid": "a12fb87a3ef6191f6c5649ce65173837", "status": "failed", "time": {"start": 1754452098668, "stop": 1754452098668, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a12fb87a3ef6191f6c5649ce65173837"}], "uid": "afbbdecfafcd893c4f7104365ff8935e"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "TestEllaDisableAllAiMagicBoxFeatures", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "70071cedc08d13cb", "parentUid": "696197cde92ea407d7be5d0ee1a43b1a", "status": "failed", "time": {"start": 1754452133677, "stop": 1754452133677, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "696197cde92ea407d7be5d0ee1a43b1a"}], "uid": "2b54914538eb635e492ddf923151d357"}, {"name": "test_disable_auto_pickup", "children": [{"name": "TestEllaDisableAutoPickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "740c63b3a902e9b8", "parentUid": "c20ac19fa762a9db14606dd531e82b8f", "status": "failed", "time": {"start": 1754452168780, "stop": 1754452168780, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c20ac19fa762a9db14606dd531e82b8f"}], "uid": "bf014ddab53633d98f8104639ce1bc1b"}, {"name": "test_disable_brightness_locking", "children": [{"name": "TestEllaDisableBrightnessLocking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "36e36fc2e197126e", "parentUid": "858fef1ffc1503318aa70e9c72d7b438", "status": "failed", "time": {"start": 1754452203747, "stop": 1754452203747, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "858fef1ffc1503318aa70e9c72d7b438"}], "uid": "66c800c4edcde7ca93c0c123e93d1914"}, {"name": "test_disable_call_rejection", "children": [{"name": "TestEllaDisableCallRejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "fc814aaf55d3b79b", "parentUid": "3f640e07903d6f89a09553a61d6690c3", "status": "failed", "time": {"start": 1754452238952, "stop": 1754452238952, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f640e07903d6f89a09553a61d6690c3"}], "uid": "021f51382efc25258ec8ca7ef3573495"}, {"name": "test_disable_hide_notifications", "children": [{"name": "TestEllaDisableHideNotifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "f9695098892ae325", "parentUid": "9f132b3849693b61c2ca479bcafef282", "status": "failed", "time": {"start": 1754452274094, "stop": 1754452274094, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9f132b3849693b61c2ca479bcafef282"}], "uid": "7030498aad7e0e3d901754313db42a5c"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "de97c82562aa7b51", "parentUid": "04c072a77ad52d90193c2ec6bc53a021", "status": "failed", "time": {"start": 1754452308838, "stop": 1754452308838, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04c072a77ad52d90193c2ec6bc53a021"}], "uid": "6f0070b209221eadb3d6425a33ecbff1"}, {"name": "test_disable_network_enhancement", "children": [{"name": "TestEllaDisableNetworkEnhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "d3a977ef03b1f2dc", "parentUid": "fe480ad7296777a27bc8f2d53aa0fccf", "status": "passed", "time": {"start": 1754452357295, "stop": 1754452371303, "duration": 14008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "fe480ad7296777a27bc8f2d53aa0fccf"}], "uid": "afc5376e9464fb8921d2fbcf9e0d3bbc"}, {"name": "test_disable_running_lock", "children": [{"name": "TestEllaDisableRunningLock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "7a41b675a24c217c", "parentUid": "8fe90804f66731578ca4d4ba9283a64c", "status": "passed", "time": {"start": 1754452385420, "stop": 1754452400847, "duration": 15427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8fe90804f66731578ca4d4ba9283a64c"}], "uid": "ad391be6683f94da1f0d53eaeb12f8a3"}, {"name": "test_disable_touch_optimization", "children": [{"name": "TestEllaDisableTouchOptimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "14097408c33e2291", "parentUid": "a61e1b3af122a2fe511826a7e99c2e1b", "status": "passed", "time": {"start": 1754452414652, "stop": 1754452428842, "duration": 14190}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a61e1b3af122a2fe511826a7e99c2e1b"}], "uid": "8d875ca58978089e7102b409d8215741"}, {"name": "test_disable_unfreeze", "children": [{"name": "TestEllaDisableUnfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "8f7de97d7fdf87b6", "parentUid": "1e21c84af5bdef90eb5dee6bd5cf2635", "status": "passed", "time": {"start": 1754452442992, "stop": 1754452457124, "duration": 14132}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "1e21c84af5bdef90eb5dee6bd5cf2635"}], "uid": "b1772b071366ab2c77148d9716c8dcd9"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "TestEllaDisableZonetouchMaster", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "103eff9072b05d94", "parentUid": "21d3787e184cbd6bdfc49c456af0ad04", "status": "passed", "time": {"start": 1754452471155, "stop": 1754452485017, "duration": 13862}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "21d3787e184cbd6bdfc49c456af0ad04"}], "uid": "f5c774dd2222acf0e4aa8f7b201f12b3"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "ee418c4c9a2793b3", "parentUid": "98ae41f5cdd53a2d20bf8172efd26083", "status": "passed", "time": {"start": 1754452498881, "stop": 1754452514424, "duration": 15543}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "98ae41f5cdd53a2d20bf8172efd26083"}], "uid": "314bfe9c667cd809cb0cc507bfcd14fc"}, {"name": "test_driving_mode", "children": [{"name": "TestEllaDrivingMode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "f5d600558ea29705", "parentUid": "ef0bd4110e3386c3ad0f74515d6f200e", "status": "passed", "time": {"start": 1754452528064, "stop": 1754452542007, "duration": 13943}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ef0bd4110e3386c3ad0f74515d6f200e"}], "uid": "514aae0515740321638cc56a86d0bdc5"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "TestEllaEnableAccelerateDialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "68918f24304aea29", "parentUid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7", "status": "failed", "time": {"start": 1754452555984, "stop": 1754452570723, "duration": 14739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7"}], "uid": "8c56ee95835e673fa28b34154afb4628"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "TestEllaEnableAllAiMagicBoxFeatures", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "d7da5dcb44eb19de", "parentUid": "8a587c12d6caed0b409b8971d49025ab", "status": "passed", "time": {"start": 1754452585080, "stop": 1754452598459, "duration": 13379}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8a587c12d6caed0b409b8971d49025ab"}], "uid": "68d379dd7f74dd5275f913b7eee03079"}, {"name": "test_enable_auto_pickup", "children": [{"name": "TestEllaEnableAutoPickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "c58e43308502a4be", "parentUid": "97d17abb9dc1ccc8f46a34e06a32340c", "status": "passed", "time": {"start": 1754452612143, "stop": 1754452626314, "duration": 14171}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "97d17abb9dc1ccc8f46a34e06a32340c"}], "uid": "8c4650098d6069190cfba3c757ba45db"}, {"name": "test_enable_brightness_locking", "children": [{"name": "TestEllaEnableBrightnessLocking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "c61bdf99a64855b6", "parentUid": "c400a86ee772ae2bb87a4d299fcffde3", "status": "passed", "time": {"start": 1754452639757, "stop": 1754452653831, "duration": 14074}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c400a86ee772ae2bb87a4d299fcffde3"}], "uid": "0e14ad27fc644f4f62c72d254e5d2d00"}, {"name": "test_enable_call_on_hold", "children": [{"name": "TestEllaEnableCallHold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "4d48f2b9ddae972d", "parentUid": "6d79c2b8de7e0682e35d7e5c727c1ff8", "status": "passed", "time": {"start": 1754452667250, "stop": 1754452690029, "duration": 22779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "6d79c2b8de7e0682e35d7e5c727c1ff8"}], "uid": "de334526c374af0073eb9eb6ddb0997a"}, {"name": "test_enable_call_rejection", "children": [{"name": "TestEllaEnableCallRejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "31459d3eaf00f3fc", "parentUid": "e26b43428a954a94928f7a25e76abac9", "status": "passed", "time": {"start": 1754452703528, "stop": 1754452725928, "duration": 22400}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e26b43428a954a94928f7a25e76abac9"}], "uid": "366922e4cbf42ddefd029f732cdd77bf"}, {"name": "test_enable_network_enhancement", "children": [{"name": "TestEllaEnableNetworkEnhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "80bc5ab5763925a4", "parentUid": "c12a817d493d41c56a647cb659a7bb8c", "status": "passed", "time": {"start": 1754452739108, "stop": 1754452753065, "duration": 13957}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c12a817d493d41c56a647cb659a7bb8c"}], "uid": "040b928633962c521baae26a6f7ac7c5"}, {"name": "test_enable_running_lock", "children": [{"name": "TestEllaEnableRunningLock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "2ed5255e29720bb5", "parentUid": "e1c78a96555072edad6f93b7ff33937c", "status": "passed", "time": {"start": 1754452766436, "stop": 1754452781916, "duration": 15480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e1c78a96555072edad6f93b7ff33937c"}], "uid": "26ee37ce6707d6284177cfa0f1cae12c"}, {"name": "test_enable_touch_optimization", "children": [{"name": "TestEllaEnableTouchOptimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "bef9248c1ac6d9e8", "parentUid": "d84bfce90dcde3b576c695692fca0fa8", "status": "passed", "time": {"start": 1754452795257, "stop": 1754452809330, "duration": 14073}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d84bfce90dcde3b576c695692fca0fa8"}], "uid": "76d94ee4c89c6674e70da8c74116bad6"}, {"name": "test_enable_unfreeze", "children": [{"name": "TestEllaEnableUnfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "930f24c0b46c4205", "parentUid": "c801d69cec54af644c884ba704111151", "status": "passed", "time": {"start": 1754452822505, "stop": 1754452836329, "duration": 13824}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c801d69cec54af644c884ba704111151"}], "uid": "63e313e7547561b2fa987da4d6072437"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "TestEllaEnableZonetouchMaster", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "f6f7df8d1ba4a4a1", "parentUid": "99a67c39634063a502862411f9efdb5c", "status": "passed", "time": {"start": 1754452849649, "stop": 1754452863794, "duration": 14145}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "99a67c39634063a502862411f9efdb5c"}], "uid": "796eef53680203e2ca4133277e86230b"}, {"name": "test_extend_the_image", "children": [{"name": "TestEllaExtendImage", "children": [{"name": "测试extend the image能正常执行", "uid": "f022af5522bf7597", "parentUid": "58d8f715c93e890833f08e611da6c0f8", "status": "failed", "time": {"start": 1754452877312, "stop": 1754452890938, "duration": 13626}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58d8f715c93e890833f08e611da6c0f8"}], "uid": "600af0308ad3a3f35324296fc9a30562"}, {"name": "test_fly_to_the_moon", "children": [{"name": "TestEllaFlyMoon", "children": [{"name": "测试fly to the moon返回正确的不支持响应", "uid": "ae09c15284e87680", "parentUid": "ec67c6fe6cde8082a9d0583356f5a18b", "status": "failed", "time": {"start": 1754452904530, "stop": 1754452932582, "duration": 28052}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ec67c6fe6cde8082a9d0583356f5a18b"}], "uid": "e454169bf474041ba89a09b57bcd387f"}, {"name": "test_help_me_write_an_email", "children": [{"name": "TestEllaHelpMeWriteAnEmail", "children": [{"name": "测试help me write an email能正常执行", "uid": "ea035c12f94d2ab2", "parentUid": "649615446ede0148c5ff8678d523d0aa", "status": "passed", "time": {"start": 1754452946349, "stop": 1754452961574, "duration": 15225}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "649615446ede0148c5ff8678d523d0aa"}], "uid": "4c50426c37e2427cf10466a61f135134"}, {"name": "test_help_me_write_an_thanks_email", "children": [{"name": "TestEllaHelpMeWriteAnThanksEmail", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "eb4a9b4b0831bbc", "parentUid": "532261b0e09523c5a2a4e366e160f5fd", "status": "passed", "time": {"start": 1754452974940, "stop": 1754452989831, "duration": 14891}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "532261b0e09523c5a2a4e366e160f5fd"}], "uid": "e38c35bd53b03a201c55a53b3d55b4b0"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "TestEllaHowSWeatherTodayShanghai", "children": [{"name": "测试how's the weather today in shanghai返回正确的不支持响应", "uid": "886a52d253902aed", "parentUid": "2032ac26b7aca3fee045a49c43714edc", "status": "failed", "time": {"start": 1754453003196, "stop": 1754453023848, "duration": 20652}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2032ac26b7aca3fee045a49c43714edc"}], "uid": "da155d26a6881a320918897565ffbeae"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "TestEllaHowSetScreenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "b5806b794286b92b", "parentUid": "4862cd77fa6584ab1cd401dd18e576cf", "status": "passed", "time": {"start": 1754453037409, "stop": 1754453051054, "duration": 13645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4862cd77fa6584ab1cd401dd18e576cf"}], "uid": "3af4e120133a0419124a1c704c7d3199"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "TestEllaIncreaseSettingsSpecialFunctions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "c31cf391c54c7cbe", "parentUid": "47b335b99ed8a1ee8475ca4a6834e17a", "status": "passed", "time": {"start": 1754453064336, "stop": 1754453087849, "duration": 23513}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "47b335b99ed8a1ee8475ca4a6834e17a"}], "uid": "c3601014aaa999aff69c5872e32743b7"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "TestEllaJumpAdaptiveBrightnessSettings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "8670584871649951", "parentUid": "f92d66f2cfb1510e3614f32014b90cdf", "status": "passed", "time": {"start": 1754453101441, "stop": 1754453115702, "duration": 14261}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f92d66f2cfb1510e3614f32014b90cdf"}], "uid": "eda1255cba29f83ff1461094841f0f40"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "TestEllaJumpAiWallpaperGeneratorSettings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "2803afd17204060a", "parentUid": "508a56999eea40b3b6eae200090d9ef7", "status": "passed", "time": {"start": 1754453129165, "stop": 1754453152341, "duration": 23176}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "508a56999eea40b3b6eae200090d9ef7"}], "uid": "952ea4e1b05a29ce2f17032769862fd2"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "TestEllaJumpAutoRotateScreenSettings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "4fa73543ebc54fd5", "parentUid": "923f4dfcc1ff430310299406df4a76ad", "status": "passed", "time": {"start": 1754453165713, "stop": 1754453188918, "duration": 23205}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "923f4dfcc1ff430310299406df4a76ad"}], "uid": "2885405d4e301681acf1f9073b75196b"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "TestEllaJumpBatteryPowerSaving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "656ca2d1faac87de", "parentUid": "7b0af410a8bc7729423b66dbd86394a1", "status": "passed", "time": {"start": 1754453202437, "stop": 1754453217767, "duration": 15330}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "7b0af410a8bc7729423b66dbd86394a1"}], "uid": "5e4b9eee9fae8ca49a205b0e08671105"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "TestEllaJumpBatteryUsage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "cc1c6310bce0716c", "parentUid": "e977b1d095b1f986aff6226776ded3c3", "status": "passed", "time": {"start": 1754453231758, "stop": 1754453246777, "duration": 15019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e977b1d095b1f986aff6226776ded3c3"}], "uid": "379854b415d26ca0952c7565049546e2"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "TestEllaJumpCallNotifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "1e2f8e6f0326fd85", "parentUid": "e1d634c784c8a3eaf84c787e520a993c", "status": "passed", "time": {"start": 1754453260837, "stop": 1754453283520, "duration": 22683}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e1d634c784c8a3eaf84c787e520a993c"}], "uid": "0d6d5e9fa27b112c5e4a5c892fa26625"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "TestEllaJumpHighBrightnessModeSettings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "3fc3f1d2266214de", "parentUid": "968c23cffad77650f73adb57ed35e44e", "status": "passed", "time": {"start": 1754453297139, "stop": 1754453311552, "duration": 14413}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "968c23cffad77650f73adb57ed35e44e"}], "uid": "f96efa82b36a271078aa933d212ff1e1"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "fee8f542d5d430f2", "parentUid": "6e6a65b648407dd3ea18fb96b915dace", "status": "passed", "time": {"start": 1754453325062, "stop": 1754453348609, "duration": 23547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "6e6a65b648407dd3ea18fb96b915dace"}], "uid": "a57e12d4c6edf0c5f1e60f981492cd0f"}, {"name": "test_jump_to_nfc_settings", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试jump to nfc settings", "uid": "1f1ccbe7a9a3e46f", "parentUid": "400a6941e3389b1b48d77dfa263077fe", "status": "passed", "time": {"start": 1754453361932, "stop": 1754453382979, "duration": 21047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "400a6941e3389b1b48d77dfa263077fe"}], "uid": "364aacfa906bc91f673d917736ae37e5"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "TestEllaJumpNotificationsStatusBarSettings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "1a255e5e8ebcea4c", "parentUid": "5891939ff40959b2d8e13bfa76678a88", "status": "passed", "time": {"start": 1754453396189, "stop": 1754453412199, "duration": 16010}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5891939ff40959b2d8e13bfa76678a88"}], "uid": "843f8e4d40a46af764e0186551ea2eab"}, {"name": "test_modify_grape_timbre", "children": [{"name": "TestEllaEnableRunningLock", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "b89d9ddd74405bb8", "parentUid": "79b5d194845308e1383e35ab63ba394b", "status": "passed", "time": {"start": 1754453425579, "stop": 1754453439364, "duration": 13785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "79b5d194845308e1383e35ab63ba394b"}], "uid": "ff7fe79e49919f1d7ccc98346b480d21"}, {"name": "test_more_settings", "children": [{"name": "TestEllaMoreSettings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "4a822568f803c00a", "parentUid": "5158adca762a1833ee225b49be689b4b", "status": "passed", "time": {"start": 1754453452902, "stop": 1754453469776, "duration": 16874}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5158adca762a1833ee225b49be689b4b"}], "uid": "2678afdbcf3cd1c2f7304aab68dff13e"}, {"name": "test_navigation_to_the_address_in_the_image", "children": [{"name": "TestEllaNavigationAddressTheImage", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "8fa1609791dfabde", "parentUid": "bdbc8f989cddecd7a3ff8b9229b6e0b7", "status": "passed", "time": {"start": 1754453483088, "stop": 1754453498792, "duration": 15704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdbc8f989cddecd7a3ff8b9229b6e0b7"}], "uid": "2ea8810e61f48227b2b12d8788c78cfa"}, {"name": "test_navigation_to_the_first_address_in_the_image", "children": [{"name": "TestEllaNavigationFirstAddressImage", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "b7f1fa9085faaa27", "parentUid": "fc6a08afad859df9f5da8d1611e05a0e", "status": "broken", "time": {"start": 1754453512052, "stop": 1754453526933, "duration": 14881}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fc6a08afad859df9f5da8d1611e05a0e"}], "uid": "f052d105eebf03aacc6d59b029ca356c"}, {"name": "test_open_font_family_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "e4feab036331402e", "parentUid": "3abee97ea9125b112c9768709b230d6c", "status": "passed", "time": {"start": 1754453540495, "stop": 1754453556552, "duration": 16057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3abee97ea9125b112c9768709b230d6c"}], "uid": "c37be023aa1668dd5137d98115ed22ee"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "3b9a61ca339a789e", "parentUid": "1547bf4ebc0f2064b90de7ee9d4b0d9a", "status": "passed", "time": {"start": 1754453570061, "stop": 1754453586358, "duration": 16297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "1547bf4ebc0f2064b90de7ee9d4b0d9a"}], "uid": "e9bb1812204e529485c6767282c66ee4"}, {"name": "test_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试open whatsapp", "uid": "a427bf5606042741", "parentUid": "a86f67524fc901b9e239b41a55bcd168", "status": "failed", "time": {"start": 1754453599545, "stop": 1754453614460, "duration": 14915}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a86f67524fc901b9e239b41a55bcd168"}], "uid": "134b51cd09c0f3f3f5664bfe08db4bfb"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaOrderBurger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "fe333cd2d054813e", "parentUid": "caf4b95867e28da5919f2eca0a324167", "status": "failed", "time": {"start": 1754453627864, "stop": 1754453641483, "duration": 13619}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "caf4b95867e28da5919f2eca0a324167"}], "uid": "5866702b17345a70f73c15e82c35a889"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderTakeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "2f730eec0c9ba595", "parentUid": "bee4551900f0d6522500f0c802a47f62", "status": "failed", "time": {"start": 1754453655065, "stop": 1754453668838, "duration": 13773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bee4551900f0d6522500f0c802a47f62"}], "uid": "d94ef1da6fdbed2a2a316ba39ec7f816"}, {"name": "test_parking_space", "children": [{"name": "TestEllaParkingSpace", "children": [{"name": "测试parking space能正常执行", "uid": "d4e2af9239179732", "parentUid": "8002e65cdc68fa89adcbace4cab353af", "status": "passed", "time": {"start": 1754453682401, "stop": 1754453695912, "duration": 13511}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8002e65cdc68fa89adcbace4cab353af"}], "uid": "91df28ab9c563eb71b7c27ebe03bd2a9"}, {"name": "test_play_football_video_by_youtube", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play football video by youtube", "uid": "9f2b049c8f60a56c", "parentUid": "f50015233729c567f6411101e9d7507e", "status": "passed", "time": {"start": 1754453709213, "stop": 1754453726412, "duration": 17199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f50015233729c567f6411101e9d7507e"}], "uid": "288230e0b3a90da164068733d1ce2f50"}, {"name": "test_play_love_sotry", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play love sotry", "uid": "142906c3e59f6be2", "parentUid": "2eafce82b79f0069cbd0ea652014bbfb", "status": "failed", "time": {"start": 1754453740436, "stop": 1754453761597, "duration": 21161}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2eafce82b79f0069cbd0ea652014bbfb"}], "uid": "395956abc96532bb04cf74abc316a3ba"}, {"name": "test_play_taylor_swift_s_song_love_sotry", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "21e4c9e08559b2f2", "parentUid": "369a78d30a3bb706690dc0f41716f29f", "status": "failed", "time": {"start": 1754453775856, "stop": 1754453797346, "duration": 21490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "369a78d30a3bb706690dc0f41716f29f"}], "uid": "fee77f8c40bb5ae6f21bab13e0798786"}, {"name": "test_play_the_album", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play the album", "uid": "1787807a0d09bf93", "parentUid": "1cfc5343c5c88745c65dc82ee560dd78", "status": "failed", "time": {"start": 1754453811752, "stop": 1754453833090, "duration": 21338}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1cfc5343c5c88745c65dc82ee560dd78"}], "uid": "58b703424f3437f68fd423d41eccfd46"}, {"name": "test_play_video", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play video", "uid": "dadc4cc89b23040e", "parentUid": "6e35a63e6264545436bbb6f84204721d", "status": "passed", "time": {"start": 1754453847145, "stop": 1754453862367, "duration": 15222}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "6e35a63e6264545436bbb6f84204721d"}], "uid": "0a6d47b585a70d59df4457bfeb32ef4b"}, {"name": "test_play_video_by_youtube", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play video by youtube", "uid": "f5d2d3a4bb66a858", "parentUid": "87ac2f2511f41462750c26dcbc17f7d7", "status": "passed", "time": {"start": 1754453876674, "stop": 1754453892534, "duration": 15860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "87ac2f2511f41462750c26dcbc17f7d7"}], "uid": "fe310a2b7e3c25a7a2ac071656c5ef12"}, {"name": "test_pls_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试pls open whatsapp", "uid": "9fe241edfc82795", "parentUid": "f606de62a17219d1049f95130d391334", "status": "failed", "time": {"start": 1754453906592, "stop": 1754453921460, "duration": 14868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f606de62a17219d1049f95130d391334"}], "uid": "71d89bf7be06a8672fe794f8b289d664"}, {"name": "test_redial", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试redial", "uid": "ef7a049e326a2e58", "parentUid": "73134362df5e82df9dbacec436fd51cc", "status": "failed", "time": {"start": 1754453935890, "stop": 1754453958417, "duration": 22527}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73134362df5e82df9dbacec436fd51cc"}], "uid": "ad6624cb14424d8194382f0f58d7848c"}, {"name": "test_reset_phone", "children": [{"name": "TestEllaResetPhone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "21927a811f40206b", "parentUid": "cac15668a449dfa5284ac8751a8bc088", "status": "passed", "time": {"start": 1754453972515, "stop": 1754453986298, "duration": 13783}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "cac15668a449dfa5284ac8751a8bc088"}], "uid": "20f77fb14c171a00344aefe112819e8f"}, {"name": "test_restart_my_phone", "children": [{"name": "TestEllaRestartMyPhone", "children": [{"name": "测试restart my phone能正常执行", "uid": "19c9abbc1af7f6c", "parentUid": "8e6b8a6f5b7ace7e38fe72173c083641", "status": "passed", "time": {"start": 1754454000086, "stop": 1754454014302, "duration": 14216}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8e6b8a6f5b7ace7e38fe72173c083641"}], "uid": "37ab7708b333614b029b8e7dbb1544ed"}, {"name": "test_search_for_addresses_on_the_screen", "children": [{"name": "TestEllaSearchAddressesScreen", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "aec6e1a96ee9d4f5", "parentUid": "4c52013229be65cab9dbe8706e15af5a", "status": "failed", "time": {"start": 1754454028008, "stop": 1754454043073, "duration": 15065}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c52013229be65cab9dbe8706e15af5a"}], "uid": "c2898d22ab26a5d42aa6bad2edebff1b"}, {"name": "test_search_the_address_in_the_image", "children": [{"name": "TestEllaSearchAddressImage", "children": [{"name": "测试search the address in the image能正常执行", "uid": "2df96fbe56ee275e", "parentUid": "c189e03b4adbf1ffd08ccde0c003cbf1", "status": "failed", "time": {"start": 1754454057296, "stop": 1754454072495, "duration": 15199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c189e03b4adbf1ffd08ccde0c003cbf1"}], "uid": "18c4911f1477f7e8b52e7c52b7206918"}, {"name": "test_search_whatsapp_for_me", "children": [{"name": "TestEllaSearchWhatsappMe", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "c35c0f8a3add0d33", "parentUid": "cd5851bdca5beda41724955a20608d69", "status": "failed", "time": {"start": 1754454086588, "stop": 1754454104322, "duration": 17734}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd5851bdca5beda41724955a20608d69"}], "uid": "000d1f465ed860bdf1ed56e67ee1ae93"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "TestEllaSearchingMethodViolentMurder", "children": [{"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "537462e9458a1a8a", "parentUid": "ce100ec49c328fc7bcaa8647cea22b48", "status": "failed", "time": {"start": 1754454118397, "stop": 1754454134176, "duration": 15779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ce100ec49c328fc7bcaa8647cea22b48"}], "uid": "be41a396acb219f73e244b463a77b9e4"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "TestEllaSendMyRecentPhotosMomThroughWhatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "a00dfe1c97e10826", "parentUid": "7a8b4afc50f4eff1f1bfb333ef0634a3", "status": "failed", "time": {"start": 1754454148304, "stop": 1754454164863, "duration": 16559}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7a8b4afc50f4eff1f1bfb333ef0634a3"}], "uid": "a815543064692fc926dec55bba6ffd8f"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "TestEllaSetAppAutoRotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "88ea7dfcbacf2d15", "parentUid": "5ec31d73ee5a8cbd16b741e2a0c12725", "status": "passed", "time": {"start": 1754454179151, "stop": 1754454193174, "duration": 14023}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5ec31d73ee5a8cbd16b741e2a0c12725"}], "uid": "c4e1b97dbe16d5e25caab90c80f85213"}, {"name": "test_set_app_notifications", "children": [{"name": "TestEllaSetAppNotifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "ab38d772841f6a8c", "parentUid": "e2b37df150554a2656df5f000b5f3877", "status": "passed", "time": {"start": 1754454206931, "stop": 1754454221093, "duration": 14162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e2b37df150554a2656df5f000b5f3877"}], "uid": "c477cad1f43ef2333b7849edf5ce05ed"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "TestEllaSetBatterySaverSettings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "4f99edc2b12b8b45", "parentUid": "c420f6bca101c62ba94f9e8e7f31a573", "status": "passed", "time": {"start": 1754454234998, "stop": 1754454251005, "duration": 16007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c420f6bca101c62ba94f9e8e7f31a573"}], "uid": "8b4c47455f601b3d7d6f1e100772a44a"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "TestEllaSetCallBackLastUsedSim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "f674c997733d4a85", "parentUid": "4c0f190c6f4aee497792a6d856a0035a", "status": "passed", "time": {"start": 1754454265047, "stop": 1754454287795, "duration": 22748}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4c0f190c6f4aee497792a6d856a0035a"}], "uid": "7cea4873dfe6764b25d4b6368eec9cfb"}, {"name": "test_set_color_style", "children": [{"name": "TestEllaSetColorStyle", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "b79216f6e34836ba", "parentUid": "a93fd41464aaeca8c5187ded9998a8cf", "status": "passed", "time": {"start": 1754454301881, "stop": 1754454316351, "duration": 14470}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a93fd41464aaeca8c5187ded9998a8cf"}], "uid": "ba0686e84222e058748253df8047a50b"}, {"name": "test_set_compatibility_mode", "children": [{"name": "TestEllaSetCompatibilityMode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "494274bea1d2dec1", "parentUid": "3b4b879cb25f4ec1c9e5f56f7b979c37", "status": "passed", "time": {"start": 1754454330251, "stop": 1754454343621, "duration": 13370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3b4b879cb25f4ec1c9e5f56f7b979c37"}], "uid": "1909cdae019ead080ae02a578b4d7316"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "TestEllaSetCoverScreenApps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "47576954d15fc148", "parentUid": "37a4a9b94837c06f3d0d560b86bbe29b", "status": "passed", "time": {"start": 1754454357679, "stop": 1754454371310, "duration": 13631}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "37a4a9b94837c06f3d0d560b86bbe29b"}], "uid": "5df35bd6f80d4c6657cbae8b27064aab"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "TestEllaSetCustomizedCoverScreen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "ba368628feba4062", "parentUid": "4d3fece58e4f70510cd292befe33b96a", "status": "passed", "time": {"start": 1754454385198, "stop": 1754454399279, "duration": 14081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4d3fece58e4f70510cd292befe33b96a"}], "uid": "3fa52a12d7acae5645ea3aa75477d44b"}, {"name": "test_set_date_time", "children": [{"name": "TestEllaSetDateTime", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "bee1c22929dac17d", "parentUid": "9d950448d0ff30907a7af780f114a130", "status": "passed", "time": {"start": 1754454413139, "stop": 1754454427224, "duration": 14085}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "9d950448d0ff30907a7af780f114a130"}], "uid": "8c30598a8b1f5940667edde71b30791c"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "TestEllaSetEdgeMistouchPrevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "99fd43b3cb1bc7f8", "parentUid": "dada83ec1ab33079d5f86d4ac7ee1f04", "status": "passed", "time": {"start": 1754454441043, "stop": 1754454454848, "duration": 13805}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "dada83ec1ab33079d5f86d4ac7ee1f04"}], "uid": "a30de810322f2776f8c899c615c4be77"}, {"name": "test_set_flex_still_mode", "children": [{"name": "TestEllaSetFlexStillMode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "61bf89f1acbe641e", "parentUid": "a51d316b425964f68fef53807195a46f", "status": "passed", "time": {"start": 1754454468583, "stop": 1754454482618, "duration": 14035}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a51d316b425964f68fef53807195a46f"}], "uid": "0100b1b2aa4c49467859f43e6353bedb"}, {"name": "test_set_flip_case_feature", "children": [{"name": "TestEllaSetFlipCaseFeature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "ee7c18c0f957d724", "parentUid": "d5b01634d272a358f06ff35ca4ca4a23", "status": "passed", "time": {"start": 1754454496546, "stop": 1754454509946, "duration": 13400}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d5b01634d272a358f06ff35ca4ca4a23"}], "uid": "659c6f22cfed2953c4ba8d4b8dd7294a"}, {"name": "test_set_floating_windows", "children": [{"name": "TestEllaSetFloatingWindows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "24fd0ea8e66dd24f", "parentUid": "e15e2e981396bcf4d57ba068a480c5b1", "status": "passed", "time": {"start": 1754454523765, "stop": 1754454537535, "duration": 13770}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e15e2e981396bcf4d57ba068a480c5b1"}], "uid": "1dc2787970a8d9c589a8ae300f462858"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "TestEllaSetFoldingScreenZone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "db4f0bfa32d8b233", "parentUid": "47c139cd34b0a780094ffb98e1812572", "status": "passed", "time": {"start": 1754454551661, "stop": 1754454565552, "duration": 13891}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "47c139cd34b0a780094ffb98e1812572"}], "uid": "45d2d7d6fad9b4da39cc6dac0c4fa337"}, {"name": "test_set_font_size", "children": [{"name": "TestEllaSetFontSize", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "575d4153a349b439", "parentUid": "80b6fcd56edd3e33d66094a25cc5d31b", "status": "passed", "time": {"start": 1754454579451, "stop": 1754454593442, "duration": 13991}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "80b6fcd56edd3e33d66094a25cc5d31b"}], "uid": "9a3389ccf2eca5621def99072303502f"}, {"name": "test_set_gesture_navigation", "children": [{"name": "TestEllaSetGestureNavigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "a6c15fc9b5578641", "parentUid": "5a5a307b4aee14702733cda564e2594e", "status": "passed", "time": {"start": 1754454607356, "stop": 1754454621555, "duration": 14199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5a5a307b4aee14702733cda564e2594e"}], "uid": "f45d715ad98a0594e567c013238fb055"}, {"name": "test_set_languages", "children": [{"name": "TestEllaSetLanguages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "8064999615978c60", "parentUid": "3e28dcd9a9cdb251320f0e3b7e655a4d", "status": "failed", "time": {"start": 1754454635493, "stop": 1754454650849, "duration": 15356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e28dcd9a9cdb251320f0e3b7e655a4d"}], "uid": "eedc9f2088f9e7ed345a41dcf938b3aa"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "TestEllaSetLockscreenPasswords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "76652ae732cd656", "parentUid": "41dd05458efc06498e30ee8a35ffd98f", "status": "passed", "time": {"start": 1754454664856, "stop": 1754454678949, "duration": 14093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "41dd05458efc06498e30ee8a35ffd98f"}], "uid": "62cf905659379d62ce72a50cba40e0f8"}, {"name": "test_set_my_fonts", "children": [{"name": "TestEllaSetMyFonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "c45788b86897c14e", "parentUid": "3feb62634faa8c2680759a67a0f0b2df", "status": "passed", "time": {"start": 1754454692062, "stop": 1754454705824, "duration": 13762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3feb62634faa8c2680759a67a0f0b2df"}], "uid": "64427e107c50bb3a09e1b67c0bf1e141"}, {"name": "test_set_my_themes", "children": [{"name": "TestEllaSetMyThemes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "614c61afe62ed631", "parentUid": "b86797063b9d865d350ec21ac7112b5f", "status": "passed", "time": {"start": 1754454718943, "stop": 1754454733021, "duration": 14078}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b86797063b9d865d350ec21ac7112b5f"}], "uid": "716eecd28b7d4b5ede25b5df0e5507c2"}, {"name": "test_set_parallel_windows", "children": [{"name": "TestEllaSetParallelWindows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "f42c4c84962569a", "parentUid": "e9b410d4bf477a3418fde54f839d7e6d", "status": "passed", "time": {"start": 1754454747179, "stop": 1754454761313, "duration": 14134}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e9b410d4bf477a3418fde54f839d7e6d"}], "uid": "55c6cd519ecfe75eb797fa2c66d0d75f"}, {"name": "test_set_personal_hotspot", "children": [{"name": "TestEllaSetPersonalHotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "c6a06f0569096cad", "parentUid": "b3579038871378a6fdcc9bb20849bdf1", "status": "passed", "time": {"start": 1754454775037, "stop": 1754454789125, "duration": 14088}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b3579038871378a6fdcc9bb20849bdf1"}], "uid": "b482fd86e6dab754c48a5bf619de5afb"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "TestEllaSetPhantomVPen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "3cd2aeb657255ae2", "parentUid": "7c5353b9ef36136e92a92708b7e45bb3", "status": "passed", "time": {"start": 1754454802520, "stop": 1754454816577, "duration": 14057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "7c5353b9ef36136e92a92708b7e45bb3"}], "uid": "107e8fe279ca1fd2b153352fda86fb4a"}, {"name": "test_set_phone_number", "children": [{"name": "TestEllaSetPhoneNumber", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "2d1be458cdfbd118", "parentUid": "96b22c407c943460e8486fc825169a80", "status": "passed", "time": {"start": 1754454829931, "stop": 1754454843704, "duration": 13773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "96b22c407c943460e8486fc825169a80"}], "uid": "e9cd1fa5db40d2ec604808d9c497fadb"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "TestEllaSetScheduledPowerOffRestart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "75b158406577622d", "parentUid": "58b82848dae0adadd98c95016e0b8711", "status": "passed", "time": {"start": 1754454856985, "stop": 1754454872199, "duration": 15214}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "58b82848dae0adadd98c95016e0b8711"}], "uid": "e6b0ed51c0d597afdb5bf8c4e6af53c3"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "TestEllaSetScreenRefreshRate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "71cd6d041a4b7320", "parentUid": "3e9009cde7fd47070b3529a63de695c3", "status": "passed", "time": {"start": 1754454885302, "stop": 1754454898804, "duration": 13502}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3e9009cde7fd47070b3529a63de695c3"}], "uid": "c9de8a50bcaa3dc3fd1b3a9409aa8ba0"}, {"name": "test_set_screen_relay", "children": [{"name": "TestEllaSetScreenRelay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "bd18a5f48c6a81ad", "parentUid": "61fe1e0731921d580561f77ccf2de7a2", "status": "passed", "time": {"start": 1754454912121, "stop": 1754454925915, "duration": 13794}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "61fe1e0731921d580561f77ccf2de7a2"}], "uid": "7ebda1dd475915217421f499f80df8c5"}, {"name": "test_set_screen_timeout", "children": [{"name": "TestEllaSetScreenTimeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "aa6637247543187a", "parentUid": "3f7e6a161af6de02774a99275b95e1d7", "status": "passed", "time": {"start": 1754454939113, "stop": 1754454952936, "duration": 13823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3f7e6a161af6de02774a99275b95e1d7"}], "uid": "3ad3475cd36fab63233e05c3002bb8c5"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "TestEllaSetScreenMinimumBrightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "4d4e365604efed50", "parentUid": "70cd5196189a628ba70f4e189ab3ed2a", "status": "passed", "time": {"start": 1754454966147, "stop": 1754454980412, "duration": 14265}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "70cd5196189a628ba70f4e189ab3ed2a"}], "uid": "ccff2bc4a244de015f51207ad9fb9973"}, {"name": "test_set_sim_ringtone", "children": [{"name": "TestEllaSetSimRingtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "b008d3663afb33be", "parentUid": "b540694383c716efd22be6cf332cff66", "status": "passed", "time": {"start": 1754454993624, "stop": 1754455007813, "duration": 14189}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b540694383c716efd22be6cf332cff66"}], "uid": "f521b50083932bc320d541b59f4fa12e"}, {"name": "test_set_smart_hub", "children": [{"name": "TestEllaSetSmartHub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "8b366b5074a3353e", "parentUid": "9982a7cb76e7edde540c33f77bcb4131", "status": "passed", "time": {"start": 1754455021098, "stop": 1754455034934, "duration": 13836}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "9982a7cb76e7edde540c33f77bcb4131"}], "uid": "9159365ae1f13aa7202a2ecabae352cd"}, {"name": "test_set_smart_panel", "children": [{"name": "TestEllaSetSmartPanel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "8c0d794b3e011ef9", "parentUid": "8186149225876d2c7f48b35cdec99dd3", "status": "passed", "time": {"start": 1754455048351, "stop": 1754455062107, "duration": 13756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8186149225876d2c7f48b35cdec99dd3"}], "uid": "6d2c866f4f10ac8e5f2e0399839eca9f"}, {"name": "test_set_special_function", "children": [{"name": "TestEllaSetSpecialFunction", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "247171d96cf78942", "parentUid": "a485e9dc82964f874c4ca66999bcf15a", "status": "passed", "time": {"start": 1754455075910, "stop": 1754455089646, "duration": 13736}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a485e9dc82964f874c4ca66999bcf15a"}], "uid": "1385459e098e23ca404584fba544f87b"}, {"name": "test_set_split_screen_apps", "children": [{"name": "TestEllaSetSplitScreenApps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "634f9fa2a409f31e", "parentUid": "e704a416305aeb7b8861f78da3d399fd", "status": "passed", "time": {"start": 1754455103017, "stop": 1754455116832, "duration": 13815}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e704a416305aeb7b8861f78da3d399fd"}], "uid": "18aa5f1d09e55e245cdba84af89bdb4f"}, {"name": "test_set_timezone", "children": [{"name": "TestEllaSetTimezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "613f079c90fa0578", "parentUid": "f68e969f0dc6fa17bc3cbb1c76e0468d", "status": "passed", "time": {"start": 1754455130094, "stop": 1754455144079, "duration": 13985}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f68e969f0dc6fa17bc3cbb1c76e0468d"}], "uid": "6b5e2d316297d46c06b128e02645ba18"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "TestEllaSetUltraPowerSaving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "fe566ec100eab021", "parentUid": "c853a846bf2e6118074af640a3bdfded", "status": "passed", "time": {"start": 1754455156956, "stop": 1754455171944, "duration": 14988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c853a846bf2e6118074af640a3bdfded"}], "uid": "516f9e12e9fe59099bceb66c299b4b06"}, {"name": "test_start_running", "children": [{"name": "TestEllaStartRunning", "children": [{"name": "测试start running能正常执行", "uid": "56d6727ada4cc121", "parentUid": "a24fb40874550e9588bb7a1e759255e9", "status": "failed", "time": {"start": 1754455185080, "stop": 1754455200076, "duration": 14996}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a24fb40874550e9588bb7a1e759255e9"}], "uid": "d46bf7e5557e0738a865ecc07e2d584c"}, {"name": "test_start_walking", "children": [{"name": "TestEllaStartWalking", "children": [{"name": "测试start walking能正常执行", "uid": "d66688f8f824d308", "parentUid": "c6ec8ec07b5dd2102a991e8a20680e71", "status": "failed", "time": {"start": 1754455213655, "stop": 1754455228605, "duration": 14950}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c6ec8ec07b5dd2102a991e8a20680e71"}], "uid": "a4e7eb3a60d774ac67cd2cc111cb9845"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "d97ce04890c6efba", "parentUid": "0bfb9433b5ffe1874f0d5456da733833", "status": "passed", "time": {"start": 1754455241895, "stop": 1754455255566, "duration": 13671}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "0bfb9433b5ffe1874f0d5456da733833"}], "uid": "6dfd47afc83ade6b7e64a599884aa82b"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "TestEllaSwitchPerformanceMode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "87a8c1c2510dfe13", "parentUid": "e8f8d6344d1896b53428623c96ae9737", "status": "passed", "time": {"start": 1754455268962, "stop": 1754455282666, "duration": 13704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e8f8d6344d1896b53428623c96ae9737"}], "uid": "660b4eae367594166e98133010b58c55"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchPowerSavingMode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "27ef3ceaf2e10ea4", "parentUid": "69d2b561fc70829d0f0c3bcbc22bd24b", "status": "passed", "time": {"start": 1754455296573, "stop": 1754455311690, "duration": 15117}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "69d2b561fc70829d0f0c3bcbc22bd24b"}], "uid": "89fed13c0557d811b5a98a81c18249c0"}, {"name": "test_switching_charging_speed", "children": [{"name": "TestEllaSwitchingChargingSpeed", "children": [{"name": "测试switching charging speed能正常执行", "uid": "8681f0bb149c4d9d", "parentUid": "364923db55b7a4749bd0f0a3d56b478a", "status": "passed", "time": {"start": 1754455325004, "stop": 1754455338649, "duration": 13645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "364923db55b7a4749bd0f0a3d56b478a"}], "uid": "2bc0a442c3b51933b3e020072f001bf8"}, {"name": "test_the_second", "children": [{"name": "TestEllaSecond", "children": [{"name": "测试the second返回正确的不支持响应", "uid": "a3c1f99a68a83c03", "parentUid": "ba446261c47bba6a43d497764fa39a1e", "status": "passed", "time": {"start": 1754455351920, "stop": 1754455366101, "duration": 14181}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ba446261c47bba6a43d497764fa39a1e"}], "uid": "3ef96941774e396d5f61e6c374c2a964"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "TestEllaTurnOffDrivingMode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "c85c9199aaaea251", "parentUid": "24ecd251ff3a56a4c2b20fac8ab55f2f", "status": "passed", "time": {"start": 1754455379237, "stop": 1754455392992, "duration": 13755}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "24ecd251ff3a56a4c2b20fac8ab55f2f"}], "uid": "ca08b2eb3eff172bf5d579af818a108f"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "TestEllaTurnOffShowBatteryPercentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "38d6821ea604d751", "parentUid": "a93695196bd86c4de1585781fedbe080", "status": "passed", "time": {"start": 1754455406018, "stop": 1754455420840, "duration": 14822}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a93695196bd86c4de1585781fedbe080"}], "uid": "492b5eb198aa5b8127740d2701bc4490"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "TestEllaTurnDrivingMode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "d930c37cd1055316", "parentUid": "6160fde1e3814087ef49c469733a5e59", "status": "passed", "time": {"start": 1754455434254, "stop": 1754455448097, "duration": 13843}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "6160fde1e3814087ef49c469733a5e59"}], "uid": "7dbd97d94de7ac9fc92ce34bec95b544"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "TestEllaTurnHighBrightnessMode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "bd7c1e709ec319a6", "parentUid": "afa769cc76891525f7fca4f496a4aa9e", "status": "passed", "time": {"start": 1754455462027, "stop": 1754455476062, "duration": 14035}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "afa769cc76891525f7fca4f496a4aa9e"}], "uid": "9751732cdae9d7a072c84060577699cd"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "TestEllaTurnShowBatteryPercentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "dd08e027787e7fe6", "parentUid": "9a14e86a2195f01365f057ce6c9b9e71", "status": "passed", "time": {"start": 1754455489902, "stop": 1754455504894, "duration": 14992}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "9a14e86a2195f01365f057ce6c9b9e71"}], "uid": "d5d7ce8427ad486d072c9aae7e2d5b3f"}, {"name": "test_vedio_call_number_by_whatsapp", "children": [{"name": "TestEllaVedioCallNumberWhatsapp", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "d8b9051cd846e87c", "parentUid": "0d26a41f5b9615ad2942aaedf4c3033b", "status": "failed", "time": {"start": 1754455518029, "stop": 1754455540486, "duration": 22457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d26a41f5b9615ad2942aaedf4c3033b"}], "uid": "2aeb91366c9de96caf75e843c25ff4e8"}, {"name": "test_voice_setting_page", "children": [{"name": "TestEllaVoiceSettingPage", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "7f98b929b3063854", "parentUid": "087c3d15647e1625a6f4e506048dafc7", "status": "failed", "time": {"start": 1754455553984, "stop": 1754455569893, "duration": 15909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "087c3d15647e1625a6f4e506048dafc7"}], "uid": "20224dbf8d73e834d2be6a17094deb05"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "TestEllaWhatSWheatherToday", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "6482ce3d1e710e63", "parentUid": "34ec790ead0442abc792eadbded82b45", "status": "passed", "time": {"start": 1754455583977, "stop": 1754455598021, "duration": 14044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "34ec790ead0442abc792eadbded82b45"}], "uid": "93ccf0164b324d8401f93f28368fa587"}, {"name": "test_yandex_eats", "children": [{"name": "TestEllaYandexEats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "75aab6cbe80e5a4", "parentUid": "3c01adf30480b292f5f2b75550755b66", "status": "passed", "time": {"start": 1754455612062, "stop": 1754455626406, "duration": 14344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3c01adf30480b292f5f2b75550755b66"}], "uid": "e099b3554c54efdee17091c4c12f77c7"}], "uid": "09cb3650ff0a2cc2af23d31dd3c975a2"}]}