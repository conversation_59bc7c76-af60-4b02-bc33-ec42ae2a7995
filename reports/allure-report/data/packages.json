{"uid": "83edc06c07f9ae9e47eb6dd1b683e4e2", "name": "packages", "children": [{"name": "testcases.test_ella", "children": [{"name": "component_coupling", "children": [{"name": "test_close_aivana", "children": [{"name": "测试close aivana能正常执行", "uid": "465733cfe6bd0a7f", "parentUid": "0a654e8ba5d406f4dced08e54877f4f8", "status": "passed", "time": {"start": 1754446474549, "stop": 1754446508948, "duration": 34399}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a654e8ba5d406f4dced08e54877f4f8"}, {"name": "test_close_ella", "children": [{"name": "测试close ella能正常执行", "uid": "2d3253e0c7e1606d", "parentUid": "b5a2cabf288d90878566c5ce33227175", "status": "passed", "time": {"start": 1754446522764, "stop": 1754446555185, "duration": 32421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5a2cabf288d90878566c5ce33227175"}, {"name": "test_close_folax", "children": [{"name": "测试close folax能正常执行", "uid": "9fdf4fc0d91493b8", "parentUid": "3b1f73acd32162c3bc6cd626dc7f1272", "status": "passed", "time": {"start": 1754446569173, "stop": 1754446601540, "duration": 32367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b1f73acd32162c3bc6cd626dc7f1272"}, {"name": "test_close_phonemaster", "children": [{"name": "测试close phonemaster能正常执行", "uid": "d9283c555a0dfffd", "parentUid": "e2aa70cb4f489d6ed135a8d54afbd69e", "status": "passed", "time": {"start": 1754446615376, "stop": 1754446629166, "duration": 13790}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2aa70cb4f489d6ed135a8d54afbd69e"}, {"name": "test_continue_music", "children": [{"name": "测试continue music能正常执行", "uid": "1a47f528b9b87329", "parentUid": "6a23546ce5380ec0616c9f82a57a36ad", "status": "passed", "time": {"start": 1754446642140, "stop": 1754446655684, "duration": 13544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a23546ce5380ec0616c9f82a57a36ad"}, {"name": "test_create_a_metting_schedule_at_tomorrow", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "cbf0dcc5d9990cc2", "parentUid": "6ef10c8d834a3fd9e2b41931a6d5b667", "status": "failed", "time": {"start": 1754446668758, "stop": 1754446684884, "duration": 16126}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6ef10c8d834a3fd9e2b41931a6d5b667"}, {"name": "test_delete_the_8_o_clock_alarm", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "d8177e2366c3da89", "parentUid": "744e7a2785bdcb064b4cb0104fffb47a", "status": "passed", "time": {"start": 1754446698704, "stop": 1754446716817, "duration": 18113}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "744e7a2785bdcb064b4cb0104fffb47a"}, {"name": "test_display_the_route_go_company", "children": [{"name": "测试display the route go company", "uid": "92ee4d46312fe03", "parentUid": "e22797f7f98b084598882fb5cec440fe", "status": "failed", "time": {"start": 1754446729951, "stop": 1754446745234, "duration": 15283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e22797f7f98b084598882fb5cec440fe"}, {"name": "test_my_phone_is_too_slow", "children": [{"name": "测试my phone is too slow能正常执行", "uid": "c08f5d98556cacbd", "parentUid": "9df0629aeb726d588255055629526bcb", "status": "passed", "time": {"start": 1754446758280, "stop": 1754446772851, "duration": 14571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9df0629aeb726d588255055629526bcb"}, {"name": "test_next_channel", "children": [{"name": "测试next channel能正常执行", "uid": "7a434d777ce8aa77", "parentUid": "3f23899abed5acee33c72c68b61ec080", "status": "passed", "time": {"start": 1754446786785, "stop": 1754446800286, "duration": 13501}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f23899abed5acee33c72c68b61ec080"}, {"name": "test_open_camera", "children": [{"name": "测试open camera能正常执行", "uid": "c80bc96ce82f076e", "parentUid": "470a45e0cfa4244680b09ac2a8c782a2", "status": "passed", "time": {"start": 1754446813335, "stop": 1754446831354, "duration": 18019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "470a45e0cfa4244680b09ac2a8c782a2"}, {"name": "test_open_clock", "children": [{"name": "open clock", "uid": "1f050226c3e963c2", "parentUid": "50daf9198779f130ca593a54f87ec128", "status": "passed", "time": {"start": 1754446845200, "stop": 1754446865875, "duration": 20675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "50daf9198779f130ca593a54f87ec128"}, {"name": "test_open_contact", "children": [{"name": "测试open contact命令", "uid": "650adc0ae13a6b5f", "parentUid": "c18b5d7a76803510c6e0d2868a096cc6", "status": "passed", "time": {"start": 1754446879576, "stop": 1754446902465, "duration": 22889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c18b5d7a76803510c6e0d2868a096cc6"}, {"name": "test_open_countdown", "children": [{"name": "测试open countdown能正常执行", "uid": "c2dada2a8cd202f3", "parentUid": "34dd669acbf251ecc3b4bcb8039abeb9", "status": "failed", "time": {"start": 1754446916540, "stop": 1754446930020, "duration": 13480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34dd669acbf251ecc3b4bcb8039abeb9"}, {"name": "test_open_dialer", "children": [{"name": "测试open dialer能正常执行", "uid": "17b298548e69b1bf", "parentUid": "9072ee3e27d0b6c6a2866a718c2df078", "status": "passed", "time": {"start": 1754446944051, "stop": 1754446966739, "duration": 22688}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9072ee3e27d0b6c6a2866a718c2df078"}, {"name": "test_open_ella", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "c305a7aff70443", "parentUid": "4ee54c4b3bb105e867eb3473949bd494", "status": "passed", "time": {"start": 1754446980560, "stop": 1754446993024, "duration": 12464}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ee54c4b3bb105e867eb3473949bd494"}, {"name": "test_open_folax", "children": [{"name": "测试open folax能正常执行", "uid": "2333c514af23b85d", "parentUid": "e02fc6e8f5b7a4fa147ce01474445b5b", "status": "passed", "time": {"start": 1754447006883, "stop": 1754447019377, "duration": 12494}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e02fc6e8f5b7a4fa147ce01474445b5b"}, {"name": "test_open_phone", "children": [{"name": "测试open contact命令", "uid": "2c63a150dba92152", "parentUid": "342f26d794168c8355629898d89335fe", "status": "passed", "time": {"start": 1754447033146, "stop": 1754447056127, "duration": 22981}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "342f26d794168c8355629898d89335fe"}, {"name": "test_pause_fm", "children": [{"name": "测试pause fm能正常执行", "uid": "db0ee351a160247a", "parentUid": "83a06f2be77f3b44f6a099f43b575dbf", "status": "passed", "time": {"start": 1754447070116, "stop": 1754447083848, "duration": 13732}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83a06f2be77f3b44f6a099f43b575dbf"}, {"name": "test_pause_music", "children": [{"name": "测试pause music能正常执行", "uid": "f2f0addf6616c69", "parentUid": "a3026ab8ba7d276b771465a73b8cd69f", "status": "passed", "time": {"start": 1754447097680, "stop": 1754447111256, "duration": 13576}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3026ab8ba7d276b771465a73b8cd69f"}, {"name": "test_play_afro_strut", "children": [{"name": "测试play afro strut", "uid": "8db2598fd16f9037", "parentUid": "51e1e1cd02cc1154b480c322b15c41c7", "status": "passed", "time": {"start": 1754447125376, "stop": 1754447154145, "duration": 28769}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51e1e1cd02cc1154b480c322b15c41c7"}, {"name": "test_play_jay_chou_s_music", "children": [{"name": "测试play jay chou's music", "uid": "46a3ccf5a87f0c13", "parentUid": "80ffd53a52b22963552b5b4f8ad4a69c", "status": "passed", "time": {"start": 1754447167871, "stop": 1754447191593, "duration": 23722}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80ffd53a52b22963552b5b4f8ad4a69c"}, {"name": "test_play_jay_chou_s_music_by_spotify", "children": [{"name": "测试play jay chou's music by spotify", "uid": "538bea4536324700", "parentUid": "fb3c6fdc98f3f31c0cad30d7f977b25b", "status": "failed", "time": {"start": 1754447205460, "stop": 1754447221759, "duration": 16299}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb3c6fdc98f3f31c0cad30d7f977b25b"}, {"name": "test_play_music", "children": [{"name": "测试play music", "uid": "8a51f504d5b44159", "parentUid": "012cce9a22125b4f9274376f463a21a6", "status": "passed", "time": {"start": 1754447235725, "stop": 1754447258400, "duration": 22675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "012cce9a22125b4f9274376f463a21a6"}, {"name": "test_play_rock_music", "children": [{"name": "测试play rock music", "uid": "b9b7e92debdfb117", "parentUid": "5fe073acfe44a1d8a05ea050b2df638f", "status": "passed", "time": {"start": 1754447272254, "stop": 1754447294725, "duration": 22471}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5fe073acfe44a1d8a05ea050b2df638f"}, {"name": "test_play_sun_be_song_of_jide_chord", "children": [{"name": "测试play sun be song of jide chord", "uid": "4722e4f5730d45cb", "parentUid": "e61c3229af1d310b3286353485e7dc26", "status": "passed", "time": {"start": 1754447308540, "stop": 1754447331031, "duration": 22491}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e61c3229af1d310b3286353485e7dc26"}, {"name": "test_record_audio_for_seconds", "children": [{"name": "测试record audio for 5 seconds能正常执行", "uid": "b21a04cbb6917b66", "parentUid": "cfa8b6a3fd06497697d78e23fab251dd", "status": "failed", "time": {"start": 1754447344785, "stop": 1754447359784, "duration": 14999}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfa8b6a3fd06497697d78e23fab251dd"}, {"name": "test_resume_music", "children": [{"name": "测试resume music能正常执行", "uid": "d5a5e14540c4d424", "parentUid": "0f680c0e3c91eaed54cda04d37208ed3", "status": "passed", "time": {"start": 1754447374047, "stop": 1754447387656, "duration": 13609}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "0f680c0e3c91eaed54cda04d37208ed3"}, {"name": "test_set_an_alarm_at_8_am", "children": [{"name": "测试set an alarm at 8 am", "uid": "62928b601638239b", "parentUid": "934efa6c18aa0ed520853ffbd4ef3bd7", "status": "passed", "time": {"start": 1754447401605, "stop": 1754447416889, "duration": 15284}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "934efa6c18aa0ed520853ffbd4ef3bd7"}, {"name": "test_start_run", "children": [{"name": "测试start run能正常执行", "uid": "32a5aa19059db7ec", "parentUid": "fc23e94499d12cef6b3807890ba21a3d", "status": "failed", "time": {"start": 1754447430794, "stop": 1754447456077, "duration": 25283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fc23e94499d12cef6b3807890ba21a3d"}, {"name": "test_stop_playing", "children": [{"name": "测试stop playing", "uid": "1ddf4917c0fa3e0f", "parentUid": "c598fac3e058ad96b70d8acf40dc13d8", "status": "passed", "time": {"start": 1754447470350, "stop": 1754447485362, "duration": 15012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c598fac3e058ad96b70d8acf40dc13d8"}, {"name": "test_take_a_screenshot", "children": [{"name": "测试take a screenshot能正常执行", "uid": "6443b855f11fc58", "parentUid": "c1793294d28b52937b4f0aa9a0fee952", "status": "passed", "time": {"start": 1754447499429, "stop": 1754447514675, "duration": 15246}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c1793294d28b52937b4f0aa9a0fee952"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "93130e85f011449", "parentUid": "85d39690a527a90b7d30ade2fdd4a8fc", "status": "passed", "time": {"start": 1754447528618, "stop": 1754447549421, "duration": 20803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85d39690a527a90b7d30ade2fdd4a8fc"}], "uid": "155057a0afc640ed94e8c8d7c444a680"}, {"name": "dialogue", "children": [{"name": "test_appeler_maman", "children": [{"name": "测试appeler maman能正常执行", "uid": "a99c73fcc462928a", "parentUid": "7954f72724ef95cff1433b0efbee5646", "status": "passed", "time": {"start": 1754447563860, "stop": 1754447577353, "duration": 13493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7954f72724ef95cff1433b0efbee5646"}, {"name": "test_book_a_flight_to_paris", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "cc30defe424c2046", "parentUid": "8f0bd634d6e7676a3a49d6dff49497fa", "status": "broken", "time": {"start": 1754447591529, "stop": 1754447607164, "duration": 15635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8f0bd634d6e7676a3a49d6dff49497fa"}, {"name": "test_call_mom_through_whatsapp", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "f7063b5f8370255c", "parentUid": "8541ffcf150af606538b232e3aea7434", "status": "failed", "time": {"start": 1754447622142, "stop": 1754447645193, "duration": 23051}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8541ffcf150af606538b232e3aea7434"}, {"name": "test_can_you_give_me_a_coin", "children": [{"name": "测试can you give me a coin能正常执行", "uid": "d8d80c1a8a7aa2ba", "parentUid": "7e22a70303a1dcd81523a4e5a786a8c4", "status": "passed", "time": {"start": 1754447660529, "stop": 1754447677746, "duration": 17217}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "7e22a70303a1dcd81523a4e5a786a8c4"}, {"name": "test_cannot_login_in_google_email_box", "children": [{"name": "测试cannot login in google email box能正常执行", "uid": "2eba593f4cb7e56a", "parentUid": "8c0c4d699c270397d14ca2a01dc1ee04", "status": "passed", "time": {"start": 1754447692028, "stop": 1754447706712, "duration": 14684}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c0c4d699c270397d14ca2a01dc1ee04"}, {"name": "test_check_status_updates_on_whatsapp", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "548281780cb22e82", "parentUid": "4ea0f2069633d579cb2aa7e3f4dee562", "status": "failed", "time": {"start": 1754447721944, "stop": 1754447736646, "duration": 14702}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ea0f2069633d579cb2aa7e3f4dee562"}, {"name": "test_close_whatsapp", "children": [{"name": "测试close whatsapp能正常执行", "uid": "9583fdfb28ae5b4d", "parentUid": "254be578575e3bd49816bef9d50f165f", "status": "passed", "time": {"start": 1754447750854, "stop": 1754447765708, "duration": 14854}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "254be578575e3bd49816bef9d50f165f"}, {"name": "test_could_you_please_search_an_for_me", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "4a731551f351aafd", "parentUid": "7327508f376f094b8e8fcbe0310288ab", "status": "failed", "time": {"start": 1754447779841, "stop": 1754447794688, "duration": 14847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7327508f376f094b8e8fcbe0310288ab"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "80279af86354810e", "parentUid": "3622231925f5d7d87dfc0a1200f98b66", "status": "passed", "time": {"start": 1754447808985, "stop": 1754447822724, "duration": 13739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3622231925f5d7d87dfc0a1200f98b66"}, {"name": "test_give_me_some_money", "children": [{"name": "测试give me some money能正常执行", "uid": "f137a3f44d2dcec2", "parentUid": "690fab1bc7060b99c06d337a10d44d74", "status": "passed", "time": {"start": 1754447836770, "stop": 1754447853314, "duration": 16544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "690fab1bc7060b99c06d337a10d44d74"}, {"name": "test_global_gdp_trends", "children": [{"name": "测试global gdp trends能正常执行", "uid": "46ba013c32f0a867", "parentUid": "76c541180f5aa46cc8bcc707c7d9c16f", "status": "passed", "time": {"start": 1754447867234, "stop": 1754447884006, "duration": 16772}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "76c541180f5aa46cc8bcc707c7d9c16f"}, {"name": "test_hello_hello", "children": [{"name": "测试hello hello能正常执行", "uid": "2d413365c9e8c32f", "parentUid": "b9ee9e8a38234ec69db09f78700d7fbb", "status": "passed", "time": {"start": 1754447898223, "stop": 1754447913407, "duration": 15184}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9ee9e8a38234ec69db09f78700d7fbb"}, {"name": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "children": [{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "6a31892470e25970", "parentUid": "c54c88063ac1edf2e569991ff95d16e9", "status": "passed", "time": {"start": 1754447927479, "stop": 1754447941296, "duration": 13817}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c54c88063ac1edf2e569991ff95d16e9"}, {"name": "test_hi", "children": [{"name": "测试hi能正常执行", "uid": "2cd8741868006483", "parentUid": "acfbc4c8f3f5adee7b09787ceaa2dd0d", "status": "passed", "time": {"start": 1754447955355, "stop": 1754447970221, "duration": 14866}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acfbc4c8f3f5adee7b09787ceaa2dd0d"}, {"name": "test_how_is_the_weather_today", "children": [{"name": "测试how is the weather today能正常执行", "uid": "ca70ed4533183a70", "parentUid": "35fc4938c7d094c99682c376792faffe", "status": "passed", "time": {"start": 1754447984022, "stop": 1754448004637, "duration": 20615}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "35fc4938c7d094c99682c376792faffe"}, {"name": "test_how_is_the_wheather_today", "children": [{"name": "测试how is the wheather today能正常执行", "uid": "fedcf4300a6c2948", "parentUid": "ccd640573d805b985ffb9441df6e5fe3", "status": "passed", "time": {"start": 1754448018652, "stop": 1754448032473, "duration": 13821}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccd640573d805b985ffb9441df6e5fe3"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "f8d2ce2b39404252", "parentUid": "addae1e5b6cb9e45cb30b52b0519d0df", "status": "passed", "time": {"start": 1754448046663, "stop": 1754448067498, "duration": 20835}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "addae1e5b6cb9e45cb30b52b0519d0df"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "测试how's the weather today in shanghai能正常执行", "uid": "74841191efeb3379", "parentUid": "5cbfc9081ff73a6bd530b2831a9e3384", "status": "passed", "time": {"start": 1754448081733, "stop": 1754448102779, "duration": 21046}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cbfc9081ff73a6bd530b2831a9e3384"}, {"name": "test_how_to_say_hello_in_french", "children": [{"name": "测试how to say hello in french能正常执行", "uid": "97384b55cfa58ab3", "parentUid": "58f878e12ecf6b0d82d8bb49999f4d47", "status": "passed", "time": {"start": 1754448117033, "stop": 1754448129631, "duration": 12598}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58f878e12ecf6b0d82d8bb49999f4d47"}, {"name": "test_how_to_say_i_love_you_in_french", "children": [{"name": "测试how to say i love you in french能正常执行", "uid": "50ac8cebb18698b6", "parentUid": "82fbe34286468477e9134feb14dde382", "status": "passed", "time": {"start": 1754448143739, "stop": 1754448155894, "duration": 12155}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "82fbe34286468477e9134feb14dde382"}, {"name": "test_i_wanna_be_rich", "children": [{"name": "测试i wanna be rich能正常执行", "uid": "63a2a142ebf8c9ee", "parentUid": "e8c194f64f4bc6de5e59e9c7b048cfe4", "status": "passed", "time": {"start": 1754448170003, "stop": 1754448186489, "duration": 16486}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8c194f64f4bc6de5e59e9c7b048cfe4"}, {"name": "test_i_want_to_make_a_call", "children": [{"name": "测试i want to make a call能正常执行", "uid": "9f342cefac9039d4", "parentUid": "3a5af3715ffe87f7125f4fc9d4a39a79", "status": "passed", "time": {"start": 1754448200490, "stop": 1754448222977, "duration": 22487}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3a5af3715ffe87f7125f4fc9d4a39a79"}, {"name": "test_i_want_to_watch_fireworks", "children": [{"name": "测试i want to watch fireworks能正常执行", "uid": "c178e4faa8523e73", "parentUid": "2d9bf69b53882c59bc26f9d5bf23b4c7", "status": "passed", "time": {"start": 1754448236986, "stop": 1754448253582, "duration": 16596}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d9bf69b53882c59bc26f9d5bf23b4c7"}, {"name": "test_introduce_yourself", "children": [{"name": "测试introduce yourself能正常执行", "uid": "d7d0df09dd27c956", "parentUid": "472715b25ea329acf3f408d9a469274a", "status": "passed", "time": {"start": 1754448267716, "stop": 1754448282495, "duration": 14779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "472715b25ea329acf3f408d9a469274a"}, {"name": "test_make_a_call", "children": [{"name": "测试make a call能正常执行", "uid": "c8046c28bc1e5fe8", "parentUid": "11ee179329ce36ecafe38100e52ec70d", "status": "passed", "time": {"start": 1754448296498, "stop": 1754448319012, "duration": 22514}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11ee179329ce36ecafe38100e52ec70d"}, {"name": "test_open_app", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "cab4b6ad694cf9cc", "parentUid": "c969d4a520c25845a9e33a24d4e3b7a4", "status": "passed", "time": {"start": 1754448332869, "stop": 1754448346643, "duration": 13774}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c969d4a520c25845a9e33a24d4e3b7a4"}, {"name": "test_play_news", "children": [{"name": "测试play news", "uid": "e14cda5e76258dcf", "parentUid": "0545a1fc4a946cb1c690d92255face5c", "status": "passed", "time": {"start": 1754448360692, "stop": 1754448378327, "duration": 17635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0545a1fc4a946cb1c690d92255face5c"}, {"name": "test_play_political_news", "children": [{"name": "测试play political news", "uid": "c9ad78195ec09a80", "parentUid": "350bcef11f4fb6eb99f97d3fa6fe1d47", "status": "passed", "time": {"start": 1754448392398, "stop": 1754448422888, "duration": 30490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "350bcef11f4fb6eb99f97d3fa6fe1d47"}, {"name": "test_say_hello", "children": [{"name": "测试say hello能正常执行", "uid": "d0f25b9e4d6dc09d", "parentUid": "b695b344ff456dc284b52017667bcd48", "status": "passed", "time": {"start": 1754448436849, "stop": 1754448451674, "duration": 14825}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b695b344ff456dc284b52017667bcd48"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "1cbe1f329bda06d2", "parentUid": "f3e7c7fb3655fd1be27c6d251a6f492c", "status": "broken", "time": {"start": 1754448465615, "stop": 1754448480470, "duration": 14855}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3e7c7fb3655fd1be27c6d251a6f492c"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "16c844c54a22bc45", "parentUid": "11264fcbfd285641cac64cf3c0f1f4a8", "status": "failed", "time": {"start": 1754448494705, "stop": 1754448511861, "duration": 17156}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11264fcbfd285641cac64cf3c0f1f4a8"}, {"name": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "c0c7a1cff2f3771e", "parentUid": "51407e55f461bb75e0ea1d0bcd974a5d", "status": "failed", "time": {"start": 1754448526292, "stop": 1754448541648, "duration": 15356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51407e55f461bb75e0ea1d0bcd974a5d"}, {"name": "test_show_scores_between_livepool_and_manchester_city", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "8ee2f0917f4d02b6", "parentUid": "390af3b968f43ff01d77f8b7960b3916", "status": "passed", "time": {"start": 1754448555853, "stop": 1754448583869, "duration": 28016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "390af3b968f43ff01d77f8b7960b3916"}, {"name": "test_stop_music", "children": [{"name": "测试stop music能正常执行", "uid": "4f13cb22f11a7ed0", "parentUid": "77eaeb306e34a7a9bfb4cedeba8ee35f", "status": "passed", "time": {"start": 1754448597918, "stop": 1754448611893, "duration": 13975}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "77eaeb306e34a7a9bfb4cedeba8ee35f"}, {"name": "test_stop_run", "children": [{"name": "测试stop run能正常执行", "uid": "3a66753f7b7cbcdf", "parentUid": "ccf9f3b4d1f905064aacc7d3127597b9", "status": "failed", "time": {"start": 1754448626044, "stop": 1754448654749, "duration": 28705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ccf9f3b4d1f905064aacc7d3127597b9"}, {"name": "test_stop_workout", "children": [{"name": "测试stop workout能正常执行", "uid": "1efbda1b523115da", "parentUid": "8ee49369bb9d601da7f062855654c904", "status": "failed", "time": {"start": 1754448668894, "stop": 1754448697708, "duration": 28814}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8ee49369bb9d601da7f062855654c904"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "测试summarize content on this page能正常执行", "uid": "73152c33f6262dbc", "parentUid": "b6a6c5c1cc3bbfd99be0b798596d90fd", "status": "passed", "time": {"start": 1754448712091, "stop": 1754448726036, "duration": 13945}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6a6c5c1cc3bbfd99be0b798596d90fd"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "测试summarize what i'm reading能正常执行", "uid": "b3f6ff1329da975e", "parentUid": "a593e493c32cd99871d376fe55644611", "status": "passed", "time": {"start": 1754448740309, "stop": 1754448753925, "duration": 13616}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a593e493c32cd99871d376fe55644611"}, {"name": "test_take_a_joke", "children": [{"name": "测试take a joke能正常执行", "uid": "8f31162e952b18d0", "parentUid": "a1543d72151cd8524884aba6ef4613f6", "status": "passed", "time": {"start": 1754448767966, "stop": 1754448782951, "duration": 14985}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1543d72151cd8524884aba6ef4613f6"}, {"name": "test_take_a_note_on_how_to_build_a_treehouse", "children": [{"name": "测试take a note on how to build a treehouse能正常执行", "uid": "765be39b94c2f21d", "parentUid": "218955f9fce08124722f8aa63ee5293a", "status": "passed", "time": {"start": 1754448797093, "stop": 1754448810624, "duration": 13531}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "218955f9fce08124722f8aa63ee5293a"}, {"name": "test_take_notes_on_how_to_build_a_treehouse", "children": [{"name": "测试take notes on how to build a treehouse能正常执行", "uid": "c6a43475c0c2675", "parentUid": "acd0d4c0ff78ae721b07111ac720141b", "status": "passed", "time": {"start": 1754448824871, "stop": 1754448838812, "duration": 13941}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acd0d4c0ff78ae721b07111ac720141b"}, {"name": "test_tell_me_a_joke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "b6962caf581b09dc", "parentUid": "1efd42262e16a36d4ac595eed9b7e185", "status": "failed", "time": {"start": 1754448852645, "stop": 1754448867433, "duration": 14788}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1efd42262e16a36d4ac595eed9b7e185"}, {"name": "test_video_call_mom_through_whatsapp", "children": [{"name": "测试video call mom through whatsapp能正常执行", "uid": "334b9b2e3cf14180", "parentUid": "f8daa61044336f258fd904820f097662", "status": "failed", "time": {"start": 1754448881747, "stop": 1754448903362, "duration": 21615}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8daa61044336f258fd904820f097662"}, {"name": "test_what_is_apec", "children": [{"name": "测试what is apec?能正常执行", "uid": "e5968f58913b35a0", "parentUid": "4567e57227f42cc7993892d86e523dbf", "status": "passed", "time": {"start": 1754448917614, "stop": 1754448933320, "duration": 15706}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4567e57227f42cc7993892d86e523dbf"}, {"name": "test_what_languages_do_you_support", "children": [{"name": "测试What languages do you support能正常执行", "uid": "d9d41f3b82b40b56", "parentUid": "20b964271968b9aba9b13a138825e688", "status": "passed", "time": {"start": 1754448947539, "stop": 1754448961326, "duration": 13787}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20b964271968b9aba9b13a138825e688"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "测试what's the weather like in shanghai today能正常执行", "uid": "7e37de99bed9dbf8", "parentUid": "15f8c381e13aff9342434a5db73fe066", "status": "passed", "time": {"start": 1754448975280, "stop": 1754448995678, "duration": 20398}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "15f8c381e13aff9342434a5db73fe066"}, {"name": "test_what_s_the_weather_today", "children": [{"name": "测试what·s the weather today？能正常执行", "uid": "44244c2a4bfe7443", "parentUid": "af2f763310dd868fb7293de462763b64", "status": "passed", "time": {"start": 1754449009816, "stop": 1754449030221, "duration": 20405}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "af2f763310dd868fb7293de462763b64"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "c09dbe14527ecc7c", "parentUid": "b880b898b5ae6eb8576adc4f20cdd8c6", "status": "failed", "time": {"start": 1754449044387, "stop": 1754449058301, "duration": 13914}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b880b898b5ae6eb8576adc4f20cdd8c6"}, {"name": "test_what_s_your_name", "children": [{"name": "测试what's your name？能正常执行", "uid": "bb166634eb4139ad", "parentUid": "4b64931505303134ebf07e49f3a9c0af", "status": "failed", "time": {"start": 1754449072637, "stop": 1754449086313, "duration": 13676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4b64931505303134ebf07e49f3a9c0af"}, {"name": "test_what_time_is_it_now", "children": [{"name": "测试what time is it now能正常执行", "uid": "53b8bc8221a23244", "parentUid": "6a367b30c7c610464ef1a8acfa0f93c9", "status": "passed", "time": {"start": 1754449100344, "stop": 1754449114223, "duration": 13879}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a367b30c7c610464ef1a8acfa0f93c9"}, {"name": "test_whats_the_weather_today", "children": [{"name": "测试whats the weather today能正常执行", "uid": "80bca04c580f2e26", "parentUid": "d38e79c83442fa3e23fba8b6ba34de56", "status": "passed", "time": {"start": 1754449128369, "stop": 1754449149343, "duration": 20974}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d38e79c83442fa3e23fba8b6ba34de56"}, {"name": "test_who_is_harry_potter", "children": [{"name": "测试who is harry potter能正常执行", "uid": "b030e1b3bc64bf48", "parentUid": "651e0dc624a6f14649ebc34a90907bc1", "status": "passed", "time": {"start": 1754449163121, "stop": 1754449179047, "duration": 15926}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "651e0dc624a6f14649ebc34a90907bc1"}, {"name": "test_who_is_j_k_rowling", "children": [{"name": "测试who is j k rowling能正常执行", "uid": "745e2ba6171983d5", "parentUid": "9e3d19482db247c568f3393a2db09f1f", "status": "passed", "time": {"start": 1754449193146, "stop": 1754449209426, "duration": 16280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3d19482db247c568f3393a2db09f1f"}, {"name": "test_why_is_my_phone_not_ringing_on_incoming_calls", "children": [{"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "19b5d1c854db43c5", "parentUid": "d08c8b89f0cacddcb2f61563717a9da6", "status": "passed", "time": {"start": 1754449223326, "stop": 1754449245649, "duration": 22323}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d08c8b89f0cacddcb2f61563717a9da6"}, {"name": "test_why_my_charging_is_so_slow", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "685a39e7eb19f7df", "parentUid": "015300f55fc1d98de4057611dffc7dd0", "status": "failed", "time": {"start": 1754449259853, "stop": 1754449273809, "duration": 13956}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "015300f55fc1d98de4057611dffc7dd0"}], "uid": "9c28642eff039d4ac586a58d4f4a0368"}, {"name": "open_app", "children": [{"name": "test_open_calculator", "children": [{"name": "测试open calculator", "uid": "8b898a5d16f54ad9", "parentUid": "f5c6bb2f48e4deab371c3ba132f485b1", "status": "failed", "time": {"start": 1754449288241, "stop": 1754449304334, "duration": 16093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f5c6bb2f48e4deab371c3ba132f485b1"}, {"name": "test_open_camera", "children": [{"name": "测试open camera", "uid": "9bc8f1759283437a", "parentUid": "f45fa681bd9042ada6caa7d67eff2ea3", "status": "passed", "time": {"start": 1754449318689, "stop": 1754449336910, "duration": 18221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f45fa681bd9042ada6caa7d67eff2ea3"}], "uid": "31ac50ea93bef768f953c51ba1fbe79c"}, {"name": "system_coupling", "children": [{"name": "test_adjustment_the_brightness_to", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "2847f36547f4565c", "parentUid": "f42ec0f584493c7e8f49de225d028d41", "status": "passed", "time": {"start": 1754449351011, "stop": 1754449364997, "duration": 13986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f42ec0f584493c7e8f49de225d028d41"}, {"name": "test_change_your_language_to_chinese", "children": [{"name": "测试change your language to chinese能正常执行", "uid": "e655393d16373534", "parentUid": "3ac391fa66b27ea8ccce7735db22cb6a", "status": "passed", "time": {"start": 1754449379042, "stop": 1754449391214, "duration": 12172}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3ac391fa66b27ea8ccce7735db22cb6a"}, {"name": "test_check_front_camera_information", "children": [{"name": "测试check front camera information能正常执行", "uid": "7776530c69781978", "parentUid": "a3c3dc73a42e85d88737ae9cfea06ccf", "status": "failed", "time": {"start": 1754449405309, "stop": 1754449428775, "duration": 23466}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3c3dc73a42e85d88737ae9cfea06ccf"}, {"name": "test_clear_junk_files", "children": [{"name": "测试clear junk files命令", "uid": "2b68b27e39c0eb31", "parentUid": "79b6df9a290803cad2cc59c26f69f0d5", "status": "failed", "time": {"start": 1754449442920, "stop": 1754449475136, "duration": 32216}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79b6df9a290803cad2cc59c26f69f0d5"}, {"name": "test_close_bluetooth", "children": [{"name": "测试close bluetooth能正常执行", "uid": "acc50f49b95172e6", "parentUid": "43c3e77fe7845ae372f8b29b479e9c36", "status": "failed", "time": {"start": 1754449489597, "stop": 1754449503246, "duration": 13649}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43c3e77fe7845ae372f8b29b479e9c36"}, {"name": "test_close_flashlight", "children": [{"name": "测试close flashlight能正常执行", "uid": "93ef6d13c079fd1", "parentUid": "05e7de1f6a30400f4ba09b6b927c0c5f", "status": "failed", "time": {"start": 1754449517677, "stop": 1754449532009, "duration": 14332}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05e7de1f6a30400f4ba09b6b927c0c5f"}, {"name": "test_close_wifi", "children": [{"name": "测试close wifi能正常执行", "uid": "fbeb0a397c34b803", "parentUid": "2fcb64cb23f984daec672237d94bb890", "status": "failed", "time": {"start": 1754449546506, "stop": 1754449559659, "duration": 13153}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2fcb64cb23f984daec672237d94bb890"}, {"name": "test_countdown_min", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "8712b4ac10ccc2e7", "parentUid": "5acc0b94de2365f9212477ba5756e434", "status": "failed", "time": {"start": 1754449574015, "stop": 1754449589731, "duration": 15716}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5acc0b94de2365f9212477ba5756e434"}, {"name": "test_decrease_the_brightness", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "a9782300c788ad8c", "parentUid": "71d6a5aa3a46022e7d7ccf02f396978b", "status": "failed", "time": {"start": 1754449604272, "stop": 1754449618034, "duration": 13762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71d6a5aa3a46022e7d7ccf02f396978b"}, {"name": "test_end_screen_recording", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "b3bd4eefa66cd793", "parentUid": "62823fc182e440a2772cd17c42ab4c29", "status": "failed", "time": {"start": 1754449632803, "stop": 1754449651634, "duration": 18831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "f1b11a5e7a615977", "parentUid": "62823fc182e440a2772cd17c42ab4c29", "status": "failed", "time": {"start": 1754449666049, "stop": 1754449684411, "duration": 18362}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62823fc182e440a2772cd17c42ab4c29"}, {"name": "test_help_me_take_a_long_screenshot", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "7595375f57ce60c8", "parentUid": "1349bd48475843b043511c26d6b12b24", "status": "failed", "time": {"start": 1754449699015, "stop": 1754449717676, "duration": 18661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1349bd48475843b043511c26d6b12b24"}, {"name": "test_help_me_take_a_screenshot", "children": [{"name": "测试help me take a screenshot能正常执行", "uid": "3814b093ab5b35ad", "parentUid": "f5c6c4880b5d8ab718c388b911880945", "status": "passed", "time": {"start": 1754449732216, "stop": 1754449748925, "duration": 16709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f5c6c4880b5d8ab718c388b911880945"}, {"name": "test_long_screenshot", "children": [{"name": "测试long screenshot能正常执行", "uid": "46957ed2e17cc77b", "parentUid": "eee53365e608da87e8c1a38cd7dffd82", "status": "passed", "time": {"start": 1754449763285, "stop": 1754449779835, "duration": 16550}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eee53365e608da87e8c1a38cd7dffd82"}, {"name": "test_maximum_volume", "children": [{"name": "测试maximum volume能正常执行", "uid": "8b9d6241d0992877", "parentUid": "df87e0d39fb14600720a434db404fb7b", "status": "failed", "time": {"start": 1754449793578, "stop": 1754449806906, "duration": 13328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df87e0d39fb14600720a434db404fb7b"}, {"name": "test_memory_cleanup", "children": [{"name": "测试memory cleanup能正常执行", "uid": "2ce3a0f3ed9c5dd1", "parentUid": "3060b8cc7f41dc69110dfc534e6b31d5", "status": "failed", "time": {"start": 1754449821447, "stop": 1754449849617, "duration": 28170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3060b8cc7f41dc69110dfc534e6b31d5"}, {"name": "test_minimum_volume", "children": [{"name": "测试minimum volume能正常执行", "uid": "743b780e831d526", "parentUid": "25a58f43bfaf0da153ec687fea5dcbb5", "status": "passed", "time": {"start": 1754449863875, "stop": 1754449878772, "duration": 14897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "25a58f43bfaf0da153ec687fea5dcbb5"}, {"name": "test_open_bluetooth", "children": [{"name": "测试open bluetooth", "uid": "96f699579976c495", "parentUid": "31e546db33350801c6eaef968b45b6aa", "status": "passed", "time": {"start": 1754449892699, "stop": 1754449906211, "duration": 13512}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "31e546db33350801c6eaef968b45b6aa"}, {"name": "test_open_bt", "children": [{"name": "测试open bt", "uid": "84f9ea91bdf0b93d", "parentUid": "176a0fdfd9c4144b8dfe3d66a049fda5", "status": "passed", "time": {"start": 1754449920126, "stop": 1754449933865, "duration": 13739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "176a0fdfd9c4144b8dfe3d66a049fda5"}, {"name": "test_open_flashlight", "children": [{"name": "测试open flashlight", "uid": "7b11c4ac97004f21", "parentUid": "649e9c0b88c706f3f0551679bed64e42", "status": "passed", "time": {"start": 1754449948163, "stop": 1754449964590, "duration": 16427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "649e9c0b88c706f3f0551679bed64e42"}, {"name": "test_open_wifi", "children": [{"name": "测试open wifi", "uid": "f883a50d936e4a49", "parentUid": "f9eadf40e8c6f09400b67a9959223d07", "status": "passed", "time": {"start": 1754449978649, "stop": 1754449993380, "duration": 14731}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f9eadf40e8c6f09400b67a9959223d07"}, {"name": "test_power_saving", "children": [{"name": "测试power saving能正常执行", "uid": "45a77846489fea0", "parentUid": "c06c413949d25e13cefc3d7381223a9b", "status": "passed", "time": {"start": 1754450007334, "stop": 1754450022955, "duration": 15621}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c06c413949d25e13cefc3d7381223a9b"}, {"name": "test_screen_record", "children": [{"name": "测试screen record能正常执行", "uid": "1cb174fb389581dd", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "passed", "time": {"start": 1754450037016, "stop": 1754450055449, "duration": 18433}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "5e085943d42f8b0c", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "passed", "time": {"start": 1754450069731, "stop": 1754450088254, "duration": 18523}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b54fab3751e243d1b424e37d5d287ec3"}, {"name": "test_set_battery_saver_setting", "children": [{"name": "测试set Battery Saver setting能正常执行", "uid": "21072eabbe5acafe", "parentUid": "b6e60ab86df5fa2a1124b5d765cbfe9a", "status": "passed", "time": {"start": 1754450102193, "stop": 1754450125586, "duration": 23393}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b6e60ab86df5fa2a1124b5d765cbfe9a"}, {"name": "test_smart_charge", "children": [{"name": "测试smart charge能正常执行", "uid": "409d5e47442fd23e", "parentUid": "38c8329119fb1c04106bbcd94b91ce11", "status": "failed", "time": {"start": 1754450139671, "stop": 1754450152968, "duration": 13297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "38c8329119fb1c04106bbcd94b91ce11"}, {"name": "test_start_record", "children": [{"name": "测试start record能正常执行", "uid": "803b6376429b640e", "parentUid": "9018ab08ec79118ab3c7ac811af9f4fa", "status": "passed", "time": {"start": 1754450167254, "stop": 1754450185189, "duration": 17935}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "78e2205d4e4f206c", "parentUid": "9018ab08ec79118ab3c7ac811af9f4fa", "status": "passed", "time": {"start": 1754450199489, "stop": 1754450218292, "duration": 18803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "9018ab08ec79118ab3c7ac811af9f4fa"}, {"name": "test_start_screen_recording", "children": [{"name": "测试start screen recording能正常执行", "uid": "51910e205a82d03e", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1754450232215, "stop": 1754450250336, "duration": 18121}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "36b8c2f8e2765835", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1754450264491, "stop": 1754450280445, "duration": 15954}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "11613cef42c356cf", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1754450294402, "stop": 1754450310643, "duration": 16241}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "e068de8b2837d67", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1754450325189, "stop": 1754450343632, "duration": 18443}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a083bce7f3fa2d36f16068ac034f4e62"}, {"name": "test_stop_recording", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "f46e11db85416dc1", "parentUid": "4eb0eb9df164551aeca11800ab680c92", "status": "passed", "time": {"start": 1754450357435, "stop": 1754450375374, "duration": 17939}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "262d606376ce8700", "parentUid": "4eb0eb9df164551aeca11800ab680c92", "status": "passed", "time": {"start": 1754450389334, "stop": 1754450403774, "duration": 14440}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4eb0eb9df164551aeca11800ab680c92"}, {"name": "test_switch_charging_modes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "afe93fd71bbebe1b", "parentUid": "43ff861df9a59eeef7ac3edd50740e02", "status": "failed", "time": {"start": 1754450417620, "stop": 1754450431165, "duration": 13545}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43ff861df9a59eeef7ac3edd50740e02"}, {"name": "test_switch_magic_voice_to_grace", "children": [{"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "f9f860dc66e4a117", "parentUid": "035194a70f8a76a4b76a28f704b8ff10", "status": "passed", "time": {"start": 1754450445439, "stop": 1754450459010, "duration": 13571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "035194a70f8a76a4b76a28f704b8ff10"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "bb94c3185f87279a", "parentUid": "661f1bc7755dcb73f23369637ff67460", "status": "passed", "time": {"start": 1754450472924, "stop": 1754450486952, "duration": 14028}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "661f1bc7755dcb73f23369637ff67460"}, {"name": "test_switch_to_barrage_notification", "children": [{"name": "测试Switch to Barrage Notification能正常执行", "uid": "987fe19014f79729", "parentUid": "71d9b05a5d33eaee0a7365eb0716032e", "status": "passed", "time": {"start": 1754450500684, "stop": 1754450514283, "duration": 13599}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "71d9b05a5d33eaee0a7365eb0716032e"}, {"name": "test_switch_to_default_mode", "children": [{"name": "测试switch to default mode能正常执行", "uid": "d95aec3991ba9c49", "parentUid": "62ed32eba114769fb134a432054912e4", "status": "failed", "time": {"start": 1754450528080, "stop": 1754450550941, "duration": 22861}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62ed32eba114769fb134a432054912e4"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "42ccbac9fc7e7802", "parentUid": "2d94b30b5f27ad8b3f8967a2a3fdebe3", "status": "passed", "time": {"start": 1754450565314, "stop": 1754450579258, "duration": 13944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "2d94b30b5f27ad8b3f8967a2a3fdebe3"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "70607ed82c3ddd8b", "parentUid": "561ddfab014b4de5926c248cf15d4c27", "status": "failed", "time": {"start": 1754450593240, "stop": 1754450616621, "duration": 23381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "561ddfab014b4de5926c248cf15d4c27"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "cddd64f0dce015e5", "parentUid": "99e29771f7571a2fc146c069d7b2c125", "status": "failed", "time": {"start": 1754450630749, "stop": 1754450644126, "duration": 13377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "99e29771f7571a2fc146c069d7b2c125"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "9c90db8fe9592caf", "parentUid": "e6a1e2156d8852f07854b84613f04720", "status": "failed", "time": {"start": 1754450658321, "stop": 1754450671918, "duration": 13597}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e6a1e2156d8852f07854b84613f04720"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "28a1196086132c45", "parentUid": "dd9e8f3389e8ba0ae9839c873338d760", "status": "passed", "time": {"start": 1754450686274, "stop": 1754450701168, "duration": 14894}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "dd9e8f3389e8ba0ae9839c873338d760"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "a1ae04a8c91793c9", "parentUid": "a22da6d101545dc9b0d8edfda3746b20", "status": "failed", "time": {"start": 1754450715220, "stop": 1754450728748, "duration": 13528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a22da6d101545dc9b0d8edfda3746b20"}, {"name": "test_switched_to_data_mode", "children": [{"name": "测试switched to data mode能正常执行", "uid": "4ce79f74a58dc18e", "parentUid": "2902f5b25b15010f9670892ff424d92c", "status": "passed", "time": {"start": 1754450743050, "stop": 1754450758408, "duration": 15358}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "2902f5b25b15010f9670892ff424d92c"}, {"name": "test_take_a_photo", "children": [{"name": "测试take a photo能正常执行", "uid": "c8517a0d9fe0ff9f", "parentUid": "07997292671f0c2fc7c4f6639faf371f", "status": "passed", "time": {"start": 1754450772328, "stop": 1754450802490, "duration": 30162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "07997292671f0c2fc7c4f6639faf371f"}, {"name": "test_take_a_selfie", "children": [{"name": "测试take a selfie能正常执行", "uid": "7de2c6f37b638397", "parentUid": "b76edbc7be133088ff6cc7dcf79d0688", "status": "passed", "time": {"start": 1754450816757, "stop": 1754450847988, "duration": 31231}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b76edbc7be133088ff6cc7dcf79d0688"}, {"name": "test_the_battery_of_the_mobile_phone_is_too_low", "children": [{"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "2c78a1a091cf6fe5", "parentUid": "fd16a15b2bee6fa00e607a69966a816d", "status": "passed", "time": {"start": 1754450861763, "stop": 1754450877680, "duration": 15917}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "fd16a15b2bee6fa00e607a69966a816d"}, {"name": "test_turn_down_ring_volume", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "81616d50f4291bda", "parentUid": "550486b451765b696814b7b6743f77fe", "status": "passed", "time": {"start": 1754450891563, "stop": 1754450905225, "duration": 13662}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "550486b451765b696814b7b6743f77fe"}, {"name": "test_turn_off_flashlight", "children": [{"name": "测试turn off flashlight能正常执行", "uid": "3f7579b798a46d85", "parentUid": "990086735fb4bd184a8ac5a25a5f821c", "status": "passed", "time": {"start": 1754450919306, "stop": 1754450935420, "duration": 16114}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "990086735fb4bd184a8ac5a25a5f821c"}, {"name": "test_turn_off_wifi", "children": [{"name": "测试turn off wifi能正常执行", "uid": "cd943b7f9abecf97", "parentUid": "bf0688649d24a4bfc807d28ebe171688", "status": "passed", "time": {"start": 1754450949206, "stop": 1754450963531, "duration": 14325}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "bf0688649d24a4bfc807d28ebe171688"}, {"name": "test_turn_on_bluetooth", "children": [{"name": "测试turn on bluetooth能正常执行", "uid": "1e11879944dfe296", "parentUid": "06858c4ec997c86c877ce6ab09e3bbaf", "status": "passed", "time": {"start": 1754450977331, "stop": 1754450990939, "duration": 13608}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "06858c4ec997c86c877ce6ab09e3bbaf"}, {"name": "test_turn_on_do_not_disturb_mode", "children": [{"name": "测试turn on do not disturb mode能正常执行", "uid": "780ea6e88536084c", "parentUid": "869ae35c254b699074c4a890b2986c6d", "status": "passed", "time": {"start": 1754451004913, "stop": 1754451019638, "duration": 14725}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "869ae35c254b699074c4a890b2986c6d"}, {"name": "test_turn_on_light_theme", "children": [{"name": "测试turn on light theme能正常执行", "uid": "9204d83b6b8f7fd2", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1754451033561, "stop": 1754451047165, "duration": 13604}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "9aee619060e4ff8", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1754451061346, "stop": 1754451074571, "duration": 13225}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "9684df9c422f9cf3eef3d6f0ddd6607c"}, {"name": "test_turn_on_location_services", "children": [{"name": "测试turn on location services能正常执行", "uid": "90e2769c8ce04c27", "parentUid": "13b991141a19f0fe4b82a01990c6a851", "status": "passed", "time": {"start": 1754451088417, "stop": 1754451103456, "duration": 15039}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "13b991141a19f0fe4b82a01990c6a851"}, {"name": "test_turn_on_the_flashlight", "children": [{"name": "测试turn on the flashlight能正常执行", "uid": "f6dd481761067622", "parentUid": "c5e4b9b650952bb94f49d9ea64fb9376", "status": "passed", "time": {"start": 1754451117198, "stop": 1754451133008, "duration": 15810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c5e4b9b650952bb94f49d9ea64fb9376"}, {"name": "test_turn_on_the_screen_record", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "bbf642526ea09a6", "parentUid": "5cf049f3605e39b97e2902b74b81323d", "status": "passed", "time": {"start": 1754451146850, "stop": 1754451164847, "duration": 17997}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "fe60a039ac946371", "parentUid": "5cf049f3605e39b97e2902b74b81323d", "status": "passed", "time": {"start": 1754451179149, "stop": 1754451198065, "duration": 18916}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5cf049f3605e39b97e2902b74b81323d"}, {"name": "test_turn_on_wifi", "children": [{"name": "测试turn on wifi能正常执行", "uid": "bc9a3f7b0d5c458d", "parentUid": "8f8981d5b6534e5c462a0d8cbafb4302", "status": "passed", "time": {"start": 1754451212018, "stop": 1754451226679, "duration": 14661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8f8981d5b6534e5c462a0d8cbafb4302"}, {"name": "test_wake_me_up_at_am_tomorrow", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "af6499461f251805", "parentUid": "193cbbfa30db6a41aacdad7a864e09bc", "status": "passed", "time": {"start": 1754451240559, "stop": 1754451254442, "duration": 13883}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "193cbbfa30db6a41aacdad7a864e09bc"}, {"name": "test_where_is_the_carlcare_service_outlet", "children": [{"name": "测试where is the carlcare service outlet能正常执行", "uid": "d2547e36da3e5bf7", "parentUid": "a69c8f4ca42c7f7233299fb00d064c9f", "status": "passed", "time": {"start": 1754451268361, "stop": 1754451284343, "duration": 15982}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a69c8f4ca42c7f7233299fb00d064c9f"}], "uid": "93fc5475dc5c7ad817f2b1ab5b0ac7b9"}, {"name": "third_coupling", "children": [{"name": "test_download_app", "children": [{"name": "测试download app能正常执行", "uid": "eb1da234ac691e7c", "parentUid": "b132f6b6f7a47dbe39c1fb7de000fe78", "status": "passed", "time": {"start": 1754451298290, "stop": 1754451312878, "duration": 14588}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b132f6b6f7a47dbe39c1fb7de000fe78"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball能正常执行", "uid": "46a76feb8efb0c84", "parentUid": "9ad7d52518459e93bf395586c4de03d3", "status": "passed", "time": {"start": 1754451326748, "stop": 1754451341823, "duration": 15075}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "9ad7d52518459e93bf395586c4de03d3"}, {"name": "test_download_qq", "children": [{"name": "测试download qq能正常执行", "uid": "ddc18dfac4b48d27", "parentUid": "88f428f9095db30aa10835e50fff2f44", "status": "passed", "time": {"start": 1754451355833, "stop": 1754451372839, "duration": 17006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "88f428f9095db30aa10835e50fff2f44"}, {"name": "test_find_a_restaurant_near_me", "children": [{"name": "测试find a restaurant near me能正常执行", "uid": "33c5442f97273add", "parentUid": "990b9417ef5d8284b5ed1c8f1dcb2456", "status": "passed", "time": {"start": 1754451386783, "stop": 1754451411771, "duration": 24988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "990b9417ef5d8284b5ed1c8f1dcb2456"}, {"name": "test_navigate_from_beijing_to_shanghai", "children": [{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "be1c07cf401ff3e1", "parentUid": "98910e36f666a4645d039127f271b15c", "status": "passed", "time": {"start": 1754451425856, "stop": 1754451444106, "duration": 18250}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "98910e36f666a4645d039127f271b15c"}, {"name": "test_navigate_from_to_red_square", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "6480df8a6c46a6db", "parentUid": "b5db47a2899bd2831280d03f6ab5ccde", "status": "passed", "time": {"start": 1754451457990, "stop": 1754451478299, "duration": 20309}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b5db47a2899bd2831280d03f6ab5ccde"}, {"name": "test_navigate_to_shanghai_disneyland", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "df371b5ba678804", "parentUid": "63d4dbd18e96d1791e9029023802405a", "status": "passed", "time": {"start": 1754451492189, "stop": 1754451512550, "duration": 20361}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "63d4dbd18e96d1791e9029023802405a"}, {"name": "test_navigation_to_the_lucky", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "8ba01c509e185f21", "parentUid": "26d631af39b9512dace23d786ed426bc", "status": "passed", "time": {"start": 1754451526706, "stop": 1754451543802, "duration": 17096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "26d631af39b9512dace23d786ed426bc"}, {"name": "test_open_facebook", "children": [{"name": "测试open facebook能正常执行", "uid": "253154c273af4b74", "parentUid": "28034a46d61d0cc3fef6e9ffd4db7994", "status": "passed", "time": {"start": 1754451557799, "stop": 1754451575932, "duration": 18133}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "28034a46d61d0cc3fef6e9ffd4db7994"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "199c7c5fd883ed42", "parentUid": "300a56019ec17b2ba7bcf48ab420b3c3", "status": "failed", "time": {"start": 1754451590134, "stop": 1754451604950, "duration": 14816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "300a56019ec17b2ba7bcf48ab420b3c3"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger能正常执行", "uid": "ff31feb8037e8115", "parentUid": "2f48f1343c12cc6c07d8e886b62e4c0d", "status": "failed", "time": {"start": 1754451619144, "stop": 1754451632488, "duration": 13344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2f48f1343c12cc6c07d8e886b62e4c0d"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "51c4a2ce03ff9f3b", "parentUid": "540441448375adc4953206d10f542ed2", "status": "failed", "time": {"start": 1754451646749, "stop": 1754451660700, "duration": 13951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "540441448375adc4953206d10f542ed2"}, {"name": "test_pls_open_the_newest_whatsapp_activity", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "ad34c9352740304a", "parentUid": "18df90aff668e599fc314e80b0851b3d", "status": "passed", "time": {"start": 1754451675001, "stop": 1754451690219, "duration": 15218}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "18df90aff668e599fc314e80b0851b3d"}, {"name": "test_whatsapp", "children": [{"name": "测试whatsapp能正常执行", "uid": "aaf2e2359901cf45", "parentUid": "3936daaae04788e4f3494c8c7b4dd2c4", "status": "broken", "time": {"start": 1754451704338, "stop": 1754451719424, "duration": 15086}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3936daaae04788e4f3494c8c7b4dd2c4"}], "uid": "f10231acc09c45f9430bedaedd076cbf"}, {"name": "unsupported_commands", "children": [{"name": "test_Add_the_images_and_text_on_the_screen_to_the_note", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "d691924f9e325c36", "parentUid": "b7d205f78a6f3e533fa2c219a8aae3b4", "status": "failed", "time": {"start": 1754451733535, "stop": 1754451747369, "duration": 13834}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7d205f78a6f3e533fa2c219a8aae3b4"}, {"name": "test_change_female_tone_name_voice", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "6513ea7d676f9aee", "parentUid": "e304012317c1b3a1f774488d1d79d607", "status": "failed", "time": {"start": 1754451748972, "stop": 1754451748972, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e304012317c1b3a1f774488d1d79d607"}, {"name": "test_check_battery_information", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "cc4b26a4d2f8fb86", "parentUid": "8757fd926e61f6ebb245d3d3d474c927", "status": "failed", "time": {"start": 1754451783661, "stop": 1754451783661, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8757fd926e61f6ebb245d3d3d474c927"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "f418a21c3f8bcc0d", "parentUid": "837efc09915a750bd95f96f1811eb69d", "status": "failed", "time": {"start": 1754451818320, "stop": 1754451818320, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "837efc09915a750bd95f96f1811eb69d"}, {"name": "test_check_model_information", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "b6f84103b65be0e9", "parentUid": "78831bedd8240ff30a47ed4c298163e6", "status": "failed", "time": {"start": 1754451853335, "stop": 1754451853335, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "78831bedd8240ff30a47ed4c298163e6"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "aff9de2e497af703", "parentUid": "bddf49f414a6afcdd8f3aa88c2990b73", "status": "failed", "time": {"start": 1754451888447, "stop": 1754451888447, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "bddf49f414a6afcdd8f3aa88c2990b73"}, {"name": "test_check_my_to_do_list", "children": [{"name": "测试check my to-do list能正常执行", "uid": "cb9bf1bed63324f0", "parentUid": "941e24236863d467719d0e685e22e88f", "status": "failed", "time": {"start": 1754451923380, "stop": 1754451923380, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "941e24236863d467719d0e685e22e88f"}, {"name": "test_check_rear_camera_information", "children": [{"name": "测试check rear camera information能正常执行", "uid": "1c1269d5fcc8834f", "parentUid": "0f68e4cddcec3d7808a4b93d7289e24d", "status": "failed", "time": {"start": 1754451958469, "stop": 1754451958469, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f68e4cddcec3d7808a4b93d7289e24d"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "42624ab66ae39dc7", "parentUid": "2a21ec0aa1162e7205ed4479032eec28", "status": "failed", "time": {"start": 1754451993567, "stop": 1754451993567, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a21ec0aa1162e7205ed4479032eec28"}, {"name": "test_close_performance_mode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "e27150fcafd03433", "parentUid": "95753a1df5aed1e2d1f53aaac547d09e", "status": "failed", "time": {"start": 1754452028631, "stop": 1754452028631, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "95753a1df5aed1e2d1f53aaac547d09e"}, {"name": "test_close_power_saving_mode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "c298b828e0b31f92", "parentUid": "cc7047e4d72a0d0f3090e6371b0aadaf", "status": "failed", "time": {"start": 1754452063624, "stop": 1754452063624, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cc7047e4d72a0d0f3090e6371b0aadaf"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "2e9c0e779971b6f2", "parentUid": "eb416fd31892d7985df8f4a00fa61281", "status": "failed", "time": {"start": 1754452098668, "stop": 1754452098668, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb416fd31892d7985df8f4a00fa61281"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "70071cedc08d13cb", "parentUid": "ae74f6051eb946be44a497a5345a6122", "status": "failed", "time": {"start": 1754452133677, "stop": 1754452133677, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae74f6051eb946be44a497a5345a6122"}, {"name": "test_disable_auto_pickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "740c63b3a902e9b8", "parentUid": "12db62453bb023c9292784bafa6f3501", "status": "failed", "time": {"start": 1754452168780, "stop": 1754452168780, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12db62453bb023c9292784bafa6f3501"}, {"name": "test_disable_brightness_locking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "36e36fc2e197126e", "parentUid": "3dcacd9d4311d09538f3bfd9137d3407", "status": "failed", "time": {"start": 1754452203747, "stop": 1754452203747, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dcacd9d4311d09538f3bfd9137d3407"}, {"name": "test_disable_call_rejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "fc814aaf55d3b79b", "parentUid": "2df8406295adf2f3ca8780a6fd181bd8", "status": "failed", "time": {"start": 1754452238952, "stop": 1754452238952, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2df8406295adf2f3ca8780a6fd181bd8"}, {"name": "test_disable_hide_notifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "f9695098892ae325", "parentUid": "4f012dc2e8bb211fd5ead2f1f23cd42d", "status": "failed", "time": {"start": 1754452274094, "stop": 1754452274094, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4f012dc2e8bb211fd5ead2f1f23cd42d"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "de97c82562aa7b51", "parentUid": "a6c9b5ce2532611959a8b1cf0b7abdf0", "status": "failed", "time": {"start": 1754452308838, "stop": 1754452308838, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a6c9b5ce2532611959a8b1cf0b7abdf0"}, {"name": "test_disable_network_enhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "d3a977ef03b1f2dc", "parentUid": "4c36c5378c3644d7cdbf63c08440a304", "status": "passed", "time": {"start": 1754452357295, "stop": 1754452371303, "duration": 14008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4c36c5378c3644d7cdbf63c08440a304"}, {"name": "test_disable_running_lock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "7a41b675a24c217c", "parentUid": "bdd8ac18c65aee858c82eb5de9f7b502", "status": "passed", "time": {"start": 1754452385420, "stop": 1754452400847, "duration": 15427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "bdd8ac18c65aee858c82eb5de9f7b502"}, {"name": "test_disable_touch_optimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "14097408c33e2291", "parentUid": "7777d1894ae8ebdcc0703d8d7a1d888d", "status": "passed", "time": {"start": 1754452414652, "stop": 1754452428842, "duration": 14190}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "7777d1894ae8ebdcc0703d8d7a1d888d"}, {"name": "test_disable_unfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "8f7de97d7fdf87b6", "parentUid": "f4f38e300a9eadad2128e98bc9820a33", "status": "passed", "time": {"start": 1754452442992, "stop": 1754452457124, "duration": 14132}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f4f38e300a9eadad2128e98bc9820a33"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "103eff9072b05d94", "parentUid": "b2b3f74c91ec8af12a7b70a1858cb09f", "status": "passed", "time": {"start": 1754452471155, "stop": 1754452485017, "duration": 13862}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b2b3f74c91ec8af12a7b70a1858cb09f"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "ee418c4c9a2793b3", "parentUid": "ac5ff5b664434d007d90715847bb0efa", "status": "passed", "time": {"start": 1754452498881, "stop": 1754452514424, "duration": 15543}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ac5ff5b664434d007d90715847bb0efa"}, {"name": "test_driving_mode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "f5d600558ea29705", "parentUid": "e021bf6bc9463d8e3bd9aab9475fb30a", "status": "passed", "time": {"start": 1754452528064, "stop": 1754452542007, "duration": 13943}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e021bf6bc9463d8e3bd9aab9475fb30a"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "68918f24304aea29", "parentUid": "29814f9b6b5bad3852e86d186b662dab", "status": "failed", "time": {"start": 1754452555984, "stop": 1754452570723, "duration": 14739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29814f9b6b5bad3852e86d186b662dab"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "d7da5dcb44eb19de", "parentUid": "9b7a1bd9dfc15c1c490b49730b3b06c6", "status": "passed", "time": {"start": 1754452585080, "stop": 1754452598459, "duration": 13379}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "9b7a1bd9dfc15c1c490b49730b3b06c6"}, {"name": "test_enable_auto_pickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "c58e43308502a4be", "parentUid": "34950724c1fa063bc98eef873eb35771", "status": "passed", "time": {"start": 1754452612143, "stop": 1754452626314, "duration": 14171}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "34950724c1fa063bc98eef873eb35771"}, {"name": "test_enable_brightness_locking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "c61bdf99a64855b6", "parentUid": "fb01cd9d24d4da8385ec6db44b9bf596", "status": "passed", "time": {"start": 1754452639757, "stop": 1754452653831, "duration": 14074}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "fb01cd9d24d4da8385ec6db44b9bf596"}, {"name": "test_enable_call_on_hold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "4d48f2b9ddae972d", "parentUid": "743e0a22021355320010c1959207f737", "status": "passed", "time": {"start": 1754452667250, "stop": 1754452690029, "duration": 22779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "743e0a22021355320010c1959207f737"}, {"name": "test_enable_call_rejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "31459d3eaf00f3fc", "parentUid": "a7a8abf2aae8cc5f45b540e4f777f037", "status": "passed", "time": {"start": 1754452703528, "stop": 1754452725928, "duration": 22400}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a7a8abf2aae8cc5f45b540e4f777f037"}, {"name": "test_enable_network_enhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "80bc5ab5763925a4", "parentUid": "ecc995aaaa028fb08ab7ae1ab0e7875d", "status": "passed", "time": {"start": 1754452739108, "stop": 1754452753065, "duration": 13957}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ecc995aaaa028fb08ab7ae1ab0e7875d"}, {"name": "test_enable_running_lock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "2ed5255e29720bb5", "parentUid": "d576b618126fc7256f34985c398c1d3b", "status": "passed", "time": {"start": 1754452766436, "stop": 1754452781916, "duration": 15480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d576b618126fc7256f34985c398c1d3b"}, {"name": "test_enable_touch_optimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "bef9248c1ac6d9e8", "parentUid": "8480a7d7a44bc6838b96d68a4c18aa05", "status": "passed", "time": {"start": 1754452795257, "stop": 1754452809330, "duration": 14073}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8480a7d7a44bc6838b96d68a4c18aa05"}, {"name": "test_enable_unfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "930f24c0b46c4205", "parentUid": "66a93da14003d58b4dfb0006f98aa4c5", "status": "passed", "time": {"start": 1754452822505, "stop": 1754452836329, "duration": 13824}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "66a93da14003d58b4dfb0006f98aa4c5"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "f6f7df8d1ba4a4a1", "parentUid": "8c6899d4ddce4d928d89f60cb07aeea9", "status": "passed", "time": {"start": 1754452849649, "stop": 1754452863794, "duration": 14145}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8c6899d4ddce4d928d89f60cb07aeea9"}, {"name": "test_extend_the_image", "children": [{"name": "测试extend the image能正常执行", "uid": "f022af5522bf7597", "parentUid": "656e7206a16e23ae119789777b609183", "status": "failed", "time": {"start": 1754452877312, "stop": 1754452890938, "duration": 13626}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "656e7206a16e23ae119789777b609183"}, {"name": "test_fly_to_the_moon", "children": [{"name": "测试fly to the moon返回正确的不支持响应", "uid": "ae09c15284e87680", "parentUid": "e82041b01d221ae41a561ef7f7808128", "status": "failed", "time": {"start": 1754452904530, "stop": 1754452932582, "duration": 28052}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e82041b01d221ae41a561ef7f7808128"}, {"name": "test_help_me_write_an_email", "children": [{"name": "测试help me write an email能正常执行", "uid": "ea035c12f94d2ab2", "parentUid": "ef47f41b350d73a6140e48381c877229", "status": "passed", "time": {"start": 1754452946349, "stop": 1754452961574, "duration": 15225}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef47f41b350d73a6140e48381c877229"}, {"name": "test_help_me_write_an_thanks_email", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "eb4a9b4b0831bbc", "parentUid": "b693f3729a9fcd922c000405462cc41b", "status": "passed", "time": {"start": 1754452974940, "stop": 1754452989831, "duration": 14891}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b693f3729a9fcd922c000405462cc41b"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "测试how's the weather today in shanghai返回正确的不支持响应", "uid": "886a52d253902aed", "parentUid": "df9737d8d66e5daf52e3b706adc10a84", "status": "failed", "time": {"start": 1754453003196, "stop": 1754453023848, "duration": 20652}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df9737d8d66e5daf52e3b706adc10a84"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "b5806b794286b92b", "parentUid": "98125eb3c0eb347bf93f6ebbb14e8847", "status": "passed", "time": {"start": 1754453037409, "stop": 1754453051054, "duration": 13645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "98125eb3c0eb347bf93f6ebbb14e8847"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "c31cf391c54c7cbe", "parentUid": "849c4d59d6ccd19947474d581af3ccb2", "status": "passed", "time": {"start": 1754453064336, "stop": 1754453087849, "duration": 23513}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "849c4d59d6ccd19947474d581af3ccb2"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "8670584871649951", "parentUid": "0355ddc9170b8d072304068f8868f806", "status": "passed", "time": {"start": 1754453101441, "stop": 1754453115702, "duration": 14261}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "0355ddc9170b8d072304068f8868f806"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "2803afd17204060a", "parentUid": "fb66326ff650d5ed24178fe23a3ec6c7", "status": "passed", "time": {"start": 1754453129165, "stop": 1754453152341, "duration": 23176}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "fb66326ff650d5ed24178fe23a3ec6c7"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "4fa73543ebc54fd5", "parentUid": "4868992c324cca3aa80f892f5012edcc", "status": "passed", "time": {"start": 1754453165713, "stop": 1754453188918, "duration": 23205}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4868992c324cca3aa80f892f5012edcc"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "656ca2d1faac87de", "parentUid": "c39df595d254493d9e1cf180846ee18a", "status": "passed", "time": {"start": 1754453202437, "stop": 1754453217767, "duration": 15330}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c39df595d254493d9e1cf180846ee18a"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "cc1c6310bce0716c", "parentUid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9", "status": "passed", "time": {"start": 1754453231758, "stop": 1754453246777, "duration": 15019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "1e2f8e6f0326fd85", "parentUid": "5a05d3bb6cee7f72819e49d4f6d5ee1f", "status": "passed", "time": {"start": 1754453260837, "stop": 1754453283520, "duration": 22683}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5a05d3bb6cee7f72819e49d4f6d5ee1f"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "3fc3f1d2266214de", "parentUid": "4a8b87bfde2b2e8ce7967634be5288ab", "status": "passed", "time": {"start": 1754453297139, "stop": 1754453311552, "duration": 14413}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4a8b87bfde2b2e8ce7967634be5288ab"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "fee8f542d5d430f2", "parentUid": "cfbc1116cbdd7d5906a09ffc432b57db", "status": "passed", "time": {"start": 1754453325062, "stop": 1754453348609, "duration": 23547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "cfbc1116cbdd7d5906a09ffc432b57db"}, {"name": "test_jump_to_nfc_settings", "children": [{"name": "测试jump to nfc settings", "uid": "1f1ccbe7a9a3e46f", "parentUid": "0285fb88f545482d120dd7eeddef468a", "status": "passed", "time": {"start": 1754453361932, "stop": 1754453382979, "duration": 21047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "0285fb88f545482d120dd7eeddef468a"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "1a255e5e8ebcea4c", "parentUid": "f6098b8dc4a6271dec4f5e5f57bd4752", "status": "passed", "time": {"start": 1754453396189, "stop": 1754453412199, "duration": 16010}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f6098b8dc4a6271dec4f5e5f57bd4752"}, {"name": "test_modify_grape_timbre", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "b89d9ddd74405bb8", "parentUid": "09910b981ee980bf81421c1efd746a52", "status": "passed", "time": {"start": 1754453425579, "stop": 1754453439364, "duration": 13785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "09910b981ee980bf81421c1efd746a52"}, {"name": "test_more_settings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "4a822568f803c00a", "parentUid": "ca0b29ba7554057ab3ba8216b6b1f0ba", "status": "passed", "time": {"start": 1754453452902, "stop": 1754453469776, "duration": 16874}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ca0b29ba7554057ab3ba8216b6b1f0ba"}, {"name": "test_navigation_to_the_address_in_the_image", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "8fa1609791dfabde", "parentUid": "6e2b1fe773a9723006e50fc18b054a32", "status": "passed", "time": {"start": 1754453483088, "stop": 1754453498792, "duration": 15704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e2b1fe773a9723006e50fc18b054a32"}, {"name": "test_navigation_to_the_first_address_in_the_image", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "b7f1fa9085faaa27", "parentUid": "930b7b34cf2d90f55ec5996ae313b76b", "status": "broken", "time": {"start": 1754453512052, "stop": 1754453526933, "duration": 14881}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "930b7b34cf2d90f55ec5996ae313b76b"}, {"name": "test_open_font_family_settings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "e4feab036331402e", "parentUid": "5d1c4a01c8558acb00bb5c6e528035a3", "status": "passed", "time": {"start": 1754453540495, "stop": 1754453556552, "duration": 16057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5d1c4a01c8558acb00bb5c6e528035a3"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "3b9a61ca339a789e", "parentUid": "309e531c38122a270a22145c2b2f4a8a", "status": "passed", "time": {"start": 1754453570061, "stop": 1754453586358, "duration": 16297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "309e531c38122a270a22145c2b2f4a8a"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "a427bf5606042741", "parentUid": "fae42569d3565f4a0509b1abe481814e", "status": "failed", "time": {"start": 1754453599545, "stop": 1754453614460, "duration": 14915}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fae42569d3565f4a0509b1abe481814e"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "fe333cd2d054813e", "parentUid": "12a853573a3ed32b45369299151ae46b", "status": "failed", "time": {"start": 1754453627864, "stop": 1754453641483, "duration": 13619}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12a853573a3ed32b45369299151ae46b"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "2f730eec0c9ba595", "parentUid": "6116229fe40039026544aa05527a4376", "status": "failed", "time": {"start": 1754453655065, "stop": 1754453668838, "duration": 13773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6116229fe40039026544aa05527a4376"}, {"name": "test_parking_space", "children": [{"name": "测试parking space能正常执行", "uid": "d4e2af9239179732", "parentUid": "d235978673eeb20a0aa5a17d0b9af2fb", "status": "passed", "time": {"start": 1754453682401, "stop": 1754453695912, "duration": 13511}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d235978673eeb20a0aa5a17d0b9af2fb"}, {"name": "test_play_football_video_by_youtube", "children": [{"name": "测试play football video by youtube", "uid": "9f2b049c8f60a56c", "parentUid": "1b38b53340d3cf404c7d3184db4b3767", "status": "passed", "time": {"start": 1754453709213, "stop": 1754453726412, "duration": 17199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "1b38b53340d3cf404c7d3184db4b3767"}, {"name": "test_play_love_sotry", "children": [{"name": "测试play love sotry", "uid": "142906c3e59f6be2", "parentUid": "3279616bb2594ec7b639e82e288080c3", "status": "failed", "time": {"start": 1754453740436, "stop": 1754453761597, "duration": 21161}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3279616bb2594ec7b639e82e288080c3"}, {"name": "test_play_taylor_swift_s_song_love_sotry", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "21e4c9e08559b2f2", "parentUid": "7c3b9cf1e80a1543e429a21b6580f11f", "status": "failed", "time": {"start": 1754453775856, "stop": 1754453797346, "duration": 21490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c3b9cf1e80a1543e429a21b6580f11f"}, {"name": "test_play_the_album", "children": [{"name": "测试play the album", "uid": "1787807a0d09bf93", "parentUid": "96bab08553e379e0c0029cca77667f8f", "status": "failed", "time": {"start": 1754453811752, "stop": 1754453833090, "duration": 21338}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96bab08553e379e0c0029cca77667f8f"}, {"name": "test_play_video", "children": [{"name": "测试play video", "uid": "dadc4cc89b23040e", "parentUid": "a38748b3b1f409e94ff601029a63f98d", "status": "passed", "time": {"start": 1754453847145, "stop": 1754453862367, "duration": 15222}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "a38748b3b1f409e94ff601029a63f98d"}, {"name": "test_play_video_by_youtube", "children": [{"name": "测试play video by youtube", "uid": "f5d2d3a4bb66a858", "parentUid": "c7f371ce0f7eeb044f6c854937a7502b", "status": "passed", "time": {"start": 1754453876674, "stop": 1754453892534, "duration": 15860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c7f371ce0f7eeb044f6c854937a7502b"}, {"name": "test_pls_open_whatsapp", "children": [{"name": "测试pls open whatsapp", "uid": "9fe241edfc82795", "parentUid": "3dc012d4a4449a7636b0642c22bceb9e", "status": "failed", "time": {"start": 1754453906592, "stop": 1754453921460, "duration": 14868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dc012d4a4449a7636b0642c22bceb9e"}, {"name": "test_redial", "children": [{"name": "测试redial", "uid": "ef7a049e326a2e58", "parentUid": "494d3e1b731af3bce21d72c38e17e357", "status": "failed", "time": {"start": 1754453935890, "stop": 1754453958417, "duration": 22527}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "494d3e1b731af3bce21d72c38e17e357"}, {"name": "test_reset_phone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "21927a811f40206b", "parentUid": "97792796a4c38ae78b9db124406767b2", "status": "passed", "time": {"start": 1754453972515, "stop": 1754453986298, "duration": 13783}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "97792796a4c38ae78b9db124406767b2"}, {"name": "test_restart_my_phone", "children": [{"name": "测试restart my phone能正常执行", "uid": "19c9abbc1af7f6c", "parentUid": "c1ea683fb9d174ed7b5f8880d961bd37", "status": "passed", "time": {"start": 1754454000086, "stop": 1754454014302, "duration": 14216}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c1ea683fb9d174ed7b5f8880d961bd37"}, {"name": "test_search_for_addresses_on_the_screen", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "aec6e1a96ee9d4f5", "parentUid": "735baa4624c52854b8a2a73abb447f23", "status": "failed", "time": {"start": 1754454028008, "stop": 1754454043073, "duration": 15065}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "735baa4624c52854b8a2a73abb447f23"}, {"name": "test_search_the_address_in_the_image", "children": [{"name": "测试search the address in the image能正常执行", "uid": "2df96fbe56ee275e", "parentUid": "cba35d4f03acbacfc1c9c3b0792e089b", "status": "failed", "time": {"start": 1754454057296, "stop": 1754454072495, "duration": 15199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cba35d4f03acbacfc1c9c3b0792e089b"}, {"name": "test_search_whatsapp_for_me", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "c35c0f8a3add0d33", "parentUid": "7c7aef6b5854cef222cfb44ffc5056fe", "status": "failed", "time": {"start": 1754454086588, "stop": 1754454104322, "duration": 17734}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c7aef6b5854cef222cfb44ffc5056fe"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "537462e9458a1a8a", "parentUid": "1445db3159fb5e02b25911ddf53e9078", "status": "failed", "time": {"start": 1754454118397, "stop": 1754454134176, "duration": 15779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1445db3159fb5e02b25911ddf53e9078"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "a00dfe1c97e10826", "parentUid": "7904446ab7fbbc082d949ad5dd1566fc", "status": "failed", "time": {"start": 1754454148304, "stop": 1754454164863, "duration": 16559}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7904446ab7fbbc082d949ad5dd1566fc"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "88ea7dfcbacf2d15", "parentUid": "42a0d4edd729031d01ed4ce54f32a86f", "status": "passed", "time": {"start": 1754454179151, "stop": 1754454193174, "duration": 14023}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "42a0d4edd729031d01ed4ce54f32a86f"}, {"name": "test_set_app_notifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "ab38d772841f6a8c", "parentUid": "f17166693c717b681b5c4ace219a5254", "status": "passed", "time": {"start": 1754454206931, "stop": 1754454221093, "duration": 14162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f17166693c717b681b5c4ace219a5254"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "4f99edc2b12b8b45", "parentUid": "90688cbd93c016ee7d407e660ac88b04", "status": "passed", "time": {"start": 1754454234998, "stop": 1754454251005, "duration": 16007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "90688cbd93c016ee7d407e660ac88b04"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "f674c997733d4a85", "parentUid": "27c7eefc61a4f26939cc8e62add52a75", "status": "passed", "time": {"start": 1754454265047, "stop": 1754454287795, "duration": 22748}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "27c7eefc61a4f26939cc8e62add52a75"}, {"name": "test_set_color_style", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "b79216f6e34836ba", "parentUid": "4a9eb6eed41e8d978e3f2271d25aeeb9", "status": "passed", "time": {"start": 1754454301881, "stop": 1754454316351, "duration": 14470}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4a9eb6eed41e8d978e3f2271d25aeeb9"}, {"name": "test_set_compatibility_mode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "494274bea1d2dec1", "parentUid": "71f9d2696419dde9e2fbc887cd35352f", "status": "passed", "time": {"start": 1754454330251, "stop": 1754454343621, "duration": 13370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "71f9d2696419dde9e2fbc887cd35352f"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "47576954d15fc148", "parentUid": "3e469638b07e0f748f137f566308ee39", "status": "passed", "time": {"start": 1754454357679, "stop": 1754454371310, "duration": 13631}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "3e469638b07e0f748f137f566308ee39"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "ba368628feba4062", "parentUid": "681063f39b4b3d4670516dc945bc7e8f", "status": "passed", "time": {"start": 1754454385198, "stop": 1754454399279, "duration": 14081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "681063f39b4b3d4670516dc945bc7e8f"}, {"name": "test_set_date_time", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "bee1c22929dac17d", "parentUid": "55afb5474f6367c035d3c3b4c5aa4a0e", "status": "passed", "time": {"start": 1754454413139, "stop": 1754454427224, "duration": 14085}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "55afb5474f6367c035d3c3b4c5aa4a0e"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "99fd43b3cb1bc7f8", "parentUid": "d063298b5e7b7ba1dd78f28933196633", "status": "passed", "time": {"start": 1754454441043, "stop": 1754454454848, "duration": 13805}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d063298b5e7b7ba1dd78f28933196633"}, {"name": "test_set_flex_still_mode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "61bf89f1acbe641e", "parentUid": "fb37e27acd491ebde3cb6675b3ff4b33", "status": "passed", "time": {"start": 1754454468583, "stop": 1754454482618, "duration": 14035}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "fb37e27acd491ebde3cb6675b3ff4b33"}, {"name": "test_set_flip_case_feature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "ee7c18c0f957d724", "parentUid": "d093123b5193b9aead35dd1996c7cd1a", "status": "passed", "time": {"start": 1754454496546, "stop": 1754454509946, "duration": 13400}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d093123b5193b9aead35dd1996c7cd1a"}, {"name": "test_set_floating_windows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "24fd0ea8e66dd24f", "parentUid": "260060301e90f5edbed593882a5fec0d", "status": "passed", "time": {"start": 1754454523765, "stop": 1754454537535, "duration": 13770}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "260060301e90f5edbed593882a5fec0d"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "db4f0bfa32d8b233", "parentUid": "d7d6eb8f64580ac8b8fb024a954198fa", "status": "passed", "time": {"start": 1754454551661, "stop": 1754454565552, "duration": 13891}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d7d6eb8f64580ac8b8fb024a954198fa"}, {"name": "test_set_font_size", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "575d4153a349b439", "parentUid": "740d4f9856207354991fc052a0633f21", "status": "passed", "time": {"start": 1754454579451, "stop": 1754454593442, "duration": 13991}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "740d4f9856207354991fc052a0633f21"}, {"name": "test_set_gesture_navigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "a6c15fc9b5578641", "parentUid": "47320f9ee2ac872ac9be9e52d1af28d8", "status": "passed", "time": {"start": 1754454607356, "stop": 1754454621555, "duration": 14199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "47320f9ee2ac872ac9be9e52d1af28d8"}, {"name": "test_set_languages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "8064999615978c60", "parentUid": "46ab090271dad35a2f22e1aa891d167d", "status": "failed", "time": {"start": 1754454635493, "stop": 1754454650849, "duration": 15356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "46ab090271dad35a2f22e1aa891d167d"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "76652ae732cd656", "parentUid": "1ebb1fb5844374f960b3ecd76cddec92", "status": "passed", "time": {"start": 1754454664856, "stop": 1754454678949, "duration": 14093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "1ebb1fb5844374f960b3ecd76cddec92"}, {"name": "test_set_my_fonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "c45788b86897c14e", "parentUid": "c63b678cbbe7de452848a070298e3fab", "status": "passed", "time": {"start": 1754454692062, "stop": 1754454705824, "duration": 13762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "c63b678cbbe7de452848a070298e3fab"}, {"name": "test_set_my_themes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "614c61afe62ed631", "parentUid": "452ed970a52e7ee9049bbdb424f9b57d", "status": "passed", "time": {"start": 1754454718943, "stop": 1754454733021, "duration": 14078}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "452ed970a52e7ee9049bbdb424f9b57d"}, {"name": "test_set_parallel_windows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "f42c4c84962569a", "parentUid": "b7b2179becb10c93603c2fcce15e0006", "status": "passed", "time": {"start": 1754454747179, "stop": 1754454761313, "duration": 14134}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "b7b2179becb10c93603c2fcce15e0006"}, {"name": "test_set_personal_hotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "c6a06f0569096cad", "parentUid": "4fed48034322a5d5119c937240c4aa77", "status": "passed", "time": {"start": 1754454775037, "stop": 1754454789125, "duration": 14088}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "4fed48034322a5d5119c937240c4aa77"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "3cd2aeb657255ae2", "parentUid": "7b8efe12e2cc4a1e4fec01a67e1ead0d", "status": "passed", "time": {"start": 1754454802520, "stop": 1754454816577, "duration": 14057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "7b8efe12e2cc4a1e4fec01a67e1ead0d"}, {"name": "test_set_phone_number", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "2d1be458cdfbd118", "parentUid": "f0d8d3a4b043370c607b0bb64b8df1bd", "status": "passed", "time": {"start": 1754454829931, "stop": 1754454843704, "duration": 13773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "f0d8d3a4b043370c607b0bb64b8df1bd"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "75b158406577622d", "parentUid": "2095cb76657298bca8aa08a5ddc4452c", "status": "passed", "time": {"start": 1754454856985, "stop": 1754454872199, "duration": 15214}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "2095cb76657298bca8aa08a5ddc4452c"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "71cd6d041a4b7320", "parentUid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b", "status": "passed", "time": {"start": 1754454885302, "stop": 1754454898804, "duration": 13502}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b"}, {"name": "test_set_screen_relay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "bd18a5f48c6a81ad", "parentUid": "66dc5bdcc3e27c2ae07a1536c41488cd", "status": "passed", "time": {"start": 1754454912121, "stop": 1754454925915, "duration": 13794}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "66dc5bdcc3e27c2ae07a1536c41488cd"}, {"name": "test_set_screen_timeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "aa6637247543187a", "parentUid": "efb5e97f214d22c0692101b1024ecde2", "status": "passed", "time": {"start": 1754454939113, "stop": 1754454952936, "duration": 13823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "efb5e97f214d22c0692101b1024ecde2"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "4d4e365604efed50", "parentUid": "e1c10086104522b2fbc3cd552709a125", "status": "passed", "time": {"start": 1754454966147, "stop": 1754454980412, "duration": 14265}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "e1c10086104522b2fbc3cd552709a125"}, {"name": "test_set_sim_ringtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "b008d3663afb33be", "parentUid": "02b906bfcb368697dfb809905922e9cb", "status": "passed", "time": {"start": 1754454993624, "stop": 1754455007813, "duration": 14189}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "02b906bfcb368697dfb809905922e9cb"}, {"name": "test_set_smart_hub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "8b366b5074a3353e", "parentUid": "1b885e8f75c6cb8827815cfc72755c55", "status": "passed", "time": {"start": 1754455021098, "stop": 1754455034934, "duration": 13836}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "1b885e8f75c6cb8827815cfc72755c55"}, {"name": "test_set_smart_panel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "8c0d794b3e011ef9", "parentUid": "116ddf2803a2d3fa8977f8f2b43e59a3", "status": "passed", "time": {"start": 1754455048351, "stop": 1754455062107, "duration": 13756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "116ddf2803a2d3fa8977f8f2b43e59a3"}, {"name": "test_set_special_function", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "247171d96cf78942", "parentUid": "1cdef3d89962b3a41ead4e6a44623984", "status": "passed", "time": {"start": 1754455075910, "stop": 1754455089646, "duration": 13736}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "1cdef3d89962b3a41ead4e6a44623984"}, {"name": "test_set_split_screen_apps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "634f9fa2a409f31e", "parentUid": "df5405aa0bd507fb6dc22f263be7dfef", "status": "passed", "time": {"start": 1754455103017, "stop": 1754455116832, "duration": 13815}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "df5405aa0bd507fb6dc22f263be7dfef"}, {"name": "test_set_timezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "613f079c90fa0578", "parentUid": "9e7c120412bd7e0f730e61377785fa8a", "status": "passed", "time": {"start": 1754455130094, "stop": 1754455144079, "duration": 13985}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "9e7c120412bd7e0f730e61377785fa8a"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "fe566ec100eab021", "parentUid": "5cae1c2c93d377af57591ba886a396b7", "status": "passed", "time": {"start": 1754455156956, "stop": 1754455171944, "duration": 14988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "5cae1c2c93d377af57591ba886a396b7"}, {"name": "test_start_running", "children": [{"name": "测试start running能正常执行", "uid": "56d6727ada4cc121", "parentUid": "8fdaf0f924e8731506064256cfd9d49e", "status": "failed", "time": {"start": 1754455185080, "stop": 1754455200076, "duration": 14996}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fdaf0f924e8731506064256cfd9d49e"}, {"name": "test_start_walking", "children": [{"name": "测试start walking能正常执行", "uid": "d66688f8f824d308", "parentUid": "4a8c9e4aef8cc53d5214b16790f29ecf", "status": "failed", "time": {"start": 1754455213655, "stop": 1754455228605, "duration": 14950}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a8c9e4aef8cc53d5214b16790f29ecf"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "d97ce04890c6efba", "parentUid": "eaf66c431b1bc4cdb440f63e804d5b7f", "status": "passed", "time": {"start": 1754455241895, "stop": 1754455255566, "duration": 13671}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "eaf66c431b1bc4cdb440f63e804d5b7f"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "87a8c1c2510dfe13", "parentUid": "9c64338b2e6a23f5945b6c45a6e8988f", "status": "passed", "time": {"start": 1754455268962, "stop": 1754455282666, "duration": 13704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "9c64338b2e6a23f5945b6c45a6e8988f"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "27ef3ceaf2e10ea4", "parentUid": "0a89b8197df33175fe755b5815c5229b", "status": "passed", "time": {"start": 1754455296573, "stop": 1754455311690, "duration": 15117}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "0a89b8197df33175fe755b5815c5229b"}, {"name": "test_switching_charging_speed", "children": [{"name": "测试switching charging speed能正常执行", "uid": "8681f0bb149c4d9d", "parentUid": "556ad59feb219cf0e64a09e0c312ef81", "status": "passed", "time": {"start": 1754455325004, "stop": 1754455338649, "duration": 13645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "556ad59feb219cf0e64a09e0c312ef81"}, {"name": "test_the_second", "children": [{"name": "测试the second返回正确的不支持响应", "uid": "a3c1f99a68a83c03", "parentUid": "33f765d60f4eb02aa8bf10da2e90a0c5", "status": "passed", "time": {"start": 1754455351920, "stop": 1754455366101, "duration": 14181}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "33f765d60f4eb02aa8bf10da2e90a0c5"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "c85c9199aaaea251", "parentUid": "07d91c6eb72b9bf62af23569018b6ed9", "status": "passed", "time": {"start": 1754455379237, "stop": 1754455392992, "duration": 13755}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "07d91c6eb72b9bf62af23569018b6ed9"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "38d6821ea604d751", "parentUid": "1da9f8360bfb0009b7bbba3b558b5ad6", "status": "passed", "time": {"start": 1754455406018, "stop": 1754455420840, "duration": 14822}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "1da9f8360bfb0009b7bbba3b558b5ad6"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "d930c37cd1055316", "parentUid": "fd2d2a21ef2aa878399c88b3d0e934ba", "status": "passed", "time": {"start": 1754455434254, "stop": 1754455448097, "duration": 13843}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "fd2d2a21ef2aa878399c88b3d0e934ba"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "bd7c1e709ec319a6", "parentUid": "49b44a51671f9fc053854051c80f7c70", "status": "passed", "time": {"start": 1754455462027, "stop": 1754455476062, "duration": 14035}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "49b44a51671f9fc053854051c80f7c70"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "dd08e027787e7fe6", "parentUid": "838b34c8fa65f970357a94fb9eaf0dcc", "status": "passed", "time": {"start": 1754455489902, "stop": 1754455504894, "duration": 14992}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "838b34c8fa65f970357a94fb9eaf0dcc"}, {"name": "test_vedio_call_number_by_whatsapp", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "d8b9051cd846e87c", "parentUid": "9d137f0f051ce9331aca868c940ab405", "status": "failed", "time": {"start": 1754455518029, "stop": 1754455540486, "duration": 22457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d137f0f051ce9331aca868c940ab405"}, {"name": "test_voice_setting_page", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "7f98b929b3063854", "parentUid": "c2e243e17e6068d0d67070f2c20e8486", "status": "failed", "time": {"start": 1754455553984, "stop": 1754455569893, "duration": 15909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c2e243e17e6068d0d67070f2c20e8486"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "6482ce3d1e710e63", "parentUid": "de0c4647cd25ea2211f7b7d6173d4170", "status": "passed", "time": {"start": 1754455583977, "stop": 1754455598021, "duration": 14044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "de0c4647cd25ea2211f7b7d6173d4170"}, {"name": "test_yandex_eats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "75aab6cbe80e5a4", "parentUid": "ffa74429a7c2fa0151c7e7ef27b25ecb", "status": "passed", "time": {"start": 1754455612062, "stop": 1754455626406, "duration": 14344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "ffa74429a7c2fa0151c7e7ef27b25ecb"}], "uid": "e416b8e9994852e8ed797dec283160f6"}], "uid": "testcases.test_ella"}]}