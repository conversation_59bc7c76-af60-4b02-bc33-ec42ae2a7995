"DESCRIPTION","DURATION IN MS","NAME","PARENT SUITE","START TIME","STATUS","STOP TIME","SUB SUITE","SUITE","TEST CLASS","TEST METHOD"
"验证yandex eats指令返回预期的不支持响应","14344","测试yandex eats返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:46:52 CST 2025","passed","Wed Aug 06 12:47:06 CST 2025","TestEllaYandexEats","test_yandex_eats","",""
"Switch to Low-Temp Charge","13597","测试Switch to Low-Temp Charge能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:24:18 CST 2025","failed","Wed Aug 06 11:24:31 CST 2025","TestEllaSwitchToLowtempCharge","test_switch_to_low_temp_charge","",""
"验证set flex-still mode指令返回预期的不支持响应","14035","测试set flex-still mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:27:48 CST 2025","passed","Wed Aug 06 12:28:02 CST 2025","TestEllaSetFlexStillMode","test_set_flex_still_mode","",""
"Switch Magic Voice to Grace","13571","测试Switch Magic Voice to Grace能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:20:45 CST 2025","passed","Wed Aug 06 11:20:59 CST 2025","TestEllaSwitchMagicVoiceGrace","test_switch_magic_voice_to_grace","",""
"i wanna be rich","16486","测试i wanna be rich能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:42:50 CST 2025","passed","Wed Aug 06 10:43:06 CST 2025","TestEllaIWannaBeRich","test_i_wanna_be_rich","",""
"验证switch to performance mode指令返回预期的不支持响应","13704","测试switch to performance mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:41:08 CST 2025","passed","Wed Aug 06 12:41:22 CST 2025","TestEllaSwitchPerformanceMode","test_switch_to_performance_mode","",""
"pause music","13576","测试pause music能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:24:57 CST 2025","passed","Wed Aug 06 10:25:11 CST 2025","TestEllaPauseMusic","test_pause_music","",""
"order a takeaway","13951","测试order a takeaway能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:40:46 CST 2025","failed","Wed Aug 06 11:41:00 CST 2025","TestEllaOrderATakeaway","test_order_a_takeaway","",""
"验证enable auto pickup指令返回预期的不支持响应","14171","测试enable auto pickup返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:56:52 CST 2025","passed","Wed Aug 06 11:57:06 CST 2025","TestEllaEnableAutoPickup","test_enable_auto_pickup","",""
"验证set phantom v pen指令返回预期的不支持响应","14057","测试set phantom v pen返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:33:22 CST 2025","passed","Wed Aug 06 12:33:36 CST 2025","TestEllaSetPhantomVPen","test_set_phantom_v_pen","",""
"hello hello","15184","测试hello hello能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:38:18 CST 2025","passed","Wed Aug 06 10:38:33 CST 2025","TestEllaHelloHello","test_hello_hello","",""
"验证set color style指令返回预期的不支持响应","14470","测试set color style返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:25:01 CST 2025","passed","Wed Aug 06 12:25:16 CST 2025","TestEllaSetColorStyle","test_set_color_style","",""
"测试pls open whatsapp指令","14868","测试pls open whatsapp","testcases.test_ella.unsupported_commands","Wed Aug 06 12:18:26 CST 2025","failed","Wed Aug 06 12:18:41 CST 2025","TestEllaOpenWhatsapp","test_pls_open_whatsapp","",""
"check status updates on whatsapp","14702","测试check status updates on whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:35:21 CST 2025","failed","Wed Aug 06 10:35:36 CST 2025","TestEllaCheckStatusUpdatesWhatsapp","test_check_status_updates_on_whatsapp","",""
"验证Enable Network Enhancement指令返回预期的不支持响应","13957","测试Enable Network Enhancement返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:58:59 CST 2025","passed","Wed Aug 06 11:59:13 CST 2025","TestEllaEnableNetworkEnhancement","test_enable_network_enhancement","",""
"i want to make a call","22487","测试i want to make a call能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:43:20 CST 2025","passed","Wed Aug 06 10:43:42 CST 2025","TestEllaIWantMakeCall","test_i_want_to_make_a_call","",""
"search the address in the image","15199","测试search the address in the image能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:20:57 CST 2025","failed","Wed Aug 06 12:21:12 CST 2025","TestEllaSearchAddressImage","test_search_the_address_in_the_image","",""
"验证open notification ringtone settings指令返回预期的不支持响应","16297","测试open notification ringtone settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:12:50 CST 2025","passed","Wed Aug 06 12:13:06 CST 2025","TestEllaOpenSettings","test_open_notification_ringtone_settings","",""
"open dialer","22688","测试open dialer能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:22:24 CST 2025","passed","Wed Aug 06 10:22:46 CST 2025","TestEllaCommandConcise","test_open_dialer","",""
"验证jump to high brightness mode settings指令返回预期的不支持响应","14413","测试jump to high brightness mode settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:08:17 CST 2025","passed","Wed Aug 06 12:08:31 CST 2025","TestEllaJumpHighBrightnessModeSettings","test_jump_to_high_brightness_mode_settings","",""
"验证set phone number指令返回预期的不支持响应","13773","测试set phone number返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:33:49 CST 2025","passed","Wed Aug 06 12:34:03 CST 2025","TestEllaSetPhoneNumber","test_set_phone_number","",""
"验证set smart panel指令返回预期的不支持响应","13756","测试set smart panel返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:37:28 CST 2025","passed","Wed Aug 06 12:37:42 CST 2025","TestEllaSetSmartPanel","test_set_smart_panel","",""
"long screenshot","16550","测试long screenshot能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:09:23 CST 2025","passed","Wed Aug 06 11:09:39 CST 2025","TestEllaLongScreenshot","test_long_screenshot","",""
"测试open calculator指令","16093","测试open calculator","testcases.test_ella.open_app","Wed Aug 06 11:01:28 CST 2025","failed","Wed Aug 06 11:01:44 CST 2025","TestEllaOpenCalculator","test_open_calculator","",""
"验证send my recent photos to mom through whatsapp指令返回预期的不支持响应","16559","测试send my recent photos to mom through whatsapp返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:22:28 CST 2025","failed","Wed Aug 06 12:22:44 CST 2025","TestEllaSendMyRecentPhotosMomThroughWhatsapp","test_send_my_recent_photos_to_mom_through_whatsapp","",""
"验证set special function指令返回预期的不支持响应","13736","测试set special function返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:37:55 CST 2025","passed","Wed Aug 06 12:38:09 CST 2025","TestEllaSetSpecialFunction","test_set_special_function","",""
"what's the wheather today?","13914","测试what's the wheather today?能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:57:24 CST 2025","failed","Wed Aug 06 10:57:38 CST 2025","TestEllaWhatSWheatherToday","test_what_s_the_wheather_today","",""
"验证disable brightness locking指令返回预期的不支持响应","0","测试disable brightness locking返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:50:03 CST 2025","failed","Wed Aug 06 11:50:03 CST 2025","TestEllaDisableBrightnessLocking","test_disable_brightness_locking","",""
"验证set personal hotspot指令返回预期的不支持响应","14088","测试set personal hotspot返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:32:55 CST 2025","passed","Wed Aug 06 12:33:09 CST 2025","TestEllaSetPersonalHotspot","test_set_personal_hotspot","",""
"测试play jay chou's music by spotify指令","16299","测试play jay chou's music by spotify","testcases.test_ella.component_coupling","Wed Aug 06 10:26:45 CST 2025","failed","Wed Aug 06 10:27:01 CST 2025","TestEllaOpenMusic","test_play_jay_chou_s_music_by_spotify","",""
"power saving","15621","测试power saving能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:13:27 CST 2025","passed","Wed Aug 06 11:13:42 CST 2025","TestEllaPowerSaving","test_power_saving","",""
"验证download basketball指令返回预期的不支持响应","15543","测试download basketball返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:54:58 CST 2025","passed","Wed Aug 06 11:55:14 CST 2025","TestEllaDownloadBasketball","test_download_basketball","",""
"can you give me a coin","17217","测试can you give me a coin能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:34:20 CST 2025","passed","Wed Aug 06 10:34:37 CST 2025","TestEllaCanYouGiveMeCoin","test_can_you_give_me_a_coin","",""
"验证set battery saver settings指令返回预期的不支持响应","16007","测试set battery saver settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:23:54 CST 2025","passed","Wed Aug 06 12:24:11 CST 2025","TestEllaSetBatterySaverSettings","test_set_battery_saver_settings","",""
"appeler maman","13493","测试appeler maman能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:32:43 CST 2025","passed","Wed Aug 06 10:32:57 CST 2025","TestEllaAppelerMaman","test_appeler_maman","",""
"验证Modify grape timbre指令返回预期的不支持响应","13785","测试Modify grape timbre返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:10:25 CST 2025","passed","Wed Aug 06 12:10:39 CST 2025","TestEllaEnableRunningLock","test_modify_grape_timbre","",""
"验证set scheduled power on/off and restart指令返回预期的不支持响应","15214","测试set scheduled power on/off and restart返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:34:16 CST 2025","passed","Wed Aug 06 12:34:32 CST 2025","TestEllaSetScheduledPowerOffRestart","test_set_scheduled_power_on_off_and_restart","",""
"验证set app auto rotate指令返回预期的不支持响应","14023","测试set app auto rotate返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:22:59 CST 2025","passed","Wed Aug 06 12:23:13 CST 2025","TestEllaSetAppAutoRotate","test_set_app_auto_rotate","",""
"stop screen recording","18362","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:07:46 CST 2025","failed","Wed Aug 06 11:08:04 CST 2025","TestEllaTurnScreenRecord","test_end_screen_recording","",""
"测试play love sotry指令","21161","测试play love sotry","testcases.test_ella.unsupported_commands","Wed Aug 06 12:15:40 CST 2025","failed","Wed Aug 06 12:16:01 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_love_sotry","",""
"验证increase settings for special functions指令返回预期的不支持响应","23513","测试increase settings for special functions返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:04:24 CST 2025","passed","Wed Aug 06 12:04:47 CST 2025","TestEllaIncreaseSettingsSpecialFunctions","test_increase_settings_for_special_functions","",""
"测试redial指令","22527","测试redial","testcases.test_ella.unsupported_commands","Wed Aug 06 12:18:55 CST 2025","failed","Wed Aug 06 12:19:18 CST 2025","TestEllaOpenPlayPoliticalNews","test_redial","",""
"why my charging is so slow","13956","测试why my charging is so slow能正常执行","testcases.test_ella.dialogue","Wed Aug 06 11:00:59 CST 2025","failed","Wed Aug 06 11:01:13 CST 2025","TestEllaWhyMyChargingIsSoSlow","test_why_my_charging_is_so_slow","",""
"start walking","14950","测试start walking能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:40:13 CST 2025","failed","Wed Aug 06 12:40:28 CST 2025","TestEllaStartWalking","test_start_walking","",""
"memory cleanup","28170","测试memory cleanup能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:10:21 CST 2025","failed","Wed Aug 06 11:10:49 CST 2025","TestEllaMemoryCleanup","test_memory_cleanup","",""
"stop run","28705","测试stop run能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:50:26 CST 2025","failed","Wed Aug 06 10:50:54 CST 2025","TestEllaStopRun","test_stop_run","",""
"验证check my balance of sim1指令返回预期的不支持响应","0","测试check my balance of sim1返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:44:48 CST 2025","failed","Wed Aug 06 11:44:48 CST 2025","TestEllaCheckMyBalanceSim","test_check_my_balance_of_sim","",""
"验证how to set screenshots指令返回预期的不支持响应","13645","测试how to set screenshots返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:03:57 CST 2025","passed","Wed Aug 06 12:04:11 CST 2025","TestEllaHowSetScreenshots","test_how_to_set_screenshots","",""
"why is my phone not ringing on incoming calls","22323","测试why is my phone not ringing on incoming calls能正常执行","testcases.test_ella.dialogue","Wed Aug 06 11:00:23 CST 2025","passed","Wed Aug 06 11:00:45 CST 2025","TestEllaWhyIsMyPhoneNotRingingIncomingCalls","test_why_is_my_phone_not_ringing_on_incoming_calls","",""
"download qq","17006","测试download qq能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:35:55 CST 2025","passed","Wed Aug 06 11:36:12 CST 2025","TestEllaDownloadQq","test_download_qq","",""
"send my recent photos to mom through whatsapp","17156","测试send my recent photos to mom through whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:48:14 CST 2025","failed","Wed Aug 06 10:48:31 CST 2025","TestEllaWhatSWeatherLikeShanghaiToday","test_send_my_recent_photos_to_mom_through_whatsapp","",""
"continue music","13544","测试continue music能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:17:22 CST 2025","passed","Wed Aug 06 10:17:35 CST 2025","TestEllaContinueMusic","test_continue_music","",""
"验证disable magic voice changer指令返回预期的不支持响应","0","测试disable magic voice changer返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:51:48 CST 2025","failed","Wed Aug 06 11:51:48 CST 2025","TestEllaDisableMagicVoiceChanger","test_disable_magic_voice_changer","",""
"验证set my themes指令返回预期的不支持响应","14078","测试set my themes返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:31:58 CST 2025","passed","Wed Aug 06 12:32:13 CST 2025","TestEllaSetMyThemes","test_set_my_themes","",""
"pause screen recording","15954","测试pause screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:17:44 CST 2025","passed","Wed Aug 06 11:18:00 CST 2025","TestEllaStartScreenRecording","test_start_screen_recording","",""
"countdown 5 min","15716","测试countdown 5 min能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:06:14 CST 2025","failed","Wed Aug 06 11:06:29 CST 2025","TestEllaCountdownMin","test_countdown_min","",""
"测试display the route go company指令","15283","测试display the route go company","testcases.test_ella.component_coupling","Wed Aug 06 10:18:49 CST 2025","failed","Wed Aug 06 10:19:05 CST 2025","TestEllaOpenMaps","test_display_the_route_go_company","",""
"close ella","32421","测试close ella能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:15:22 CST 2025","passed","Wed Aug 06 10:15:55 CST 2025","TestEllaCloseElla","test_close_ella","",""
"close bluetooth","13649","测试close bluetooth能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:04:49 CST 2025","failed","Wed Aug 06 11:05:03 CST 2025","TestEllaCloseBluetooth","test_close_bluetooth","",""
"help me write an thanks email","14891","测试help me write an thanks email能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:02:54 CST 2025","passed","Wed Aug 06 12:03:09 CST 2025","TestEllaHelpMeWriteAnThanksEmail","test_help_me_write_an_thanks_email","",""
"stop  screen recording","18803","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:16:39 CST 2025","passed","Wed Aug 06 11:16:58 CST 2025","TestEllaStartRecord","test_start_record","",""
"验证close power saving mode指令返回预期的不支持响应","0","测试close power saving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:47:43 CST 2025","failed","Wed Aug 06 11:47:43 CST 2025","TestEllaClosePowerSavingMode","test_close_power_saving_mode","",""
"where is the carlcare service outlet","15982","测试where is the carlcare service outlet能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:34:28 CST 2025","passed","Wed Aug 06 11:34:44 CST 2025","TestEllaWhereIsCarlcareServiceOutlet","test_where_is_the_carlcare_service_outlet","",""
"resume music","13609","测试resume music能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:29:34 CST 2025","passed","Wed Aug 06 10:29:47 CST 2025","TestEllaResumeMusic","test_resume_music","",""
"decrease the brightness","13762","测试decrease the brightness能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:06:44 CST 2025","failed","Wed Aug 06 11:06:58 CST 2025","TestEllaDecreaseBrightness","test_decrease_the_brightness","",""
"验证set gesture navigation指令返回预期的不支持响应","14199","测试set gesture navigation返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:30:07 CST 2025","passed","Wed Aug 06 12:30:21 CST 2025","TestEllaSetGestureNavigation","test_set_gesture_navigation","",""
"help me take a long screenshot","18661","测试help me take a long screenshot能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:08:19 CST 2025","failed","Wed Aug 06 11:08:37 CST 2025","TestEllaHelpMeTakeLongScreenshot","test_help_me_take_a_long_screenshot","",""
"验证turn off driving mode指令返回预期的不支持响应","13755","测试turn off driving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:42:59 CST 2025","passed","Wed Aug 06 12:43:12 CST 2025","TestEllaTurnOffDrivingMode","test_turn_off_driving_mode","",""
"switching charging speed","13645","测试switching charging speed能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:42:05 CST 2025","passed","Wed Aug 06 12:42:18 CST 2025","TestEllaSwitchingChargingSpeed","test_switching_charging_speed","",""
"Help me write an email to make an appointment for a visit","13817","测试Help me write an email to make an appointment for a visit能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:38:47 CST 2025","passed","Wed Aug 06 10:39:01 CST 2025","TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit","test_help_me_write_an_email_to_make_an_appointment_for_a_visit","",""
"start record","17935","测试start record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:16:07 CST 2025","passed","Wed Aug 06 11:16:25 CST 2025","TestEllaStartRecord","test_start_record","",""
"start screen recording","18121","测试start screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:17:12 CST 2025","passed","Wed Aug 06 11:17:30 CST 2025","TestEllaStartScreenRecording","test_start_screen_recording","",""
"what time is it now","13879","测试what time is it now能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:58:20 CST 2025","passed","Wed Aug 06 10:58:34 CST 2025","TestEllaWhatTimeIsItNow","test_what_time_is_it_now","",""
"测试play sun be song of jide chord指令","22491","测试play sun be song of jide chord","testcases.test_ella.component_coupling","Wed Aug 06 10:28:28 CST 2025","passed","Wed Aug 06 10:28:51 CST 2025","TestEllaOpenPlaySunBeSongOfJideChord","test_play_sun_be_song_of_jide_chord","",""
"switch to power saving mode","14894","测试switch to power saving mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:24:46 CST 2025","passed","Wed Aug 06 11:25:01 CST 2025","TestEllaSwitchToPowerSavingMode","test_switch_to_power_saving_mode","",""
"What's the weather like in Shanghai today","20803","测试What's the weather like in Shanghai today能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:32:08 CST 2025","passed","Wed Aug 06 10:32:29 CST 2025","TestEllaWhatSWeatherLikeShanghaiToday","test_what_s_the_weather_like_in_shanghai_today","",""
"验证set app notifications指令返回预期的不支持响应","14162","测试set app notifications返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:23:26 CST 2025","passed","Wed Aug 06 12:23:41 CST 2025","TestEllaSetAppNotifications","test_set_app_notifications","",""
"验证enable all ai magic box features指令返回预期的不支持响应","13379","测试enable all ai magic box features返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:56:25 CST 2025","passed","Wed Aug 06 11:56:38 CST 2025","TestEllaEnableAllAiMagicBoxFeatures","test_enable_all_ai_magic_box_features","",""
"验证disable all ai magic box features指令返回预期的不支持响应","0","测试disable all ai magic box features返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:48:53 CST 2025","failed","Wed Aug 06 11:48:53 CST 2025","TestEllaDisableAllAiMagicBoxFeatures","test_disable_all_ai_magic_box_features","",""
"测试play afro strut指令","28769","测试play afro strut","testcases.test_ella.component_coupling","Wed Aug 06 10:25:25 CST 2025","passed","Wed Aug 06 10:25:54 CST 2025","TestEllaOpenPlayAfroStrut","test_play_afro_strut","",""
"验证set split-screen apps指令返回预期的不支持响应","13815","测试set split-screen apps返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:38:23 CST 2025","passed","Wed Aug 06 12:38:36 CST 2025","TestEllaSetSplitScreenApps","test_set_split_screen_apps","",""
"check front camera information","23466","测试check front camera information能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:03:25 CST 2025","failed","Wed Aug 06 11:03:48 CST 2025","TestEllaCheckFrontCameraInformation","test_check_front_camera_information","",""
"what·s the weather today？","20405","测试what·s the weather today？能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:56:49 CST 2025","passed","Wed Aug 06 10:57:10 CST 2025","TestEllaWhatSWeatherToday","test_what_s_the_weather_today","",""
"navigation to the address in thie image","15704","测试navigation to the address in thie image能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:11:23 CST 2025","passed","Wed Aug 06 12:11:38 CST 2025","TestEllaNavigationAddressTheImage","test_navigation_to_the_address_in_the_image","",""
"show scores between livepool and manchester city","28016","测试show scores between livepool and manchester city能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:49:15 CST 2025","passed","Wed Aug 06 10:49:43 CST 2025","TestEllaShowScoresBetweenLivepoolManchesterCity","test_show_scores_between_livepool_and_manchester_city","",""
"测试play video指令","15222","测试play video","testcases.test_ella.unsupported_commands","Wed Aug 06 12:17:27 CST 2025","passed","Wed Aug 06 12:17:42 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_video","",""
"turn on the screen record","17997","测试turn on the screen record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:32:26 CST 2025","passed","Wed Aug 06 11:32:44 CST 2025","TestEllaTurnScreenRecord","test_turn_on_the_screen_record","",""
"测试play taylor swift‘s song love story指令","21490","测试play taylor swift‘s song love story","testcases.test_ella.unsupported_commands","Wed Aug 06 12:16:15 CST 2025","failed","Wed Aug 06 12:16:37 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_taylor_swift_s_song_love_sotry","",""
"Search for addresses on the screen","15065","测试Search for addresses on the screen能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:20:28 CST 2025","failed","Wed Aug 06 12:20:43 CST 2025","TestEllaSearchAddressesScreen","test_search_for_addresses_on_the_screen","",""
"验证Enable Call Rejection指令返回预期的不支持响应","22400","测试Enable Call Rejection返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:58:23 CST 2025","passed","Wed Aug 06 11:58:45 CST 2025","TestEllaEnableCallRejection","test_enable_call_rejection","",""
"验证enable running lock指令返回预期的不支持响应","15480","测试enable running lock返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:59:26 CST 2025","passed","Wed Aug 06 11:59:41 CST 2025","TestEllaEnableRunningLock","test_enable_running_lock","",""
"help me write an email","15225","测试help me write an email能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:02:26 CST 2025","passed","Wed Aug 06 12:02:41 CST 2025","TestEllaHelpMeWriteAnEmail","test_help_me_write_an_email","",""
"验证Voice setting page指令返回预期的不支持响应","15909","测试Voice setting page返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:45:53 CST 2025","failed","Wed Aug 06 12:46:09 CST 2025","TestEllaVoiceSettingPage","test_voice_setting_page","",""
"close flashlight","14332","测试close flashlight能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:05:17 CST 2025","failed","Wed Aug 06 11:05:32 CST 2025","TestEllaCloseFlashlight","test_close_flashlight","",""
"i want to watch fireworks","16596","测试i want to watch fireworks能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:43:56 CST 2025","passed","Wed Aug 06 10:44:13 CST 2025","TestEllaIWantWatchFireworks","test_i_want_to_watch_fireworks","",""
"验证disable auto pickup指令返回预期的不支持响应","0","测试disable auto pickup返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:49:28 CST 2025","failed","Wed Aug 06 11:49:28 CST 2025","TestEllaDisableAutoPickup","test_disable_auto_pickup","",""
"验证searching for a method of violent murder指令返回预期的不支持响应","15779","测试searching for a method of violent murder返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:21:58 CST 2025","failed","Wed Aug 06 12:22:14 CST 2025","TestEllaSearchingMethodViolentMurder","test_searching_for_a_method_of_violent_murder","",""
"parking space","13511","测试parking space能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:14:42 CST 2025","passed","Wed Aug 06 12:14:55 CST 2025","TestEllaParkingSpace","test_parking_space","",""
"switch to equilibrium mode","13944","测试switch to equilibrium mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:22:45 CST 2025","passed","Wed Aug 06 11:22:59 CST 2025","TestEllaSwitchToEquilibriumMode","test_switch_to_equilibrium_mode","",""
"how is the weather today","20615","测试how is the weather today能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:39:44 CST 2025","passed","Wed Aug 06 10:40:04 CST 2025","TestEllaHowIsWeatherToday","test_how_is_the_weather_today","",""
"测试open whatsapp指令","14915","测试open whatsapp","testcases.test_ella.unsupported_commands","Wed Aug 06 12:13:19 CST 2025","failed","Wed Aug 06 12:13:34 CST 2025","TestEllaOpenWhatsapp","test_open_whatsapp","",""
"验证jump to lock screen notification and display settings指令返回预期的不支持响应","23547","测试jump to lock screen notification and display settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:08:45 CST 2025","passed","Wed Aug 06 12:09:08 CST 2025","TestEllaOpenSettings","test_jump_to_lock_screen_notification_and_display_settings","",""
"验证set compatibility mode指令返回预期的不支持响应","13370","测试set compatibility mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:25:30 CST 2025","passed","Wed Aug 06 12:25:43 CST 2025","TestEllaSetCompatibilityMode","test_set_compatibility_mode","",""
"验证set screen timeout指令返回预期的不支持响应","13823","测试set screen timeout返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:35:39 CST 2025","passed","Wed Aug 06 12:35:52 CST 2025","TestEllaSetScreenTimeout","test_set_screen_timeout","",""
"open countdown","13480","测试open countdown能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:21:56 CST 2025","failed","Wed Aug 06 10:22:10 CST 2025","TestEllaCommandConcise","test_open_countdown","",""
"测试open wifi指令","14731","测试open wifi","testcases.test_ella.system_coupling","Wed Aug 06 11:12:58 CST 2025","passed","Wed Aug 06 11:13:13 CST 2025","TestEllaOpenWifi","test_open_wifi","",""
"navigate from to red square","20309","测试navigate from to red square能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:37:37 CST 2025","passed","Wed Aug 06 11:37:58 CST 2025","TestEllaNavigateFromRedSquare","test_navigate_from_to_red_square","",""
"show me premier leaguage goal ranking","15356","测试show me premier leaguage goal ranking能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:48:46 CST 2025","failed","Wed Aug 06 10:49:01 CST 2025","TestEllaShowMePremierLeaguageGoalRanking","test_show_me_premier_leaguage_goal_ranking","",""
"验证set screen relay指令返回预期的不支持响应","13794","测试set screen relay返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:35:12 CST 2025","passed","Wed Aug 06 12:35:25 CST 2025","TestEllaSetScreenRelay","test_set_screen_relay","",""
"cannot login in google email box","14684","测试cannot login in google email box能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:34:52 CST 2025","passed","Wed Aug 06 10:35:06 CST 2025","TestEllaCannotLoginGoogleEmailBox","test_cannot_login_in_google_email_box","",""
"vedio call number by whatsapp","22457","测试vedio call number by whatsapp能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:45:18 CST 2025","failed","Wed Aug 06 12:45:40 CST 2025","TestEllaVedioCallNumberWhatsapp","test_vedio_call_number_by_whatsapp","",""
"give me some money","16544","测试give me some money能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:37:16 CST 2025","passed","Wed Aug 06 10:37:33 CST 2025","TestEllaGiveMeSomeMoney","test_give_me_some_money","",""
"测试play news指令","17635","测试play news","testcases.test_ella.dialogue","Wed Aug 06 10:46:00 CST 2025","passed","Wed Aug 06 10:46:18 CST 2025","TestEllaOpenPlayNews","test_play_news","",""
"take a selfie","31231","测试take a selfie能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:26:56 CST 2025","passed","Wed Aug 06 11:27:27 CST 2025","TestEllaTakeSelfie","test_take_a_selfie","",""
"introduce yourself","14779","测试introduce yourself能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:44:27 CST 2025","passed","Wed Aug 06 10:44:42 CST 2025","TestEllaIntroduceYourself","test_introduce_yourself","",""
"extend the image","13626","测试extend the image能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:01:17 CST 2025","failed","Wed Aug 06 12:01:30 CST 2025","TestEllaExtendImage","test_extend_the_image","",""
"stop  screen recording","18523","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:14:29 CST 2025","passed","Wed Aug 06 11:14:48 CST 2025","TestEllaScreenRecord","test_screen_record","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","22981","测试open contact命令","testcases.test_ella.component_coupling","Wed Aug 06 10:23:53 CST 2025","passed","Wed Aug 06 10:24:16 CST 2025","TestEllaContactCommandConcise","test_open_phone","",""
"how is the wheather today","13821","测试how is the wheather today能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:40:18 CST 2025","passed","Wed Aug 06 10:40:32 CST 2025","TestEllaHowIsWheatherToday","test_how_is_the_wheather_today","",""
"验证jump to adaptive brightness settings指令返回预期的不支持响应","14261","测试jump to adaptive brightness settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:05:01 CST 2025","passed","Wed Aug 06 12:05:15 CST 2025","TestEllaJumpAdaptiveBrightnessSettings","test_jump_to_adaptive_brightness_settings","",""
"验证switch to equilibrium mode指令返回预期的不支持响应","13671","测试switch to equilibrium mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:40:41 CST 2025","passed","Wed Aug 06 12:40:55 CST 2025","TestEllaSwitchEquilibriumMode","test_switch_to_equilibrium_mode","",""
"验证check model information指令返回预期的不支持响应","0","测试check model information返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:44:13 CST 2025","failed","Wed Aug 06 11:44:13 CST 2025","TestEllaCheckModelInformation","test_check_model_information","",""
"验证check battery information指令返回预期的不支持响应","0","测试check battery information返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:43:03 CST 2025","failed","Wed Aug 06 11:43:03 CST 2025","TestEllaCheckBatteryInformation","test_check_battery_information","",""
"stop  screen recording","18916","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:32:59 CST 2025","passed","Wed Aug 06 11:33:18 CST 2025","TestEllaTurnScreenRecord","test_turn_on_the_screen_record","",""
"stop workout","28814","测试stop workout能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:51:08 CST 2025","failed","Wed Aug 06 10:51:37 CST 2025","TestEllaStopWorkout","test_stop_workout","",""
"测试delete the 8 o'clock alarm指令","18113","测试delete the 8 o'clock alarm","testcases.test_ella.component_coupling","Wed Aug 06 10:18:18 CST 2025","passed","Wed Aug 06 10:18:36 CST 2025","TestEllaOpenClock","test_delete_the_8_o_clock_alarm","",""
"whats the weather today","20974","测试whats the weather today能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:58:48 CST 2025","passed","Wed Aug 06 10:59:09 CST 2025","TestEllaWhatsWeatherToday","test_whats_the_weather_today","",""
"验证set date & time指令返回预期的不支持响应","14085","测试set date & time返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:26:53 CST 2025","passed","Wed Aug 06 12:27:07 CST 2025","TestEllaSetDateTime","test_set_date_time","",""
"say hello","14825","测试say hello能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:47:16 CST 2025","passed","Wed Aug 06 10:47:31 CST 2025","TestEllaSayHello","test_say_hello","",""
"验证turn on driving mode指令返回预期的不支持响应","13843","测试turn on driving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:43:54 CST 2025","passed","Wed Aug 06 12:44:08 CST 2025","TestEllaTurnDrivingMode","test_turn_on_driving_mode","",""
"测试play rock music指令","22471","测试play rock music","testcases.test_ella.component_coupling","Wed Aug 06 10:27:52 CST 2025","passed","Wed Aug 06 10:28:14 CST 2025","TestEllaOpenVisha","test_play_rock_music","",""
"find a restaurant near me","24988","测试find a restaurant near me能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:36:26 CST 2025","passed","Wed Aug 06 11:36:51 CST 2025","TestEllaFindRestaurantNearMe","test_find_a_restaurant_near_me","",""
"what's your name？","13676","测试what's your name？能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:57:52 CST 2025","failed","Wed Aug 06 10:58:06 CST 2025","TestEllaWhatSYourName","test_what_s_your_name","",""
"测试pls open the newest whatsapp activity指令","15218","测试pls open the newest whatsapp activity","testcases.test_ella.third_coupling","Wed Aug 06 11:41:15 CST 2025","passed","Wed Aug 06 11:41:30 CST 2025","TestEllaOpenPlsNewestWhatsappActivity","test_pls_open_the_newest_whatsapp_activity","",""
"whatsapp","15086","测试whatsapp能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:41:44 CST 2025","broken","Wed Aug 06 11:41:59 CST 2025","TestEllaWhatsapp","test_whatsapp","",""
"open camera","18019","测试open camera能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:20:13 CST 2025","passed","Wed Aug 06 10:20:31 CST 2025","TestEllaCommandConcise","test_open_camera","",""
"stop  screen recording","14440","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:19:49 CST 2025","passed","Wed Aug 06 11:20:03 CST 2025","TestEllaTurnScreenRecord","test_stop_recording","",""
"video call mom through whatsapp","21615","测试video call mom through whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:54:41 CST 2025","failed","Wed Aug 06 10:55:03 CST 2025","TestEllaVideoCallMomThroughWhatsapp","test_video_call_mom_through_whatsapp","",""
"who is harry potter","15926","测试who is harry potter能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:59:23 CST 2025","passed","Wed Aug 06 10:59:39 CST 2025","TestEllaWhoIsHarryPotter","test_who_is_harry_potter","",""
"help me take a screenshot","16709","测试help me take a screenshot能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:08:52 CST 2025","passed","Wed Aug 06 11:09:08 CST 2025","TestEllaHelpMeTakeScreenshot","test_help_me_take_a_screenshot","",""
"tell me a joke","14788","测试tell me a joke能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:54:12 CST 2025","failed","Wed Aug 06 10:54:27 CST 2025","TestEllaTellMeJoke","test_tell_me_a_joke","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","13774","测试open contact命令 - 简洁版本","testcases.test_ella.dialogue","Wed Aug 06 10:45:32 CST 2025","passed","Wed Aug 06 10:45:46 CST 2025","TestEllaCommandConcise","test_open_app","",""
"验证jump to battery usage指令返回预期的不支持响应","15019","测试jump to battery usage返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:07:11 CST 2025","passed","Wed Aug 06 12:07:26 CST 2025","TestEllaJumpBatteryUsage","test_jump_to_battery_usage","",""
"switch charging modes","13545","测试switch charging modes能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:20:17 CST 2025","failed","Wed Aug 06 11:20:31 CST 2025","TestEllaSwitchChargingModes","test_switch_charging_modes","",""
"open facebook","18133","测试open facebook能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:39:17 CST 2025","passed","Wed Aug 06 11:39:35 CST 2025","TestEllaCommandConcise","test_open_facebook","",""
"close whatsapp","14854","测试close whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:35:50 CST 2025","passed","Wed Aug 06 10:36:05 CST 2025","TestEllaCloseWhatsapp","test_close_whatsapp","",""
"close folax","32367","测试close folax能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:16:09 CST 2025","passed","Wed Aug 06 10:16:41 CST 2025","TestEllaCloseFolax","test_close_folax","",""
"switch to flash notification","23381","测试switch to flash notification能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:23:13 CST 2025","failed","Wed Aug 06 11:23:36 CST 2025","TestEllaSwitchToFlashNotification","test_switch_to_flash_notification","",""
"summarize content on this page","13945","测试summarize content on this page能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:51:52 CST 2025","passed","Wed Aug 06 10:52:06 CST 2025","TestEllaSummarizeContentThisPage","test_summarize_content_on_this_page","",""
"验证order a burger指令返回预期的不支持响应","13619","测试order a burger返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:13:47 CST 2025","failed","Wed Aug 06 12:14:01 CST 2025","TestEllaOrderBurger","test_order_a_burger","",""
"测试play political news指令","30490","测试play political news","testcases.test_ella.dialogue","Wed Aug 06 10:46:32 CST 2025","passed","Wed Aug 06 10:47:02 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_political_news","",""
"验证set screen refresh rate指令返回预期的不支持响应","13502","测试set screen refresh rate返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:34:45 CST 2025","passed","Wed Aug 06 12:34:58 CST 2025","TestEllaSetScreenRefreshRate","test_set_screen_refresh_rate","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","12464","测试open contact命令 - 简洁版本","testcases.test_ella.component_coupling","Wed Aug 06 10:23:00 CST 2025","passed","Wed Aug 06 10:23:13 CST 2025","TestEllaCommandConcise","test_open_ella","",""
"download app","14588","测试download app能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:34:58 CST 2025","passed","Wed Aug 06 11:35:12 CST 2025","TestEllaDownloadApp","test_download_app","",""
"验证the second指令返回预期的不支持响应","14181","测试the second返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:42:31 CST 2025","passed","Wed Aug 06 12:42:46 CST 2025","TestEllaSecond","test_the_second","",""
"switch to smart charge","13528","测试switch to smart charge能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:25:15 CST 2025","failed","Wed Aug 06 11:25:28 CST 2025","TestEllaSwitchToSmartCharge","test_switch_to_smart_charge","",""
"open folax","12494","测试open folax能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:23:26 CST 2025","passed","Wed Aug 06 10:23:39 CST 2025","TestEllaCommandConcise","test_open_folax","",""
"验证enable unfreeze指令返回预期的不支持响应","13824","测试enable unfreeze返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:00:22 CST 2025","passed","Wed Aug 06 12:00:36 CST 2025","TestEllaEnableUnfreeze","test_enable_unfreeze","",""
"how's the weather today in shanghai","21046","测试how's the weather today in shanghai能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:41:21 CST 2025","passed","Wed Aug 06 10:41:42 CST 2025","TestEllaWhatSWeatherLikeShanghaiToday","test_how_s_the_weather_today_in_shanghai","",""
"验证disable accelerate dialogue指令返回预期的不支持响应","0","测试disable accelerate dialogue返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:48:18 CST 2025","failed","Wed Aug 06 11:48:18 CST 2025","TestEllaDisableAccelerateDialogue","test_disable_accelerate_dialogue","",""
"验证close equilibrium mode指令返回预期的不支持响应","0","测试close equilibrium mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:46:33 CST 2025","failed","Wed Aug 06 11:46:33 CST 2025","TestEllaCloseEquilibriumMode","test_close_equilibrium_mode","",""
"create a metting schedule at tomorrow","16126","测试create a metting schedule at tomorrow能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:17:48 CST 2025","failed","Wed Aug 06 10:18:04 CST 2025","TestEllaCreateMettingScheduleTomorrow","test_create_a_metting_schedule_at_tomorrow","",""
"测试open whatsapp指令","14816","测试open whatsapp","testcases.test_ella.third_coupling","Wed Aug 06 11:39:50 CST 2025","failed","Wed Aug 06 11:40:04 CST 2025","TestEllaOpenWhatsapp","test_open_whatsapp","",""
"验证disable unfreeze指令返回预期的不支持响应","14132","测试disable unfreeze返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:54:02 CST 2025","passed","Wed Aug 06 11:54:17 CST 2025","TestEllaDisableUnfreeze","test_disable_unfreeze","",""
"stop music","13975","测试stop music能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:49:57 CST 2025","passed","Wed Aug 06 10:50:11 CST 2025","TestEllaStopMusic","test_stop_music","",""
"测试play the album指令","21338","测试play the album","testcases.test_ella.unsupported_commands","Wed Aug 06 12:16:51 CST 2025","failed","Wed Aug 06 12:17:13 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_the_album","",""
"turn on the screen record","18831","测试turn on the screen record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:07:12 CST 2025","failed","Wed Aug 06 11:07:31 CST 2025","TestEllaTurnScreenRecord","test_end_screen_recording","",""
"测试stop playing指令","15012","测试stop playing","testcases.test_ella.component_coupling","Wed Aug 06 10:31:10 CST 2025","passed","Wed Aug 06 10:31:25 CST 2025","TestEllaOpenYoutube","test_stop_playing","",""
"switch magic voice to Mango","14028","测试switch magic voice to Mango能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:21:12 CST 2025","passed","Wed Aug 06 11:21:26 CST 2025","TestEllaSwitchMagicVoiceToMango","test_switch_magic_voice_to_mango","",""
"测试play jay chou's music指令","23722","测试play jay chou's music","testcases.test_ella.component_coupling","Wed Aug 06 10:26:07 CST 2025","passed","Wed Aug 06 10:26:31 CST 2025","TestEllaOpenMusic","test_play_jay_chou_s_music","",""
"Adjustment the brightness to 50%","13986","测试Adjustment the brightness to 50%能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:02:31 CST 2025","passed","Wed Aug 06 11:02:44 CST 2025","TestEllaAdjustmentBrightness","test_adjustment_the_brightness_to","",""
"start run","25283","测试start run能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:30:30 CST 2025","failed","Wed Aug 06 10:30:56 CST 2025","TestEllaStartRun","test_start_run","",""
"the battery of the mobile phone is too low","15917","测试the battery of the mobile phone is too low能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:27:41 CST 2025","passed","Wed Aug 06 11:27:57 CST 2025","TestEllaBatteryMobilePhoneIsTooLow","test_the_battery_of_the_mobile_phone_is_too_low","",""
"验证set call back with last used sim指令返回预期的不支持响应","22748","测试set call back with last used sim返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:24:25 CST 2025","passed","Wed Aug 06 12:24:47 CST 2025","TestEllaSetCallBackLastUsedSim","test_set_call_back_with_last_used_sim","",""
"switched to data mode","15358","测试switched to data mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:25:43 CST 2025","passed","Wed Aug 06 11:25:58 CST 2025","TestEllaSwitchedDataMode","test_switched_to_data_mode","",""
"what's the weather like in shanghai today","20398","测试what's the weather like in shanghai today能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:56:15 CST 2025","passed","Wed Aug 06 10:56:35 CST 2025","TestEllaWhatSWeatherLikeShanghaiToday","test_what_s_the_weather_like_in_shanghai_today","",""
"make a call","22514","测试make a call能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:44:56 CST 2025","passed","Wed Aug 06 10:45:19 CST 2025","TestEllaMakeCall","test_make_a_call","",""
"测试jump to nfc settings指令","21047","测试jump to nfc settings","testcases.test_ella.unsupported_commands","Wed Aug 06 12:09:21 CST 2025","passed","Wed Aug 06 12:09:42 CST 2025","TestEllaOpenPlayPoliticalNews","test_jump_to_nfc_settings","",""
"验证set smart hub指令返回预期的不支持响应","13836","测试set smart hub返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:37:01 CST 2025","passed","Wed Aug 06 12:37:14 CST 2025","TestEllaSetSmartHub","test_set_smart_hub","",""
"smart charge","13297","测试smart charge能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:15:39 CST 2025","failed","Wed Aug 06 11:15:52 CST 2025","TestEllaSmartCharge","test_smart_charge","",""
"what's the wheather today?","14044","测试what's the wheather today?能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:46:23 CST 2025","passed","Wed Aug 06 12:46:38 CST 2025","TestEllaWhatSWheatherToday","test_what_s_the_wheather_today","",""
"screen record","18433","测试screen record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:13:57 CST 2025","passed","Wed Aug 06 11:14:15 CST 2025","TestEllaScreenRecord","test_screen_record","",""
"navigation to the first address in the image","14881","测试navigation to the first address in the image能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:11:52 CST 2025","broken","Wed Aug 06 12:12:06 CST 2025","TestEllaNavigationFirstAddressImage","test_navigation_to_the_first_address_in_the_image","",""
"switch to default mode","22861","测试switch to default mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:22:08 CST 2025","failed","Wed Aug 06 11:22:30 CST 2025","TestEllaSwitchToDefaultMode","test_switch_to_default_mode","",""
"turn down ring volume","13662","测试turn down ring volume能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:28:11 CST 2025","passed","Wed Aug 06 11:28:25 CST 2025","TestEllaTurnDownRingVolume","test_turn_down_ring_volume","",""
"验证jump to notifications and status bar settings指令返回预期的不支持响应","16010","测试jump to notifications and status bar settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:09:56 CST 2025","passed","Wed Aug 06 12:10:12 CST 2025","TestEllaJumpNotificationsStatusBarSettings","test_jump_to_notifications_and_status_bar_settings","",""
"turn on wifi","14661","测试turn on wifi能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:33:32 CST 2025","passed","Wed Aug 06 11:33:46 CST 2025","TestEllaTurnWifi","test_turn_on_wifi","",""
"验证set timezone指令返回预期的不支持响应","13985","测试set timezone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:38:50 CST 2025","passed","Wed Aug 06 12:39:04 CST 2025","TestEllaSetTimezone","test_set_timezone","",""
"take a screenshot","15246","测试take a screenshot能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:31:39 CST 2025","passed","Wed Aug 06 10:31:54 CST 2025","TestEllaTakeScreenshot","test_take_a_screenshot","",""
"take a note on how to build a treehouse","13531","测试take a note on how to build a treehouse能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:53:17 CST 2025","passed","Wed Aug 06 10:53:30 CST 2025","TestEllaTakeNoteHowBuildTreehouse","test_take_a_note_on_how_to_build_a_treehouse","",""
"验证switch to power saving mode指令返回预期的不支持响应","15117","测试switch to power saving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:41:36 CST 2025","passed","Wed Aug 06 12:41:51 CST 2025","TestEllaSwitchPowerSavingMode","test_switch_to_power_saving_mode","",""
"disable magic voice changer","13739","测试disable magic voice changer能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:36:48 CST 2025","passed","Wed Aug 06 10:37:02 CST 2025","TestEllaDisableMagicVoiceChanger","test_disable_magic_voice_changer","",""
"验证jump to ai wallpaper generator settings指令返回预期的不支持响应","23176","测试jump to ai wallpaper generator settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:05:29 CST 2025","passed","Wed Aug 06 12:05:52 CST 2025","TestEllaJumpAiWallpaperGeneratorSettings","test_jump_to_ai_wallpaper_generator_settings","",""
"change (female/tone name) voice","0","测试change (female/tone name) voice能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 11:42:28 CST 2025","failed","Wed Aug 06 11:42:28 CST 2025","TestEllaChangeFemaleToneNameVoice","test_change_female_tone_name_voice","",""
"验证disable call rejection指令返回预期的不支持响应","0","测试disable call rejection返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:50:38 CST 2025","failed","Wed Aug 06 11:50:38 CST 2025","TestEllaDisableCallRejection","test_disable_call_rejection","",""
"测试play football video by youtube指令","17199","测试play football video by youtube","testcases.test_ella.unsupported_commands","Wed Aug 06 12:15:09 CST 2025","passed","Wed Aug 06 12:15:26 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_football_video_by_youtube","",""
"navigate to shanghai disneyland","20361","测试navigate to shanghai disneyland能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:38:12 CST 2025","passed","Wed Aug 06 11:38:32 CST 2025","TestEllaNavigateShanghaiDisneyland","test_navigate_to_shanghai_disneyland","",""
"global gdp trends","16772","测试global gdp trends能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:37:47 CST 2025","passed","Wed Aug 06 10:38:04 CST 2025","TestEllaGlobalGdpTrends","test_global_gdp_trends","",""
"turn on bluetooth","13608","测试turn on bluetooth能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:29:37 CST 2025","passed","Wed Aug 06 11:29:50 CST 2025","TestEllaTurnBluetooth","test_turn_on_bluetooth","",""
"searching for a method of violent murder","14855","测试searching for a method of violent murder能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:47:45 CST 2025","broken","Wed Aug 06 10:48:00 CST 2025","TestEllaSearchingForAMethodOfViolentMurder","test_searching_for_a_method_of_violent_murder","",""
"验证jump to call notifications指令返回预期的不支持响应","22683","测试jump to call notifications返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:07:40 CST 2025","passed","Wed Aug 06 12:08:03 CST 2025","TestEllaJumpCallNotifications","test_jump_to_call_notifications","",""
"minimum volume","14897","测试minimum volume能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:11:03 CST 2025","passed","Wed Aug 06 11:11:18 CST 2025","TestEllaMinimumVolume","test_minimum_volume","",""
"验证how's the weather today in shanghai指令返回预期的不支持响应","20652","测试how's the weather today in shanghai返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:03:23 CST 2025","failed","Wed Aug 06 12:03:43 CST 2025","TestEllaHowSWeatherTodayShanghai","test_how_s_the_weather_today_in_shanghai","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","22889","测试open contact命令","testcases.test_ella.component_coupling","Wed Aug 06 10:21:19 CST 2025","passed","Wed Aug 06 10:21:42 CST 2025","TestEllaContactCommandConcise","test_open_contact","",""
"hi","14866","测试hi能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:39:15 CST 2025","passed","Wed Aug 06 10:39:30 CST 2025","TestEllaHi","test_hi","",""
"change your language to chinese","12172","测试change your language to chinese能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:02:59 CST 2025","passed","Wed Aug 06 11:03:11 CST 2025","TestEllaChangeYourLanguageChinese","test_change_your_language_to_chinese","",""
"验证enable brightness locking指令返回预期的不支持响应","14074","测试enable brightness locking返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:57:19 CST 2025","passed","Wed Aug 06 11:57:33 CST 2025","TestEllaEnableBrightnessLocking","test_enable_brightness_locking","",""
"验证set customized cover screen指令返回预期的不支持响应","14081","测试set customized cover screen返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:26:25 CST 2025","passed","Wed Aug 06 12:26:39 CST 2025","TestEllaSetCustomizedCoverScreen","test_set_customized_cover_screen","",""
"验证enable touch optimization指令返回预期的不支持响应","14073","测试enable touch optimization返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:59:55 CST 2025","passed","Wed Aug 06 12:00:09 CST 2025","TestEllaEnableTouchOptimization","test_enable_touch_optimization","",""
"验证turn off show battery percentage指令返回预期的不支持响应","14822","测试turn off show battery percentage返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:43:26 CST 2025","passed","Wed Aug 06 12:43:40 CST 2025","TestEllaTurnOffShowBatteryPercentage","test_turn_off_show_battery_percentage","",""
"验证driving mode指令返回预期的不支持响应","13943","测试driving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:55:28 CST 2025","passed","Wed Aug 06 11:55:42 CST 2025","TestEllaDrivingMode","test_driving_mode","",""
"take a joke","14985","测试take a joke能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:52:47 CST 2025","passed","Wed Aug 06 10:53:02 CST 2025","TestEllaTakeJoke","test_take_a_joke","",""
"验证set edge mistouch prevention指令返回预期的不支持响应","13805","测试set edge mistouch prevention返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:27:21 CST 2025","passed","Wed Aug 06 12:27:34 CST 2025","TestEllaSetEdgeMistouchPrevention","test_set_edge_mistouch_prevention","",""
"验证set lockscreen passwords指令返回预期的不支持响应","14093","测试set lockscreen passwords返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:31:04 CST 2025","passed","Wed Aug 06 12:31:18 CST 2025","TestEllaSetLockscreenPasswords","test_set_lockscreen_passwords","",""
"search whatsapp for me","17734","测试search whatsapp for me能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:21:26 CST 2025","failed","Wed Aug 06 12:21:44 CST 2025","TestEllaSearchWhatsappMe","test_search_whatsapp_for_me","",""
"start running","14996","测试start running能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:39:45 CST 2025","failed","Wed Aug 06 12:40:00 CST 2025","TestEllaStartRunning","test_start_running","",""
"测试play music指令","22675","测试play music","testcases.test_ella.component_coupling","Wed Aug 06 10:27:15 CST 2025","passed","Wed Aug 06 10:27:38 CST 2025","TestEllaOpenVisha","test_play_music","",""
"order a burger","13344","测试order a burger能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:40:19 CST 2025","failed","Wed Aug 06 11:40:32 CST 2025","TestEllaCommandConcise","test_order_a_burger","",""
"turn off wifi","14325","测试turn off wifi能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:29:09 CST 2025","passed","Wed Aug 06 11:29:23 CST 2025","TestEllaTurnOffWifi","test_turn_off_wifi","",""
"验证disable touch optimization指令返回预期的不支持响应","14190","测试disable touch optimization返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:53:34 CST 2025","passed","Wed Aug 06 11:53:48 CST 2025","TestEllaDisableTouchOptimization","test_disable_touch_optimization","",""
"navigation to the lucky","17096","测试navigation to the lucky能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:38:46 CST 2025","passed","Wed Aug 06 11:39:03 CST 2025","TestEllaNavigationToTheLucky","test_navigation_to_the_lucky","",""
"wake me up at 7:00 am tomorrow","13883","测试wake me up at 7:00 am tomorrow能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:34:00 CST 2025","passed","Wed Aug 06 11:34:14 CST 2025","TestEllaWakeMeUpAmTomorrow","test_wake_me_up_at_am_tomorrow","",""
"how to say hello in french","12598","测试how to say hello in french能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:41:57 CST 2025","passed","Wed Aug 06 10:42:09 CST 2025","TestEllaHowSayHelloFrench","test_how_to_say_hello_in_french","",""
"验证turn on show battery percentage指令返回预期的不支持响应","14992","测试turn on show battery percentage返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:44:49 CST 2025","passed","Wed Aug 06 12:45:04 CST 2025","TestEllaTurnShowBatteryPercentage","test_turn_on_show_battery_percentage","",""
"使用open clock命令，验证响应包含Done且实际打开clock命令","20675","open clock","testcases.test_ella.component_coupling","Wed Aug 06 10:20:45 CST 2025","passed","Wed Aug 06 10:21:05 CST 2025","TestEllaCommandConcise","test_open_clock","",""
"验证disable running lock指令返回预期的不支持响应","15427","测试disable running lock返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:53:05 CST 2025","passed","Wed Aug 06 11:53:20 CST 2025","TestEllaDisableRunningLock","test_disable_running_lock","",""
"record audio for 5 seconds","14999","测试record audio for 5 seconds能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:29:04 CST 2025","failed","Wed Aug 06 10:29:19 CST 2025","TestEllaRecordAudioSeconds","test_record_audio_for_seconds","",""
"验证set screen to minimum brightness指令返回预期的不支持响应","14265","测试set screen to minimum brightness返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:36:06 CST 2025","passed","Wed Aug 06 12:36:20 CST 2025","TestEllaSetScreenMinimumBrightness","test_set_screen_to_minimum_brightness","",""
"验证disable zonetouch master指令返回预期的不支持响应","13862","测试disable zonetouch master返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:54:31 CST 2025","passed","Wed Aug 06 11:54:45 CST 2025","TestEllaDisableZonetouchMaster","test_disable_zonetouch_master","",""
"测试set an alarm at 8 am指令","15284","测试set an alarm at 8 am","testcases.test_ella.component_coupling","Wed Aug 06 10:30:01 CST 2025","passed","Wed Aug 06 10:30:16 CST 2025","TestEllaOpenClock","test_set_an_alarm_at_8_am","",""
"验证enable zonetouch master指令返回预期的不支持响应","14145","测试enable zonetouch master返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:00:49 CST 2025","passed","Wed Aug 06 12:01:03 CST 2025","TestEllaEnableZonetouchMaster","test_enable_zonetouch_master","",""
"验证set floating windows指令返回预期的不支持响应","13770","测试set floating windows返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:28:43 CST 2025","passed","Wed Aug 06 12:28:57 CST 2025","TestEllaSetFloatingWindows","test_set_floating_windows","",""
"check my to-do list","0","测试check my to-do list能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 11:45:23 CST 2025","failed","Wed Aug 06 11:45:23 CST 2025","TestEllaCheckMyDoList","test_check_my_to_do_list","",""
"测试open bt指令","13739","测试open bt","testcases.test_ella.system_coupling","Wed Aug 06 11:12:00 CST 2025","passed","Wed Aug 06 11:12:13 CST 2025","TestEllaOpenBluetooth","test_open_bt","",""
"turn on the flashlight","15810","测试turn on the flashlight能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:31:57 CST 2025","passed","Wed Aug 06 11:32:13 CST 2025","TestEllaTurnFlashlight","test_turn_on_the_flashlight","",""
"验证fly to the moon指令返回预期的不支持响应","28052","测试fly to the moon返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:01:44 CST 2025","failed","Wed Aug 06 12:02:12 CST 2025","TestEllaFlyMoon","test_fly_to_the_moon","",""
"验证turn on high brightness mode指令返回预期的不支持响应","14035","测试turn on high brightness mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:44:22 CST 2025","passed","Wed Aug 06 12:44:36 CST 2025","TestEllaTurnHighBrightnessMode","test_turn_on_high_brightness_mode","",""
"What languages do you support","13787","测试What languages do you support能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:55:47 CST 2025","passed","Wed Aug 06 10:56:01 CST 2025","TestEllaWhatLanguagesDoYouSupport","test_what_languages_do_you_support","",""
"验证set my fonts指令返回预期的不支持响应","13762","测试set my fonts返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:31:32 CST 2025","passed","Wed Aug 06 12:31:45 CST 2025","TestEllaSetMyFonts","test_set_my_fonts","",""
"验证book a flight to paris指令返回预期的不支持响应","15635","测试book a flight to paris返回正确的不支持响应","testcases.test_ella.dialogue","Wed Aug 06 10:33:11 CST 2025","broken","Wed Aug 06 10:33:27 CST 2025","TestEllaBookFlightParis","test_book_a_flight_to_paris","",""
"验证jump to battery and power saving指令返回预期的不支持响应","15330","测试jump to battery and power saving返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:06:42 CST 2025","passed","Wed Aug 06 12:06:57 CST 2025","TestEllaJumpBatteryPowerSaving","test_jump_to_battery_and_power_saving","",""
"验证check mobile data balance of sim2指令返回预期的不支持响应","0","测试check mobile data balance of sim2返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:43:38 CST 2025","failed","Wed Aug 06 11:43:38 CST 2025","TestEllaCheckMobileDataBalanceSim","test_check_mobile_data_balance_of_sim","",""
"turn on location services","15039","测试turn on location services能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:31:28 CST 2025","passed","Wed Aug 06 11:31:43 CST 2025","TestEllaTurnLocationServices","test_turn_on_location_services","",""
"使用简化的测试框架测试phonemaster开启命令，验证响应包含Done且实际打开PhoneMaster","32216","测试clear junk files命令","testcases.test_ella.system_coupling","Wed Aug 06 11:04:02 CST 2025","failed","Wed Aug 06 11:04:35 CST 2025","TestEllaClearJunkFiles","test_clear_junk_files","",""
"set Battery Saver setting","23393","测试set Battery Saver setting能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:15:02 CST 2025","passed","Wed Aug 06 11:15:25 CST 2025","TestEllaSetBatterySaverSetting","test_set_battery_saver_setting","",""
"验证set cover screen apps指令返回预期的不支持响应","13631","测试set cover screen apps返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:25:57 CST 2025","passed","Wed Aug 06 12:26:11 CST 2025","TestEllaSetCoverScreenApps","test_set_cover_screen_apps","",""
"Switch to Barrage Notification","13599","测试Switch to Barrage Notification能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:21:40 CST 2025","passed","Wed Aug 06 11:21:54 CST 2025","TestEllaSwitchBarrageNotification","test_switch_to_barrage_notification","",""
"turn on light theme","13225","测试turn on light theme能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:31:01 CST 2025","passed","Wed Aug 06 11:31:14 CST 2025","TestEllaTurnLightTheme","test_turn_on_light_theme","",""
"测试Add the images and text on the screen to the note指令","13834","测试Add the images and text on the screen to the note","testcases.test_ella.unsupported_commands","Wed Aug 06 11:42:13 CST 2025","failed","Wed Aug 06 11:42:27 CST 2025","TestEllaOpenPlayPoliticalNews","test_Add_the_images_and_text_on_the_screen_to_the_note","",""
"continue  screen recording","16241","continue  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:18:14 CST 2025","passed","Wed Aug 06 11:18:30 CST 2025","TestEllaStartScreenRecording","test_start_screen_recording","",""
"check rear camera information","0","测试check rear camera information能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 11:45:58 CST 2025","failed","Wed Aug 06 11:45:58 CST 2025","TestEllaCheckRearCameraInformation","test_check_rear_camera_information","",""
"验证open font family settings指令返回预期的不支持响应","16057","测试open font family settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:12:20 CST 2025","passed","Wed Aug 06 12:12:36 CST 2025","TestEllaOpenSettings","test_open_font_family_settings","",""
"验证more settings指令返回预期的不支持响应","16874","测试more settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:10:52 CST 2025","passed","Wed Aug 06 12:11:09 CST 2025","TestEllaMoreSettings","test_more_settings","",""
"close wifi","13153","测试close wifi能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:05:46 CST 2025","failed","Wed Aug 06 11:05:59 CST 2025","TestEllaCloseWifi","test_close_wifi","",""
"call mom through whatsapp","23051","测试call mom through whatsapp能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:33:42 CST 2025","failed","Wed Aug 06 10:34:05 CST 2025","TestEllaCallMomThroughWhatsapp","test_call_mom_through_whatsapp","",""
"could you please search an for me","14847","测试could you please search an for me能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:36:19 CST 2025","failed","Wed Aug 06 10:36:34 CST 2025","TestEllaCouldYouPleaseSearchAnMe","test_could_you_please_search_an_for_me","",""
"验证Enable Call on Hold指令返回预期的不支持响应","22779","测试Enable Call on Hold返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:57:47 CST 2025","passed","Wed Aug 06 11:58:10 CST 2025","TestEllaEnableCallHold","test_enable_call_on_hold","",""
"验证set languages指令返回预期的不支持响应","15356","测试set languages返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:30:35 CST 2025","failed","Wed Aug 06 12:30:50 CST 2025","TestEllaSetLanguages","test_set_languages","",""
"turn on the screen record","17939","测试turn on the screen record能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:19:17 CST 2025","passed","Wed Aug 06 11:19:35 CST 2025","TestEllaTurnScreenRecord","test_stop_recording","",""
"close phonemaster","13790","测试close phonemaster能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:16:55 CST 2025","passed","Wed Aug 06 10:17:09 CST 2025","TestEllaClosePhonemaster","test_close_phonemaster","",""
"测试open bluetooth指令","13512","测试open bluetooth","testcases.test_ella.system_coupling","Wed Aug 06 11:11:32 CST 2025","passed","Wed Aug 06 11:11:46 CST 2025","TestEllaOpenBluetooth","test_open_bluetooth","",""
"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier","16427","测试open flashlight","testcases.test_ella.system_coupling","Wed Aug 06 11:12:28 CST 2025","passed","Wed Aug 06 11:12:44 CST 2025","TestEllaCommandConcise","test_open_flashlight","",""
"pause fm","13732","测试pause fm能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:24:30 CST 2025","passed","Wed Aug 06 10:24:43 CST 2025","TestEllaPauseFm","test_pause_fm","",""
"验证enable accelerate dialogue指令返回预期的不支持响应","14739","测试enable accelerate dialogue返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:55:55 CST 2025","failed","Wed Aug 06 11:56:10 CST 2025","TestEllaEnableAccelerateDialogue","test_enable_accelerate_dialogue","",""
"验证set folding screen zone指令返回预期的不支持响应","13891","测试set folding screen zone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:29:11 CST 2025","passed","Wed Aug 06 12:29:25 CST 2025","TestEllaSetFoldingScreenZone","test_set_folding_screen_zone","",""
"maximum volume","13328","测试maximum volume能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:09:53 CST 2025","failed","Wed Aug 06 11:10:06 CST 2025","TestEllaMaximumVolume","test_maximum_volume","",""
"Switch to Hyper Charge","13377","测试Switch to Hyper Charge能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:23:50 CST 2025","failed","Wed Aug 06 11:24:04 CST 2025","TestEllaSwitchToHyperCharge","test_switch_to_hyper_charge","",""
"验证reset phone指令返回预期的不支持响应","13783","测试reset phone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:19:32 CST 2025","passed","Wed Aug 06 12:19:46 CST 2025","TestEllaResetPhone","test_reset_phone","",""
"take notes on how to build a treehouse","13941","测试take notes on how to build a treehouse能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:53:44 CST 2025","passed","Wed Aug 06 10:53:58 CST 2025","TestEllaTakeNotesHowBuildTreehouse","test_take_notes_on_how_to_build_a_treehouse","",""
"测试play video by youtube指令","15860","测试play video by youtube","testcases.test_ella.unsupported_commands","Wed Aug 06 12:17:56 CST 2025","passed","Wed Aug 06 12:18:12 CST 2025","TestEllaOpenPlayPoliticalNews","test_play_video_by_youtube","",""
"who is j k rowling","16280","测试who is j k rowling能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:59:53 CST 2025","passed","Wed Aug 06 11:00:09 CST 2025","TestEllaWhoIsJKRowling","test_who_is_j_k_rowling","",""
"测试open camera指令","18221","测试open camera","testcases.test_ella.open_app","Wed Aug 06 11:01:58 CST 2025","passed","Wed Aug 06 11:02:16 CST 2025","TestEllaOpenCamera","test_open_camera","",""
"验证order a takeaway指令返回预期的不支持响应","13773","测试order a takeaway返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:14:15 CST 2025","failed","Wed Aug 06 12:14:28 CST 2025","TestEllaOrderTakeaway","test_order_a_takeaway","",""
"验证set flip case feature指令返回预期的不支持响应","13400","测试set flip case feature返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:28:16 CST 2025","passed","Wed Aug 06 12:28:29 CST 2025","TestEllaSetFlipCaseFeature","test_set_flip_case_feature","",""
"验证close performance mode指令返回预期的不支持响应","0","测试close performance mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:47:08 CST 2025","failed","Wed Aug 06 11:47:08 CST 2025","TestEllaClosePerformanceMode","test_close_performance_mode","",""
"what is apec?","15706","测试what is apec?能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:55:17 CST 2025","passed","Wed Aug 06 10:55:33 CST 2025","TestEllaWhatIsApec","test_what_is_apec","",""
"验证set sim1 ringtone指令返回预期的不支持响应","14189","测试set sim1 ringtone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:36:33 CST 2025","passed","Wed Aug 06 12:36:47 CST 2025","TestEllaSetSimRingtone","test_set_sim_ringtone","",""
"download basketball","15075","测试download basketball能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:35:26 CST 2025","passed","Wed Aug 06 11:35:41 CST 2025","TestEllaDownloadBasketball","test_download_basketball","",""
"验证disable hide notifications指令返回预期的不支持响应","0","测试disable hide notifications返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:51:14 CST 2025","failed","Wed Aug 06 11:51:14 CST 2025","TestEllaDisableHideNotifications","test_disable_hide_notifications","",""
"turn on light theme","13604","测试turn on light theme能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:30:33 CST 2025","passed","Wed Aug 06 11:30:47 CST 2025","TestEllaTurnLightTheme","test_turn_on_light_theme","",""
"summarize what i'm reading","13616","测试summarize what i'm reading能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:52:20 CST 2025","passed","Wed Aug 06 10:52:33 CST 2025","TestEllaSummarizeWhatIMReading","test_summarize_what_i_m_reading","",""
"turn off flashlight","16114","测试turn off flashlight能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:28:39 CST 2025","passed","Wed Aug 06 11:28:55 CST 2025","TestEllaTurnOffFlashlight","test_turn_off_flashlight","",""
"restart my phone","14216","测试restart my phone能正常执行","testcases.test_ella.unsupported_commands","Wed Aug 06 12:20:00 CST 2025","passed","Wed Aug 06 12:20:14 CST 2025","TestEllaRestartMyPhone","test_restart_my_phone","",""
"navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai","18250","测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行","testcases.test_ella.third_coupling","Wed Aug 06 11:37:05 CST 2025","passed","Wed Aug 06 11:37:24 CST 2025","TestEllaNavigateFromBeijingShanghai","test_navigate_from_beijing_to_shanghai","",""
"take a photo","30162","测试take a photo能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:26:12 CST 2025","passed","Wed Aug 06 11:26:42 CST 2025","TestEllaTakePhoto","test_take_a_photo","",""
"stop  screen recording","18443","stop  screen recording能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:18:45 CST 2025","passed","Wed Aug 06 11:19:03 CST 2025","TestEllaStartScreenRecording","test_start_screen_recording","",""
"验证how's the weather today?指令返回预期的不支持响应","20835","测试how's the weather today?返回正确的不支持响应","testcases.test_ella.dialogue","Wed Aug 06 10:40:46 CST 2025","passed","Wed Aug 06 10:41:07 CST 2025","TestEllaHowSWeatherToday","test_how_s_the_weather_today","",""
"验证set parallel windows指令返回预期的不支持响应","14134","测试set parallel windows返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:32:27 CST 2025","passed","Wed Aug 06 12:32:41 CST 2025","TestEllaSetParallelWindows","test_set_parallel_windows","",""
"my phone is too slow","14571","测试my phone is too slow能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:19:18 CST 2025","passed","Wed Aug 06 10:19:32 CST 2025","TestEllaMyPhoneIsTooSlow","test_my_phone_is_too_slow","",""
"turn on do not disturb mode","14725","测试turn on do not disturb mode能正常执行","testcases.test_ella.system_coupling","Wed Aug 06 11:30:04 CST 2025","passed","Wed Aug 06 11:30:19 CST 2025","TestEllaTurnDoNotDisturbMode","test_turn_on_do_not_disturb_mode","",""
"验证set ultra power saving指令返回预期的不支持响应","14988","测试set ultra power saving返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:39:16 CST 2025","passed","Wed Aug 06 12:39:31 CST 2025","TestEllaSetUltraPowerSaving","test_set_ultra_power_saving","",""
"next channel","13501","测试next channel能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:19:46 CST 2025","passed","Wed Aug 06 10:20:00 CST 2025","TestEllaNextChannel","test_next_channel","",""
"验证set font size指令返回预期的不支持响应","13991","测试set font size返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:29:39 CST 2025","passed","Wed Aug 06 12:29:53 CST 2025","TestEllaSetFontSize","test_set_font_size","",""
"验证jump to auto rotate screen settings指令返回预期的不支持响应","23205","测试jump to auto rotate screen settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 12:06:05 CST 2025","passed","Wed Aug 06 12:06:28 CST 2025","TestEllaJumpAutoRotateScreenSettings","test_jump_to_auto_rotate_screen_settings","",""
"how to say i love you in french","12155","测试how to say i love you in french能正常执行","testcases.test_ella.dialogue","Wed Aug 06 10:42:23 CST 2025","passed","Wed Aug 06 10:42:35 CST 2025","TestEllaHowSayILoveYouFrench","test_how_to_say_i_love_you_in_french","",""
"close aivana","34399","测试close aivana能正常执行","testcases.test_ella.component_coupling","Wed Aug 06 10:14:34 CST 2025","passed","Wed Aug 06 10:15:08 CST 2025","TestEllaCloseAivana","test_close_aivana","",""
"验证disable network enhancement指令返回预期的不支持响应","14008","测试disable network enhancement返回正确的不支持响应","testcases.test_ella.unsupported_commands","Wed Aug 06 11:52:37 CST 2025","passed","Wed Aug 06 11:52:51 CST 2025","TestEllaDisableNetworkEnhancement","test_disable_network_enhancement","",""
