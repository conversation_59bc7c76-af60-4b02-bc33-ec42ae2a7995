{"uid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "name": "timeline", "children": [{"name": "SHCYbucy-pc", "children": [{"name": "37684-MainThread", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "af04ac0c8051c95f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401925659, "stop": 1754401943496, "duration": 17837}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to flash notification能正常执行", "uid": "4c93385049189d57", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400758701, "stop": 1754400781767, "duration": 23066}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试show scores between livepool and manchester city能正常执行", "uid": "a0eaae1088e3dc8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398800120, "stop": 1754398814640, "duration": 14520}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "57fc86b27e49842c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400558088, "stop": 1754400569708, "duration": 11620}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试the second返回正确的不支持响应", "uid": "40bc34a3cc2c250e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405393876, "stop": 1754405408167, "duration": 14291}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start run能正常执行", "uid": "80ad1f560a8132", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397720302, "stop": 1754397735155, "duration": 14853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to say i love you in french能正常执行", "uid": "62acaa3041ed52b0", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398412864, "stop": 1754398425777, "duration": 12913}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on do not disturb mode能正常执行", "uid": "588cc2abfa696660", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401159628, "stop": 1754401171063, "duration": 11435}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger能正常执行", "uid": "188c1a450a54388", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401764650, "stop": 1754401775527, "duration": 10877}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set battery saver settings返回正确的不支持响应", "uid": "40c9df1680c5103b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404243130, "stop": 1754404260113, "duration": 16983}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "b834f5b409e39032", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399818688, "stop": 1754399837021, "duration": 18333}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试redial", "uid": "bee0110354c87879", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403948451, "stop": 1754403970128, "duration": 21677}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop playing", "uid": "51c874c21ac94a95", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397750420, "stop": 1754397765026, "duration": 14606}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试video call mom through whatsapp能正常执行", "uid": "4913c47f5cd68c2a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399076052, "stop": 1754399097766, "duration": 21714}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试my phone is too slow能正常执行", "uid": "1b43953d17bc05b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397068436, "stop": 1754397081716, "duration": 13280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set an alarm at 8 am", "uid": "ecd122ba3ea3ea0a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397691760, "stop": 1754397706148, "duration": 14388}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my fonts返回正确的不支持响应", "uid": "9b35f6ec3ea1d67b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404699227, "stop": 1754404714882, "duration": 15655}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试parking space能正常执行", "uid": "ec27c6ea931f67eb", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403699092, "stop": 1754403709862, "duration": 10770}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play rock music", "uid": "ad16fe59d0da691", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397570511, "stop": 1754397586519, "duration": 16008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play sun be song of jide chord", "uid": "3cf415527b3ee855", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397600817, "stop": 1754397622053, "duration": 21236}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play taylor swift‘s song love story", "uid": "6605d0889363ec4c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403791398, "stop": 1754403811508, "duration": 20110}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i wanna be rich能正常执行", "uid": "a497a4f10cf03219", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398439998, "stop": 1754398454283, "duration": 14285}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set customized cover screen返回正确的不支持响应", "uid": "9f731d6196e1f1ac", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404392683, "stop": 1754404403881, "duration": 11198}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "cdc10dab0dde50b2", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401215884, "stop": 1754401232339, "duration": 16455}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn down ring volume能正常执行", "uid": "9afebd272e1deed4", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401051937, "stop": 1754401063083, "duration": 11146}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试why my charging is so slow能正常执行", "uid": "b51578cd34e6c8bf", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399460367, "stop": 1754399473366, "duration": 12999}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "2f514b5b655f5882", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403593327, "stop": 1754403606500, "duration": 13173}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play afro strut", "uid": "d43ecfde8b649b6b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397434366, "stop": 1754397456408, "duration": 22042}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试find a restaurant near me能正常执行", "uid": "d69eace3c8b6238f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754401538293, "stop": 1754401562244, "duration": 23951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an thanks email能正常执行", "uid": "18bae186eb61c6cc", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402987211, "stop": 1754403000102, "duration": 12891}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "756083cb7af5dc78", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399852007, "stop": 1754399869808, "duration": 17801}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "55e0ac33cc98ced8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754402009861, "stop": 1754402022784, "duration": 12923}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "ab06ac7d19247ec4", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403078975, "stop": 1754403100894, "duration": 21919}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop run能正常执行", "uid": "5b34488539e56b9b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398855952, "stop": 1754398868928, "duration": 12976}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open whatsapp", "uid": "330442fca2780dc6", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403920981, "stop": 1754403933642, "duration": 12661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to make a call能正常执行", "uid": "5a857d261f4a2ea1", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398468360, "stop": 1754398490137, "duration": 21777}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an email能正常执行", "uid": "2873ce24215c8fc8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754402960040, "stop": 1754402973051, "duration": 13011}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set parallel windows返回正确的不支持响应", "uid": "f32acb276b2d0dad", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404758128, "stop": 1754404769200, "duration": 11072}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open camera能正常执行", "uid": "30504da5d19f1072", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397121136, "stop": 1754397138079, "duration": 16943}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set ultra power saving返回正确的不支持响应", "uid": "ff5b7c2d1d514320", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405190396, "stop": 1754405201735, "duration": 11339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试long screenshot能正常执行", "uid": "e2f40efc3b65c3eb", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399945669, "stop": 1754399960605, "duration": 14936}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试who is j k rowling能正常执行", "uid": "4cf52d0e6ea4d2ce", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399383324, "stop": 1754399409890, "duration": 26566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode能正常执行", "uid": "b58d08985d1ac10b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400727493, "stop": 1754400743612, "duration": 16119}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试yandex eats返回正确的不支持响应", "uid": "fb766181f848b01", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405652567, "stop": 1754405663727, "duration": 11160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "1af053dc3643bc05", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404859827, "stop": 1754404876609, "duration": 16782}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play love sotry", "uid": "f37e6490153621f4", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403755698, "stop": 1754403776505, "duration": 20807}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger返回正确的不支持响应", "uid": "334c8da3f86543b7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403647812, "stop": 1754403658679, "duration": 10867}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switched to data mode能正常执行", "uid": "4105704213c94fc", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400899575, "stop": 1754400911739, "duration": 12164}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试next channel能正常执行", "uid": "a34c1115fd12a8e9", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397094717, "stop": 1754397107416, "duration": 12699}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "8b0c15bf0ba50935", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400466252, "stop": 1754400479788, "duration": 13536}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music", "uid": "c7e528d9e62712d9", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397536624, "stop": 1754397556389, "duration": 19765}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close bluetooth能正常执行", "uid": "9b826ffb23635fb1", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399680265, "stop": 1754399692577, "duration": 12312}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what is apec?能正常执行", "uid": "38124d92f03aa953", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399112436, "stop": 1754399127749, "duration": 15313}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "d0bc974b322cdb0c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397807292, "stop": 1754397827627, "duration": 20335}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "21f4016d44e1d422", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404667548, "stop": 1754404684724, "duration": 17176}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate to shanghai disneyland能正常执行", "uid": "743aa2515bc2c753", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401643543, "stop": 1754401663291, "duration": 19748}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "eaadeb82cfe0a2ae", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400822502, "stop": 1754400833347, "duration": 10845}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open bt", "uid": "63d04327f0971163", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400098871, "stop": 1754400111246, "duration": 12375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open camera", "uid": "1dfd12c1bd7d35e2", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399517612, "stop": 1754399534201, "duration": 16589}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable touch optimization返回正确的不支持响应", "uid": "1013019944286f04", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402829069, "stop": 1754402840209, "duration": 11140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play jay chou's music by spotify", "uid": "9e170c72aa5cf03f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397506758, "stop": 1754397522067, "duration": 15309}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "a5310af2de506bcb", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404274451, "stop": 1754404294889, "duration": 20438}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to smart charge能正常执行", "uid": "b1851fb028947ab1", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400873724, "stop": 1754400884683, "duration": 10959}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试change your language to chinese能正常执行", "uid": "1f38cfd1433da4f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399576019, "stop": 1754399587350, "duration": 11331}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open dialer能正常执行", "uid": "e4f52714d79f9e15", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397246702, "stop": 1754397270687, "duration": 23985}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "5cc59b04e378c1af", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754396979911, "stop": 1754396995017, "duration": 15106}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how is the weather today能正常执行", "uid": "9c64e1e3896872ea", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398256807, "stop": 1754398276706, "duration": 19899}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what·s the weather today？能正常执行", "uid": "5c0db2c76d746ab7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399203309, "stop": 1754399223188, "duration": 19879}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "679d38728b39c6c6", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400641059, "stop": 1754400652177, "duration": 11118}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试countdown 5 min能正常执行", "uid": "ffa4837733e154ab", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399761450, "stop": 1754399776104, "duration": 14654}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the first address in the image能正常执行", "uid": "ea81cfda8b678e9e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "broken", "time": {"start": 1754403534392, "stop": 1754403547438, "duration": 13046}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "99ed628eb665a54a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404444272, "stop": 1754404456485, "duration": 12213}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate from to red square能正常执行", "uid": "30df45280102ccec", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401608878, "stop": 1754401629332, "duration": 20454}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set folding screen zone返回正确的不支持响应", "uid": "45cdb921cad6925b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404553433, "stop": 1754404564515, "duration": 11082}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试display the route go company", "uid": "cb4e44e83a418051", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397041032, "stop": 1754397054977, "duration": 13945}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试tell me a joke能正常执行", "uid": "7ec1e9439e3e16a0", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399047608, "stop": 1754399061089, "duration": 13481}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set languages返回正确的不支持响应", "uid": "d3acbbedc56c2e8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404639547, "stop": 1754404652600, "duration": 13053}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "ad8a0a6175e3f59c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754398741408, "stop": 1754398757082, "duration": 15674}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close flashlight能正常执行", "uid": "63d26b6e24a592f8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399707311, "stop": 1754399720622, "duration": 13311}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "66f0cdbcc09a1110", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401396308, "stop": 1754401408555, "duration": 12247}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "eddc069dcdd677ff", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402502346, "stop": 1754402515520, "duration": 13174}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start running能正常执行", "uid": "ca14de023bf241d2", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405216243, "stop": 1754405241833, "duration": 25590}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试vedio call number by whatsapp能正常执行", "uid": "9d40caa87747bafd", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405561605, "stop": 1754405582082, "duration": 20477}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试introduce yourself能正常执行", "uid": "597023f7a3056d7e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398533070, "stop": 1754398546898, "duration": 13828}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switching charging speed能正常执行", "uid": "2ae612c65a28130b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405363731, "stop": 1754405379363, "duration": 15632}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "de61ee6963d31004", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403446102, "stop": 1754403461551, "duration": 15449}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close phonemaster能正常执行", "uid": "d24e31670a5f18b1", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754396925746, "stop": 1754396938822, "duration": 13076}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off flashlight能正常执行", "uid": "b4b8e7b6dab5f8f3", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401078086, "stop": 1754401091361, "duration": 13275}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable touch optimization返回正确的不支持响应", "uid": "c11c3638efcf1ad4", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402450853, "stop": 1754402462134, "duration": 11281}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app auto rotate返回正确的不支持响应", "uid": "25b8505da9a1a699", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404187688, "stop": 1754404198725, "duration": 11037}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how is the wheather today能正常执行", "uid": "50ef96927d3bd86f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398290921, "stop": 1754398303892, "duration": 12971}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today in shanghai能正常执行", "uid": "81941d3cfb50de0e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398352397, "stop": 1754398372537, "duration": 20140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download app能正常执行", "uid": "6af1de0ba659e3b1", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401453214, "stop": 1754401465583, "duration": 12369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable call rejection返回正确的不支持响应", "uid": "b76ddcbe49a1b6ff", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402297102, "stop": 1754402321642, "duration": 24540}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set Battery Saver setting能正常执行", "uid": "93d302602f21d652", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400282194, "stop": 1754400303281, "duration": 21087}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the wheather today?能正常执行", "uid": "11ea2d6e7fe9fa5d", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399236964, "stop": 1754399249887, "duration": 12923}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set split-screen apps返回正确的不支持响应", "uid": "6a78bcdeb911f1dd", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405130127, "stop": 1754405143973, "duration": 13846}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball返回正确的不支持响应", "uid": "c3316fab4b6571cf", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402530444, "stop": 1754402543014, "duration": 12570}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check rear camera information能正常执行", "uid": "20614471f4c18344", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402064528, "stop": 1754402084545, "duration": 20017}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the weather like in shanghai today能正常执行", "uid": "2cb8ed69ebde6522", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399169453, "stop": 1754399189388, "duration": 19935}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试who is harry potter能正常执行", "uid": "4bf08d2af72213b8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399353992, "stop": 1754399369403, "duration": 15411}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phone number返回正确的不支持响应", "uid": "32cf0933330af47f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404834369, "stop": 1754404845296, "duration": 10927}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "34a1bf589c7c8604", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403621170, "stop": 1754403633347, "duration": 12177}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start walking能正常执行", "uid": "2ec5326da2311571", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405256136, "stop": 1754405267375, "duration": 11239}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a note on how to build a treehouse能正常执行", "uid": "79af219d3a0f25c5", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398993585, "stop": 1754399006413, "duration": 12828}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set floating windows返回正确的不支持响应", "uid": "5274bb6eade20550", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404523008, "stop": 1754404538860, "duration": 15852}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试global gdp trends能正常执行", "uid": "f60123fd513b8334", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398131113, "stop": 1754398158728, "duration": 27615}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause music能正常执行", "uid": "3209a336e36bcb0f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397406124, "stop": 1754397419872, "duration": 13748}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试can you give me a coin能正常执行", "uid": "a375fd272737defc", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397934145, "stop": 1754397947527, "duration": 13382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open folax能正常执行", "uid": "878c21bfb8fab771", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397312634, "stop": 1754397325399, "duration": 12765}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable unfreeze返回正确的不支持响应", "uid": "18fdeabe9705935a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402854717, "stop": 1754402865637, "duration": 10920}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to nfc settings", "uid": "ea1bfb9e3930e5e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403379376, "stop": 1754403399823, "duration": 20447}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试memory cleanup能正常执行", "uid": "42291cfbb47e2506", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400002929, "stop": 1754400028748, "duration": 25819}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试continue music能正常执行", "uid": "927966aed0675c11", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754396953010, "stop": 1754396965571, "duration": 12561}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable network enhancement返回正确的不支持响应", "uid": "3adbb40b5329d22f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402393253, "stop": 1754402404124, "duration": 10871}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set gesture navigation返回正确的不支持响应", "uid": "6528b6950a6c68ad", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404609497, "stop": 1754404624950, "duration": 15453}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试extend the image能正常执行", "uid": "c48dcd0bde5d5ef1", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402906528, "stop": 1754402917567, "duration": 11039}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable hide notifications返回正确的不支持响应", "uid": "47f1c2f244fda413", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402336630, "stop": 1754402350178, "duration": 13548}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试more settings返回正确的不支持响应", "uid": "9dccbf5fb1fc170c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403476099, "stop": 1754403491474, "duration": 15375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试screen record能正常执行", "uid": "a41258ab5dd976d3", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400219939, "stop": 1754400236945, "duration": 17006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "cde328be425470a7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405448464, "stop": 1754405459826, "duration": 11362}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "fe9a5830380cd3ea", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402181561, "stop": 1754402197701, "duration": 16140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close whatsapp能正常执行", "uid": "271ea11d2cdbc1c0", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398017912, "stop": 1754398032366, "duration": 14454}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable auto pickup返回正确的不支持响应", "uid": "8be4e7e21f9f1a96", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402634452, "stop": 1754402649409, "duration": 14957}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open bluetooth", "uid": "2cdea1c03876fbed", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400071923, "stop": 1754400084101, "duration": 12178}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a screenshot能正常执行", "uid": "b5b647eaf24aceba", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397779894, "stop": 1754397792684, "duration": 12790}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set special function返回正确的不支持响应", "uid": "5aa37400ba297f61", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405098798, "stop": 1754405115554, "duration": 16756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "98eeabad24fd12f3", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400375681, "stop": 1754400391399, "duration": 15718}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试make a call能正常执行", "uid": "7eb67937aa9b6276", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398561070, "stop": 1754398582929, "duration": 21859}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "32689990d4658391", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405504607, "stop": 1754405520793, "duration": 16186}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "1e019e20ec20ce56", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754398771652, "stop": 1754398785520, "duration": 13868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试give me some money能正常执行", "uid": "3798453d42ddef7a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398101857, "stop": 1754398116736, "duration": 14879}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off wifi能正常执行", "uid": "cc02ed079b866cfb", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401106177, "stop": 1754401118040, "duration": 11863}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close ella能正常执行", "uid": "ca1a3155aaeee8a9", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754396835533, "stop": 1754396865403, "duration": 29870}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "e1c42c9fcac6120d", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402771478, "stop": 1754402782822, "duration": 11344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "c20cff1223132437", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401013093, "stop": 1754401037140, "duration": 24047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "f5501468cee07820", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402583544, "stop": 1754402594430, "duration": 10886}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play football video by youtube", "uid": "129f15bf42fbd0cd", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403724419, "stop": 1754403741204, "duration": 16785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable brightness locking返回正确的不支持响应", "uid": "e478fca60fdae9ab", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402271555, "stop": 1754402282745, "duration": 11190}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable running lock返回正确的不支持响应", "uid": "970dcb3f0e912a2", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402418873, "stop": 1754402436211, "duration": 17338}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "open clock", "uid": "246dc31e75d9eca5", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397151722, "stop": 1754397171255, "duration": 19533}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set cover screen apps返回正确的不支持响应", "uid": "1fadd1f8d5aebf9b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404366958, "stop": 1754404378077, "duration": 11119}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today?返回正确的不支持响应", "uid": "8251282b801233c8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398318149, "stop": 1754398338348, "duration": 20199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check front camera information能正常执行", "uid": "6af28f88917964c3", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399601741, "stop": 1754399622536, "duration": 20795}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a photo能正常执行", "uid": "577708211108941c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400926509, "stop": 1754400955674, "duration": 29165}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "cb6143fe08ac5795", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405281915, "stop": 1754405297809, "duration": 15894}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery usage返回正确的不支持响应", "uid": "d731e6c34bccd94c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403248455, "stop": 1754403260041, "duration": 11586}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a selfie能正常执行", "uid": "779b0038aeae73fb", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754400970403, "stop": 1754400998323, "duration": 27920}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flip case feature返回正确的不支持响应", "uid": "2df5df1d22a2c47a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404497358, "stop": 1754404508427, "duration": 11069}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play video", "uid": "ec3a7ca0e904fb11", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403861515, "stop": 1754403876356, "duration": 14841}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试hi能正常执行", "uid": "3c57688e4eca8e05", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398228333, "stop": 1754398242164, "duration": 13831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "4dad5ef93da37ee6", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398200943, "stop": 1754398214377, "duration": 13434}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试search whatsapp for me能正常执行", "uid": "b8072c658b21cbc2", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404100567, "stop": 1754404116608, "duration": 16041}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "b488b943f0021eba", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402212625, "stop": 1754402226119, "duration": 13494}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close wifi能正常执行", "uid": "bb4ecca46b92cd6a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399735240, "stop": 1754399747160, "duration": 11920}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试decrease the brightness能正常执行", "uid": "3beca9a63a49807a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399791096, "stop": 1754399803582, "duration": 12486}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "ed8356a04fc7c716", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405535637, "stop": 1754405547072, "duration": 11435}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download qq能正常执行", "uid": "196cd951200f71ac", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401507682, "stop": 1754401523567, "duration": 15885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open font family settings返回正确的不支持响应", "uid": "8bcb19201db38cee", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403562055, "stop": 1754403578855, "duration": 16800}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "2da599cf356186e8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404131349, "stop": 1754404143949, "duration": 12600}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set font size返回正确的不支持响应", "uid": "c32be5965af03bcf", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404579366, "stop": 1754404594795, "duration": 15429}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试searching for a method of violent murder能正常执行", "uid": "9d96b05ae7681b23", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "broken", "time": {"start": 1754398712082, "stop": 1754398726794, "duration": 14712}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me take a screenshot能正常执行", "uid": "6827be8ffca997a7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399916015, "stop": 1754399931170, "duration": 15155}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试call mom through whatsapp能正常执行", "uid": "54e738c53cf73eb3", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397898261, "stop": 1754397919743, "duration": 21482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway返回正确的不支持响应", "uid": "95c98159f5e501ad", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403673402, "stop": 1754403684507, "duration": 11105}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "eb491126c83dca0a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404979662, "stop": 1754404997002, "duration": 17340}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close folax能正常执行", "uid": "4aabdb885aa35939", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754396879640, "stop": 1754396910986, "duration": 31346}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phantom v pen返回正确的不支持响应", "uid": "fc8ccc288e2ceac3", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404808870, "stop": 1754404819945, "duration": 11075}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off driving mode返回正确的不支持响应", "uid": "a64c108c7b429dc8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405422601, "stop": 1754405433840, "duration": 11239}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "5d3c0e1fecb3a3f1", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403115613, "stop": 1754403132332, "duration": 16719}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Search for addresses on the screen能正常执行", "uid": "b48c90dea488094b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404041726, "stop": 1754404056282, "duration": 14556}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "b00aec770d138d6e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404158739, "stop": 1754404173044, "duration": 14305}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open the newest whatsapp activity", "uid": "c1aa2c3ecd7e41d8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401815867, "stop": 1754401828049, "duration": 12182}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable brightness locking返回正确的不支持响应", "uid": "bd8eb7c2a266edaf", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402664220, "stop": 1754402680972, "duration": 16752}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable unfreeze返回正确的不支持响应", "uid": "84b9b1a573ce6760", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402476742, "stop": 1754402487775, "duration": 11033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set personal hotspot返回正确的不支持响应", "uid": "e6cb42f76280e035", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404783659, "stop": 1754404794243, "duration": 10584}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop music能正常执行", "uid": "51941e8f0d4c7860", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754398828682, "stop": 1754398841380, "duration": 12698}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open facebook能正常执行", "uid": "cff36dac1ac2240", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401705877, "stop": 1754401723302, "duration": 17425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode能正常执行", "uid": "cf90ef5fa79481d6", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400847767, "stop": 1754400859116, "duration": 11349}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "f76ef410d43dcd0", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405338253, "stop": 1754405349424, "duration": 11171}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Barrage Notification能正常执行", "uid": "6a6d8fe8c58d434f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400667033, "stop": 1754400683004, "duration": 15971}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to performance mode返回正确的不支持响应", "uid": "7cadc9bc33f94b5e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405312469, "stop": 1754405323635, "duration": 11166}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "f6b5180ed5339c89", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401576339, "stop": 1754401594234, "duration": 17895}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "2c48949f3559eed4", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397285450, "stop": 1754397298256, "duration": 12806}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause fm能正常执行", "uid": "dac25dfd4e1508f2", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397377552, "stop": 1754397391767, "duration": 14215}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to call notifications返回正确的不支持响应", "uid": "84d4f841cbe28d47", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403274939, "stop": 1754403299198, "duration": 24259}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试maximum volume能正常执行", "uid": "4a68482227702cf7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399975318, "stop": 1754399987865, "duration": 12547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "579535b7c694d7cc", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397185412, "stop": 1754397203564, "duration": 18152}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "bf5eddc2520444f8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398597132, "stop": 1754398609851, "duration": 12719}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "745c1794a29194a9", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400494936, "stop": 1754400511085, "duration": 16149}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on driving mode返回正确的不支持响应", "uid": "3b10cf576dc64f7b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405474408, "stop": 1754405490084, "duration": 15676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check model information返回正确的不支持响应", "uid": "a3954dcfa2acffd2", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401984135, "stop": 1754401995195, "duration": 11060}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试minimum volume能正常执行", "uid": "c5f687104531f3a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400043877, "stop": 1754400056644, "duration": 12767}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen relay返回正确的不支持响应", "uid": "ce41b794e3ffc806", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404923078, "stop": 1754404933925, "duration": 10847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试where is the carlcare service outlet能正常执行", "uid": "7eec2b03f82f0c85", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401423573, "stop": 1754401438589, "duration": 15016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen timeout返回正确的不支持响应", "uid": "10451cd4b3c7db1c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404948799, "stop": 1754404964797, "duration": 15998}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "5cec133f5450d84e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405011273, "stop": 1754405027766, "duration": 16493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open countdown能正常执行", "uid": "44ac90fcb6e8fa5a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397218385, "stop": 1754397231515, "duration": 13130}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "aed6ce87b87238ad", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400251556, "stop": 1754400267549, "duration": 15993}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "b7c0a432afe503a5", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403147273, "stop": 1754403168331, "duration": 21058}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试hello hello能正常执行", "uid": "596722b4afad1463", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398173047, "stop": 1754398186506, "duration": 13459}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试smart charge能正常执行", "uid": "7116c0127f60d650", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400317923, "stop": 1754400328908, "duration": 10985}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Hyper Charge能正常执行", "uid": "8f9dc1d18ed551f7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400796736, "stop": 1754400807723, "duration": 10987}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试summarize content on this page能正常执行", "uid": "ea6730154255a6b7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398910901, "stop": 1754398923728, "duration": 12827}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "b4475b9055e1a3e8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403414558, "stop": 1754403431585, "duration": 17027}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试restart my phone能正常执行", "uid": "9d949e2dd712a5a6", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404016011, "stop": 1754404027312, "duration": 11301}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on bluetooth能正常执行", "uid": "554b736d8a926fdb", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401132691, "stop": 1754401145172, "duration": 12481}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to say hello in french能正常执行", "uid": "5da7d9303360c66f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398386919, "stop": 1754398398598, "duration": 11679}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app notifications返回正确的不支持响应", "uid": "56effd5d424bdbc7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404213403, "stop": 1754404228453, "duration": 15050}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试record audio for 5 seconds能正常执行", "uid": "b0e55cceb32362", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397636386, "stop": 1754397650034, "duration": 13648}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop workout能正常执行", "uid": "af75142628110a3f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398883448, "stop": 1754398896386, "duration": 12938}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today in shanghai返回正确的不支持响应", "uid": "2d93085906a386de", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403014783, "stop": 1754403034397, "duration": 19614}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "492c4e9d6bef3996", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401338276, "stop": 1754401354965, "duration": 16689}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试appeler maman能正常执行", "uid": "73784dd66b275a1b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397841751, "stop": 1754397854561, "duration": 12810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start screen recording能正常执行", "uid": "c29e74db44408169", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400405853, "stop": 1754400422784, "duration": 16931}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试search the address in the image能正常执行", "uid": "5703aea948c4de84", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404070890, "stop": 1754404086053, "duration": 15163}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set compatibility mode返回正确的不支持响应", "uid": "e293087c57db4456", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404341419, "stop": 1754404352417, "duration": 10998}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "1557f763d7b18e05", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401306113, "stop": 1754401323288, "duration": 17175}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take notes on how to build a treehouse能正常执行", "uid": "784ae7c2d07c1340", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399020354, "stop": 1754399033277, "duration": 12923}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试cannot login in google email box能正常执行", "uid": "3892ac10f825702f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397962237, "stop": 1754397974946, "duration": 12709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试book a flight to paris返回正确的不支持响应", "uid": "b57d9700043bf78", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "broken", "time": {"start": 1754397868550, "stop": 1754397883663, "duration": 15113}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "e62b49bee8a5de1d", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400615179, "stop": 1754400626214, "duration": 11035}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "9438516b06c8c4e2", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401185554, "stop": 1754401201577, "duration": 16023}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "92f44c195b9b178d", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403183004, "stop": 1754403204054, "duration": 21050}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "225ed715c239151a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403313855, "stop": 1754403330303, "duration": 16448}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open wifi", "uid": "916fcb5f12a4c9fe", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400153609, "stop": 1754400166131, "duration": 12522}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "75a0e9720c6254c6", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400525767, "stop": 1754400542955, "duration": 17188}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch charging modes能正常执行", "uid": "72c0fc1153de8f93", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400584307, "stop": 1754400599584, "duration": 15277}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the lucky能正常执行", "uid": "5a4f4a2734888530", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754401677753, "stop": 1754401691699, "duration": 13946}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "c0a8be39dfce2faf", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397339907, "stop": 1754397363132, "duration": 23225}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on location services能正常执行", "uid": "68e32977e11e378", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401246956, "stop": 1754401263142, "duration": 16186}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "9a66b50119d7552a", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402099347, "stop": 1754402115187, "duration": 15840}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway能正常执行", "uid": "d0aac7516b3fffec", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401790422, "stop": 1754401801300, "duration": 10878}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a joke能正常执行", "uid": "6d749867b8a13d8e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398965423, "stop": 1754398979207, "duration": 13784}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play the album", "uid": "213bfb25d0122e54", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403826096, "stop": 1754403846789, "duration": 20693}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试clear junk files命令", "uid": "39fc1b9c2207ff3c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399637231, "stop": 1754399665281, "duration": 28050}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Voice setting page返回正确的不支持响应", "uid": "2312df551748d3e9", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405597162, "stop": 1754405610499, "duration": 13337}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable auto pickup返回正确的不支持响应", "uid": "80d3df97a8246d80", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402240805, "stop": 1754402256992, "duration": 16187}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to set screenshots返回正确的不支持响应", "uid": "a1ef12101f81b7d", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403048991, "stop": 1754403064562, "duration": 15571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's your name？能正常执行", "uid": "c537288c0e1503c5", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399264581, "stop": 1754399277656, "duration": 13075}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "33fac653fa84c3ee", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399548504, "stop": 1754399561536, "duration": 13032}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the wheather today?能正常执行", "uid": "f026f66cbf7e42b7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405625110, "stop": 1754405637918, "duration": 12808}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to watch fireworks能正常执行", "uid": "526c5f31263fcce1", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398504513, "stop": 1754398519081, "duration": 14568}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close power saving mode返回正确的不支持响应", "uid": "b82ed0c85c2f0136", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402155865, "stop": 1754402167175, "duration": 11310}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试whatsapp能正常执行", "uid": "77a207bf58510ea3", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "broken", "time": {"start": 1754401843149, "stop": 1754401855503, "duration": 12354}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set date & time返回正确的不支持响应", "uid": "dff37a5ea2104e4f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404418443, "stop": 1754404429480, "duration": 11037}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the address in thie image能正常执行", "uid": "2a584a3fa4306c62", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754403506408, "stop": 1754403520174, "duration": 13766}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "e763eb3d638654b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400437392, "stop": 1754400451546, "duration": 14154}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "b2b16d888f1ac4b0", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403218514, "stop": 1754403233951, "duration": 15437}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play video by youtube", "uid": "5d6d2f27c1282d41", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403890973, "stop": 1754403906319, "duration": 15346}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart panel返回正确的不支持响应", "uid": "c77ed09dfead7039", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405068350, "stop": 1754405084000, "duration": 15650}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "610dd20e7374d3c8", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399424224, "stop": 1754399446307, "duration": 22083}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试could you please search an for me能正常执行", "uid": "483238969f79cb41", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754398046168, "stop": 1754398059774, "duration": 13606}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试resume music能正常执行", "uid": "746d5df8d9bd2b3b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397664595, "stop": 1754397677178, "duration": 12583}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me take a long screenshot能正常执行", "uid": "33cfa9681d0b6933", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399884489, "stop": 1754399901283, "duration": 16794}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check status updates on whatsapp能正常执行", "uid": "1b798cfce09a0b9f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754397988881, "stop": 1754398003372, "duration": 14491}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "7600488ad8fc7982", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402364836, "stop": 1754402378721, "duration": 13885}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set color style返回正确的不支持响应", "uid": "4729842bdc752b23", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404309484, "stop": 1754404326639, "duration": 17155}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play news", "uid": "4b3374b2c47a62f0", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398624023, "stop": 1754398639789, "duration": 15766}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "d7f63fe8b1ba2a35", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401958340, "stop": 1754401969313, "duration": 10973}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试summarize what i'm reading能正常执行", "uid": "2e8dd23e79a70f77", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398937975, "stop": 1754398951009, "duration": 13034}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "3ec13cb330f9849f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401738016, "stop": 1754401750216, "duration": 12200}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试fly to the moon返回正确的不支持响应", "uid": "b355a4d55fe3cc24", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402932044, "stop": 1754402945598, "duration": 13554}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试power saving能正常执行", "uid": "d4ab31adbeaef605", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400180565, "stop": 1754400205336, "duration": 24771}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart hub返回正确的不支持响应", "uid": "baa3c99991f59110", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405042678, "stop": 1754405053505, "duration": 10827}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试whats the weather today能正常执行", "uid": "523730bcb360743f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399319034, "stop": 1754399339460, "duration": 20426}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what time is it now能正常执行", "uid": "ef22d8f0a498be92", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399291821, "stop": 1754399304794, "duration": 12973}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start record能正常执行", "uid": "3903395be9c42062", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400343590, "stop": 1754400360843, "duration": 17253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试reset phone返回正确的不支持响应", "uid": "af1298ad3bbd6a52", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403984773, "stop": 1754404001334, "duration": 16561}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the flashlight能正常执行", "uid": "b731c6d30a5edcab", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401277802, "stop": 1754401291384, "duration": 13582}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open calculator", "uid": "cbafd1ea8808d5df", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754399487911, "stop": 1754399503355, "duration": 15444}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "11fcdf27778f568e", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402608966, "stop": 1754402620044, "duration": 11078}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer能正常执行", "uid": "12c8b31eae81770c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398074697, "stop": 1754398087507, "duration": 12810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my themes返回正确的不支持响应", "uid": "5cca1f6181dc473b", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404729688, "stop": 1754404743445, "duration": 13757}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close performance mode返回正确的不支持响应", "uid": "b189078eca379002", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402129908, "stop": 1754402141119, "duration": 11211}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "19588de64ce7a6b5", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754403344699, "stop": 1754403364901, "duration": 20202}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open flashlight", "uid": "63080b8a9b4f0272", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754400125863, "stop": 1754400139434, "duration": 13571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试What languages do you support能正常执行", "uid": "2bebe751895151ae", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754399141966, "stop": 1754399155267, "duration": 13301}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play political news", "uid": "9cfb216f65c6bc92", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398654100, "stop": 1754398669905, "duration": 15805}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "274816f91f59cf0", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402880051, "stop": 1754402891603, "duration": 11552}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close aivana能正常执行", "uid": "6eb5fa83c7d1d80c", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754396789473, "stop": 1754396821362, "duration": 31889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "979737cc692b98a9", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404891455, "stop": 1754404908244, "duration": 16789}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flex-still mode返回正确的不支持响应", "uid": "ff51c37aaa3a0361", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754404471464, "stop": 1754404482487, "duration": 11023}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable running lock返回正确的不支持响应", "uid": "deb3d4f1094cfcce", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402797120, "stop": 1754402814529, "duration": 17409}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set timezone返回正确的不支持响应", "uid": "9b33d0eb300d4f90", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754405158782, "stop": 1754405175744, "duration": 16962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试say hello能正常执行", "uid": "2fdc22d6076ea746", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754398683994, "stop": 1754398697666, "duration": 13672}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Add the images and text on the screen to the note", "uid": "892a9a82d3fc2a10", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401870176, "stop": 1754401886145, "duration": 15969}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试delete the 8 o'clock alarm", "uid": "a6004a58bef8d00f", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397009121, "stop": 1754397026564, "duration": 17443}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "61d37c7565c0e541", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402695765, "stop": 1754402717667, "duration": 21902}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play jay chou's music", "uid": "ce6590d0c6c6f198", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "passed", "time": {"start": 1754397470669, "stop": 1754397492320, "duration": 21651}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "28728c838647beb0", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402732328, "stop": 1754402756724, "duration": 24396}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to default mode能正常执行", "uid": "2086cb2aaf34cd85", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754400697864, "stop": 1754400712885, "duration": 15021}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试change (female/tone name) voice能正常执行", "uid": "c67bf10cb385dcb7", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401900548, "stop": 1754401911294, "duration": 10746}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试driving mode返回正确的不支持响应", "uid": "1b4e03bbf1fbfe63", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402557388, "stop": 1754402568688, "duration": 11300}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on wifi能正常执行", "uid": "b422b20f38eec2f6", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401369356, "stop": 1754401381263, "duration": 11907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check my to-do list能正常执行", "uid": "7a2adafcaf75a476", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754402037310, "stop": 1754402049769, "duration": 12459}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball能正常执行", "uid": "4ab6aef2b0a9a905", "parentUid": "02fa654a2130990f39bb87bb03211af9", "status": "failed", "time": {"start": 1754401480341, "stop": 1754401493094, "duration": 12753}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "02fa654a2130990f39bb87bb03211af9"}, {"name": "64860-MainThread", "children": [{"name": "测试fly to the moon返回正确的不支持响应", "uid": "ae09c15284e87680", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452904530, "stop": 1754452932582, "duration": 28052}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "2847f36547f4565c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449351011, "stop": 1754449364997, "duration": 13986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "16c844c54a22bc45", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754448494705, "stop": 1754448511861, "duration": 17156}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试cannot login in google email box能正常执行", "uid": "2eba593f4cb7e56a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447692028, "stop": 1754447706712, "duration": 14684}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "6a31892470e25970", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447927479, "stop": 1754447941296, "duration": 13817}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close bluetooth能正常执行", "uid": "acc50f49b95172e6", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449489597, "stop": 1754449503246, "duration": 13649}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an email能正常执行", "uid": "ea035c12f94d2ab2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452946349, "stop": 1754452961574, "duration": 15225}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the weather like in shanghai today能正常执行", "uid": "7e37de99bed9dbf8", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448975280, "stop": 1754448995678, "duration": 20398}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试tell me a joke能正常执行", "uid": "b6962caf581b09dc", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754448852645, "stop": 1754448867433, "duration": 14788}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today?返回正确的不支持响应", "uid": "f8d2ce2b39404252", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448046663, "stop": 1754448067498, "duration": 20835}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on location services能正常执行", "uid": "90e2769c8ce04c27", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451088417, "stop": 1754451103456, "duration": 15039}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to performance mode返回正确的不支持响应", "uid": "87a8c1c2510dfe13", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455268962, "stop": 1754455282666, "duration": 13704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery usage返回正确的不支持响应", "uid": "cc1c6310bce0716c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453231758, "stop": 1754453246777, "duration": 15019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable hide notifications返回正确的不支持响应", "uid": "f9695098892ae325", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452274094, "stop": 1754452274094, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "70071cedc08d13cb", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452133677, "stop": 1754452133677, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "2803afd17204060a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453129165, "stop": 1754453152341, "duration": 23176}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试play love sotry", "uid": "142906c3e59f6be2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754453740436, "stop": 1754453761597, "duration": 21161}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to set screenshots返回正确的不支持响应", "uid": "b5806b794286b92b", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453037409, "stop": 1754453051054, "duration": 13645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试search whatsapp for me能正常执行", "uid": "c35c0f8a3add0d33", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754454086588, "stop": 1754454104322, "duration": 17734}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "199c7c5fd883ed42", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451590134, "stop": 1754451604950, "duration": 14816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close ella能正常执行", "uid": "2d3253e0c7e1606d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446522764, "stop": 1754446555185, "duration": 32421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试continue music能正常执行", "uid": "1a47f528b9b87329", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446642140, "stop": 1754446655684, "duration": 13544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "4d4e365604efed50", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454966147, "stop": 1754454980412, "duration": 14265}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试yandex eats返回正确的不支持响应", "uid": "75aab6cbe80e5a4", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455612062, "stop": 1754455626406, "duration": 14344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试why my charging is so slow能正常执行", "uid": "685a39e7eb19f7df", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449259853, "stop": 1754449273809, "duration": 13956}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phantom v pen返回正确的不支持响应", "uid": "3cd2aeb657255ae2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454802520, "stop": 1754454816577, "duration": 14057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试hi能正常执行", "uid": "2cd8741868006483", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447955355, "stop": 1754447970221, "duration": 14866}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app auto rotate返回正确的不支持响应", "uid": "88ea7dfcbacf2d15", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454179151, "stop": 1754454193174, "duration": 14023}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open whatsapp", "uid": "9fe241edfc82795", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754453906592, "stop": 1754453921460, "duration": 14868}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close whatsapp能正常执行", "uid": "9583fdfb28ae5b4d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447750854, "stop": 1754447765708, "duration": 14854}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable touch optimization返回正确的不支持响应", "uid": "14097408c33e2291", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452414652, "stop": 1754452428842, "duration": 14190}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set parallel windows返回正确的不支持响应", "uid": "f42c4c84962569a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454747179, "stop": 1754454761313, "duration": 14134}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试start running能正常执行", "uid": "56d6727ada4cc121", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754455185080, "stop": 1754455200076, "duration": 14996}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "4d48f2b9ddae972d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452667250, "stop": 1754452690029, "duration": 22779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试appeler maman能正常执行", "uid": "a99c73fcc462928a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447563860, "stop": 1754447577353, "duration": 13493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause fm能正常执行", "uid": "db0ee351a160247a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447070116, "stop": 1754447083848, "duration": 13732}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试make a call能正常执行", "uid": "c8046c28bc1e5fe8", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448296498, "stop": 1754448319012, "duration": 22514}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger能正常执行", "uid": "ff31feb8037e8115", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451619144, "stop": 1754451632488, "duration": 13344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "3fc3f1d2266214de", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453297139, "stop": 1754453311552, "duration": 14413}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "be1c07cf401ff3e1", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451425856, "stop": 1754451444106, "duration": 18250}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试check model information返回正确的不支持响应", "uid": "b6f84103b65be0e9", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451853335, "stop": 1754451853335, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play taylor swift‘s song love story", "uid": "21e4c9e08559b2f2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754453775856, "stop": 1754453797346, "duration": 21490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today in shanghai返回正确的不支持响应", "uid": "886a52d253902aed", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754453003196, "stop": 1754453023848, "duration": 20652}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试global gdp trends能正常执行", "uid": "46ba013c32f0a867", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447867234, "stop": 1754447884006, "duration": 16772}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check front camera information能正常执行", "uid": "7776530c69781978", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449405309, "stop": 1754449428775, "duration": 23466}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play rock music", "uid": "b9b7e92debdfb117", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447272254, "stop": 1754447294725, "duration": 22471}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode能正常执行", "uid": "42ccbac9fc7e7802", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450565314, "stop": 1754450579258, "duration": 13944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set Battery Saver setting能正常执行", "uid": "21072eabbe5acafe", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450102193, "stop": 1754450125586, "duration": 23393}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause music能正常执行", "uid": "f2f0addf6616c69", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447097680, "stop": 1754447111256, "duration": 13576}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "9204d83b6b8f7fd2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451033561, "stop": 1754451047165, "duration": 13604}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "c0c7a1cff2f3771e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754448526292, "stop": 1754448541648, "duration": 15356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable unfreeze返回正确的不支持响应", "uid": "930f24c0b46c4205", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452822505, "stop": 1754452836329, "duration": 13824}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the address in thie image能正常执行", "uid": "8fa1609791dfabde", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453483088, "stop": 1754453498792, "duration": 15704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop workout能正常执行", "uid": "1efbda1b523115da", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754448668894, "stop": 1754448697708, "duration": 28814}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen relay返回正确的不支持响应", "uid": "bd18a5f48c6a81ad", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454912121, "stop": 1754454925915, "duration": 13794}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试memory cleanup能正常执行", "uid": "2ce3a0f3ed9c5dd1", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449821447, "stop": 1754449849617, "duration": 28170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to flash notification能正常执行", "uid": "70607ed82c3ddd8b", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754450593240, "stop": 1754450616621, "duration": 23381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open flashlight", "uid": "7b11c4ac97004f21", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449948163, "stop": 1754449964590, "duration": 16427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set cover screen apps返回正确的不支持响应", "uid": "47576954d15fc148", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454357679, "stop": 1754454371310, "duration": 13631}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试driving mode返回正确的不支持响应", "uid": "f5d600558ea29705", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452528064, "stop": 1754452542007, "duration": 13943}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "f674c997733d4a85", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454265047, "stop": 1754454287795, "duration": 22748}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "31459d3eaf00f3fc", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452703528, "stop": 1754452725928, "duration": 22400}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flex-still mode返回正确的不支持响应", "uid": "61bf89f1acbe641e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454468583, "stop": 1754454482618, "duration": 14035}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试open calculator", "uid": "8b898a5d16f54ad9", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449288241, "stop": 1754449304334, "duration": 16093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play jay chou's music", "uid": "46a3ccf5a87f0c13", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447167871, "stop": 1754447191593, "duration": 23722}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "2c63a150dba92152", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447033146, "stop": 1754447056127, "duration": 22981}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "9aee619060e4ff8", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451061346, "stop": 1754451074571, "duration": 13225}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me write an thanks email能正常执行", "uid": "eb4a9b4b0831bbc", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452974940, "stop": 1754452989831, "duration": 14891}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试open bluetooth", "uid": "96f699579976c495", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449892699, "stop": 1754449906211, "duration": 13512}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart hub返回正确的不支持响应", "uid": "8b366b5074a3353e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455021098, "stop": 1754455034934, "duration": 13836}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试play video by youtube", "uid": "f5d2d3a4bb66a858", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453876674, "stop": 1754453892534, "duration": 15860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令", "uid": "650adc0ae13a6b5f", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446879576, "stop": 1754446902465, "duration": 22889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试change (female/tone name) voice能正常执行", "uid": "6513ea7d676f9aee", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451748972, "stop": 1754451748972, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Voice setting page返回正确的不支持响应", "uid": "7f98b929b3063854", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754455553984, "stop": 1754455569893, "duration": 15909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close performance mode返回正确的不支持响应", "uid": "e27150fcafd03433", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452028631, "stop": 1754452028631, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set gesture navigation返回正确的不支持响应", "uid": "a6c15fc9b5578641", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454607356, "stop": 1754454621555, "duration": 14199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app notifications返回正确的不支持响应", "uid": "ab38d772841f6a8c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454206931, "stop": 1754454221093, "duration": 14162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Hyper Charge能正常执行", "uid": "cddd64f0dce015e5", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754450630749, "stop": 1754450644126, "duration": 13377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试What languages do you support能正常执行", "uid": "d9d41f3b82b40b56", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448947539, "stop": 1754448961326, "duration": 13787}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "af6499461f251805", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451240559, "stop": 1754451254442, "duration": 13883}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set customized cover screen返回正确的不支持响应", "uid": "ba368628feba4062", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454385198, "stop": 1754454399279, "duration": 14081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试how is the weather today能正常执行", "uid": "ca70ed4533183a70", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447984022, "stop": 1754448004637, "duration": 20615}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable network enhancement返回正确的不支持响应", "uid": "d3a977ef03b1f2dc", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452357295, "stop": 1754452371303, "duration": 14008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试find a restaurant near me能正常执行", "uid": "33c5442f97273add", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451386783, "stop": 1754451411771, "duration": 24988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play the album", "uid": "1787807a0d09bf93", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754453811752, "stop": 1754453833090, "duration": 21338}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn down ring volume能正常执行", "uid": "81616d50f4291bda", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450891563, "stop": 1754450905225, "duration": 13662}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "c31cf391c54c7cbe", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453064336, "stop": 1754453087849, "duration": 23513}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试can you give me a coin能正常执行", "uid": "d8d80c1a8a7aa2ba", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447660529, "stop": 1754447677746, "duration": 17217}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set color style返回正确的不支持响应", "uid": "b79216f6e34836ba", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454301881, "stop": 1754454316351, "duration": 14470}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试next channel能正常执行", "uid": "7a434d777ce8aa77", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446786785, "stop": 1754446800286, "duration": 13501}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set floating windows返回正确的不支持响应", "uid": "24fd0ea8e66dd24f", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454523765, "stop": 1754454537535, "duration": 13770}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me take a long screenshot能正常执行", "uid": "7595375f57ce60c8", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449699015, "stop": 1754449717676, "duration": 18661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "99fd43b3cb1bc7f8", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454441043, "stop": 1754454454848, "duration": 13805}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试Add the images and text on the screen to the note", "uid": "d691924f9e325c36", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451733535, "stop": 1754451747369, "duration": 13834}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "4fa73543ebc54fd5", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453165713, "stop": 1754453188918, "duration": 23205}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a selfie能正常执行", "uid": "7de2c6f37b638397", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450816757, "stop": 1754450847988, "duration": 31231}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close flashlight能正常执行", "uid": "93ef6d13c079fd1", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449517677, "stop": 1754449532009, "duration": 14332}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试my phone is too slow能正常执行", "uid": "c08f5d98556cacbd", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446758280, "stop": 1754446772851, "duration": 14571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试help me take a screenshot能正常执行", "uid": "3814b093ab5b35ad", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449732216, "stop": 1754449748925, "duration": 16709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open folax能正常执行", "uid": "2333c514af23b85d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447006883, "stop": 1754447019377, "duration": 12494}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试book a flight to paris返回正确的不支持响应", "uid": "cc30defe424c2046", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "broken", "time": {"start": 1754447591529, "stop": 1754447607164, "duration": 15635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "8670584871649951", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453101441, "stop": 1754453115702, "duration": 14261}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to default mode能正常执行", "uid": "d95aec3991ba9c49", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754450528080, "stop": 1754450550941, "duration": 22861}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "f1b11a5e7a615977", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449666049, "stop": 1754449684411, "duration": 18362}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start screen recording能正常执行", "uid": "51910e205a82d03e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450232215, "stop": 1754450250336, "duration": 18121}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "d97ce04890c6efba", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455241895, "stop": 1754455255566, "duration": 13671}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "bb94c3185f87279a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450472924, "stop": 1754450486952, "duration": 14028}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "103eff9072b05d94", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452471155, "stop": 1754452485017, "duration": 13862}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "9c90db8fe9592caf", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754450658321, "stop": 1754450671918, "duration": 13597}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on wifi能正常执行", "uid": "bc9a3f7b0d5c458d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451212018, "stop": 1754451226679, "duration": 14661}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试check status updates on whatsapp能正常执行", "uid": "548281780cb22e82", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754447721944, "stop": 1754447736646, "duration": 14702}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试record audio for 5 seconds能正常执行", "uid": "b21a04cbb6917b66", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754447344785, "stop": 1754447359784, "duration": 14999}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my themes返回正确的不支持响应", "uid": "614c61afe62ed631", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454718943, "stop": 1754454733021, "duration": 14078}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "537462e9458a1a8a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754454118397, "stop": 1754454134176, "duration": 15779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to say hello in french能正常执行", "uid": "97384b55cfa58ab3", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448117033, "stop": 1754448129631, "duration": 12598}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "f9f860dc66e4a117", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450445439, "stop": 1754450459010, "duration": 13571}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试play video", "uid": "dadc4cc89b23040e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453847145, "stop": 1754453862367, "duration": 15222}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set date & time返回正确的不支持响应", "uid": "bee1c22929dac17d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454413139, "stop": 1754454427224, "duration": 14085}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "e068de8b2837d67", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450325189, "stop": 1754450343632, "duration": 18443}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable auto pickup返回正确的不支持响应", "uid": "c58e43308502a4be", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452612143, "stop": 1754452626314, "duration": 14171}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试change your language to chinese能正常执行", "uid": "e655393d16373534", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449379042, "stop": 1754449391214, "duration": 12172}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the flashlight能正常执行", "uid": "f6dd481761067622", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451117198, "stop": 1754451133008, "duration": 15810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试open camera能正常执行", "uid": "c80bc96ce82f076e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446813335, "stop": 1754446831354, "duration": 18019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试clear junk files命令", "uid": "2b68b27e39c0eb31", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449442920, "stop": 1754449475136, "duration": 32216}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试maximum volume能正常执行", "uid": "8b9d6241d0992877", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449793578, "stop": 1754449806906, "duration": 13328}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close phonemaster能正常执行", "uid": "d9283c555a0dfffd", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446615376, "stop": 1754446629166, "duration": 13790}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试who is j k rowling能正常执行", "uid": "745e2ba6171983d5", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449193146, "stop": 1754449209426, "duration": 16280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's your name？能正常执行", "uid": "bb166634eb4139ad", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449072637, "stop": 1754449086313, "duration": 13676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger返回正确的不支持响应", "uid": "fe333cd2d054813e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754453627864, "stop": 1754453641483, "duration": 13619}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试the second返回正确的不支持响应", "uid": "a3c1f99a68a83c03", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455351920, "stop": 1754455366101, "duration": 14181}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "dd08e027787e7fe6", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455489902, "stop": 1754455504894, "duration": 14992}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试searching for a method of violent murder能正常执行", "uid": "1cbe1f329bda06d2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "broken", "time": {"start": 1754448465615, "stop": 1754448480470, "duration": 14855}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "b89d9ddd74405bb8", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453425579, "stop": 1754453439364, "duration": 13785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off flashlight能正常执行", "uid": "3f7579b798a46d85", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450919306, "stop": 1754450935420, "duration": 16114}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试extend the image能正常执行", "uid": "f022af5522bf7597", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452877312, "stop": 1754452890938, "duration": 13626}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play afro strut", "uid": "8db2598fd16f9037", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447125376, "stop": 1754447154145, "duration": 28769}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set timezone返回正确的不支持响应", "uid": "613f079c90fa0578", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455130094, "stop": 1754455144079, "duration": 13985}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "78e2205d4e4f206c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450199489, "stop": 1754450218292, "duration": 18803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "2e9c0e779971b6f2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452098668, "stop": 1754452098668, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check my to-do list能正常执行", "uid": "cb9bf1bed63324f0", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451923380, "stop": 1754451923380, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "19b5d1c854db43c5", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449223326, "stop": 1754449245649, "duration": 22323}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable running lock返回正确的不支持响应", "uid": "7a41b675a24c217c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452385420, "stop": 1754452400847, "duration": 15427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试open font family settings返回正确的不支持响应", "uid": "e4feab036331402e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453540495, "stop": 1754453556552, "duration": 16057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off wifi能正常执行", "uid": "cd943b7f9abecf97", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450949206, "stop": 1754450963531, "duration": 14325}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today in shanghai能正常执行", "uid": "74841191efeb3379", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448081733, "stop": 1754448102779, "duration": 21046}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试where is the carlcare service outlet能正常执行", "uid": "d2547e36da3e5bf7", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451268361, "stop": 1754451284343, "duration": 15982}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试vedio call number by whatsapp能正常执行", "uid": "d8b9051cd846e87c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754455518029, "stop": 1754455540486, "duration": 22457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "open clock", "uid": "1f050226c3e963c2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446845200, "stop": 1754446865875, "duration": 20675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch charging modes能正常执行", "uid": "afe93fd71bbebe1b", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754450417620, "stop": 1754450431165, "duration": 13545}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试resume music能正常执行", "uid": "d5a5e14540c4d424", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447374047, "stop": 1754447387656, "duration": 13609}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable running lock返回正确的不支持响应", "uid": "2ed5255e29720bb5", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452766436, "stop": 1754452781916, "duration": 15480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试close wifi能正常执行", "uid": "fbeb0a397c34b803", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449546506, "stop": 1754449559659, "duration": 13153}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download app能正常执行", "uid": "eb1da234ac691e7c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451298290, "stop": 1754451312878, "duration": 14588}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "f46e11db85416dc1", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450357435, "stop": 1754450375374, "duration": 17939}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "68918f24304aea29", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452555984, "stop": 1754452570723, "duration": 14739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on driving mode返回正确的不支持响应", "uid": "d930c37cd1055316", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455434254, "stop": 1754455448097, "duration": 13843}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试how is the wheather today能正常执行", "uid": "fedcf4300a6c2948", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448018652, "stop": 1754448032473, "duration": 13821}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "1a255e5e8ebcea4c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453396189, "stop": 1754453412199, "duration": 16010}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway能正常执行", "uid": "51c4a2ce03ff9f3b", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451646749, "stop": 1754451660700, "duration": 13951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the first address in the image能正常执行", "uid": "b7f1fa9085faaa27", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "broken", "time": {"start": 1754453512052, "stop": 1754453526933, "duration": 14881}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the lucky能正常执行", "uid": "8ba01c509e185f21", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451526706, "stop": 1754451543802, "duration": 17096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试parking space能正常执行", "uid": "d4e2af9239179732", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453682401, "stop": 1754453695912, "duration": 13511}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "262d606376ce8700", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450389334, "stop": 1754450403774, "duration": 14440}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set folding screen zone返回正确的不支持响应", "uid": "db4f0bfa32d8b233", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454551661, "stop": 1754454565552, "duration": 13891}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "656ca2d1faac87de", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453202437, "stop": 1754453217767, "duration": 15330}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试open wifi", "uid": "f883a50d936e4a49", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449978649, "stop": 1754449993380, "duration": 14731}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a screenshot能正常执行", "uid": "6443b855f11fc58", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447499429, "stop": 1754447514675, "duration": 15246}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试check battery information返回正确的不支持响应", "uid": "cc4b26a4d2f8fb86", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451783661, "stop": 1754451783661, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phone number返回正确的不支持响应", "uid": "2d1be458cdfbd118", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454829931, "stop": 1754454843704, "duration": 13773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "de97c82562aa7b51", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452308838, "stop": 1754452308838, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set special function返回正确的不支持响应", "uid": "247171d96cf78942", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455075910, "stop": 1754455089646, "duration": 13736}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试what time is it now能正常执行", "uid": "53b8bc8221a23244", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449100344, "stop": 1754449114223, "duration": 13879}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a note on how to build a treehouse能正常执行", "uid": "765be39b94c2f21d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448797093, "stop": 1754448810624, "duration": 13531}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试delete the 8 o'clock alarm", "uid": "d8177e2366c3da89", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446698704, "stop": 1754446716817, "duration": 18113}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my fonts返回正确的不支持响应", "uid": "c45788b86897c14e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454692062, "stop": 1754454705824, "duration": 13762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试restart my phone能正常执行", "uid": "19c9abbc1af7f6c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454000086, "stop": 1754454014302, "duration": 14216}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试summarize what i'm reading能正常执行", "uid": "b3f6ff1329da975e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448740309, "stop": 1754448753925, "duration": 13616}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the wheather today?能正常执行", "uid": "c09dbe14527ecc7c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449044387, "stop": 1754449058301, "duration": 13914}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "aff9de2e497af703", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451888447, "stop": 1754451888447, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable brightness locking返回正确的不支持响应", "uid": "36e36fc2e197126e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452203747, "stop": 1754452203747, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode能正常执行", "uid": "28a1196086132c45", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450686274, "stop": 1754450701168, "duration": 14894}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试say hello能正常执行", "uid": "d0f25b9e4d6dc09d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448436849, "stop": 1754448451674, "duration": 14825}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable unfreeze返回正确的不支持响应", "uid": "8f7de97d7fdf87b6", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452442992, "stop": 1754452457124, "duration": 14132}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试play music", "uid": "8a51f504d5b44159", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447235725, "stop": 1754447258400, "duration": 22675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play jay chou's music by spotify", "uid": "538bea4536324700", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754447205460, "stop": 1754447221759, "duration": 16299}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open camera", "uid": "9bc8f1759283437a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449318689, "stop": 1754449336910, "duration": 18221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open countdown能正常执行", "uid": "c2dada2a8cd202f3", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754446916540, "stop": 1754446930020, "duration": 13480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable call rejection返回正确的不支持响应", "uid": "fc814aaf55d3b79b", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452238952, "stop": 1754452238952, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "36b8c2f8e2765835", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450264491, "stop": 1754450280445, "duration": 15954}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试switching charging speed能正常执行", "uid": "8681f0bb149c4d9d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455325004, "stop": 1754455338649, "duration": 13645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试open facebook能正常执行", "uid": "253154c273af4b74", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451557799, "stop": 1754451575932, "duration": 18133}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "42624ab66ae39dc7", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451993567, "stop": 1754451993567, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate to shanghai disneyland能正常执行", "uid": "df371b5ba678804", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451492189, "stop": 1754451512550, "duration": 20361}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "cab4b6ad694cf9cc", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448332869, "stop": 1754448346643, "duration": 13774}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what's the wheather today?能正常执行", "uid": "6482ce3d1e710e63", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455583977, "stop": 1754455598021, "duration": 14044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on bluetooth能正常执行", "uid": "1e11879944dfe296", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450977331, "stop": 1754450990939, "duration": 13608}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "d7da5dcb44eb19de", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452585080, "stop": 1754452598459, "duration": 13379}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway返回正确的不支持响应", "uid": "2f730eec0c9ba595", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754453655065, "stop": 1754453668838, "duration": 13773}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen timeout返回正确的不支持响应", "uid": "aa6637247543187a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454939113, "stop": 1754454952936, "duration": 13823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试close folax能正常执行", "uid": "9fdf4fc0d91493b8", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446569173, "stop": 1754446601540, "duration": 32367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open whatsapp", "uid": "a427bf5606042741", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754453599545, "stop": 1754453614460, "duration": 14915}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on do not disturb mode能正常执行", "uid": "780ea6e88536084c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451004913, "stop": 1754451019638, "duration": 14725}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to call notifications返回正确的不支持响应", "uid": "1e2f8e6f0326fd85", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453260837, "stop": 1754453283520, "duration": 22683}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "fe60a039ac946371", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451179149, "stop": 1754451198065, "duration": 18916}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "bd7c1e709ec319a6", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455462027, "stop": 1754455476062, "duration": 14035}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "2c78a1a091cf6fe5", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450861763, "stop": 1754450877680, "duration": 15917}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试play sun be song of jide chord", "uid": "4722e4f5730d45cb", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447308540, "stop": 1754447331031, "duration": 22491}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close power saving mode返回正确的不支持响应", "uid": "c298b828e0b31f92", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452063624, "stop": 1754452063624, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "27ef3ceaf2e10ea4", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455296573, "stop": 1754455311690, "duration": 15117}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试call mom through whatsapp能正常执行", "uid": "f7063b5f8370255c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754447622142, "stop": 1754447645193, "duration": 23051}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flip case feature返回正确的不支持响应", "uid": "ee7c18c0f957d724", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454496546, "stop": 1754454509946, "duration": 13400}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "93130e85f011449", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447528618, "stop": 1754447549421, "duration": 20803}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to say i love you in french能正常执行", "uid": "50ac8cebb18698b6", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448143739, "stop": 1754448155894, "duration": 12155}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set an alarm at 8 am", "uid": "62928b601638239b", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447401605, "stop": 1754447416889, "duration": 15284}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable touch optimization返回正确的不支持响应", "uid": "bef9248c1ac6d9e8", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452795257, "stop": 1754452809330, "duration": 14073}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试hello hello能正常执行", "uid": "2d413365c9e8c32f", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447898223, "stop": 1754447913407, "duration": 15184}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试what is apec?能正常执行", "uid": "e5968f58913b35a0", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448917614, "stop": 1754448933320, "duration": 15706}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close aivana能正常执行", "uid": "465733cfe6bd0a7f", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446474549, "stop": 1754446508948, "duration": 34399}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试smart charge能正常执行", "uid": "409d5e47442fd23e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754450139671, "stop": 1754450152968, "duration": 13297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试show scores between livepool and manchester city能正常执行", "uid": "8ee2f0917f4d02b6", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448555853, "stop": 1754448583869, "duration": 28016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "38d6821ea604d751", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455406018, "stop": 1754455420840, "duration": 14822}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to make a call能正常执行", "uid": "9f342cefac9039d4", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448200490, "stop": 1754448222977, "duration": 22487}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open contact命令 - 简洁版本", "uid": "c305a7aff70443", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446980560, "stop": 1754446993024, "duration": 12464}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable brightness locking返回正确的不支持响应", "uid": "c61bdf99a64855b6", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452639757, "stop": 1754452653831, "duration": 14074}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball返回正确的不支持响应", "uid": "ee418c4c9a2793b3", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452498881, "stop": 1754452514424, "duration": 15543}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a photo能正常执行", "uid": "c8517a0d9fe0ff9f", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450772328, "stop": 1754450802490, "duration": 30162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试play news", "uid": "e14cda5e76258dcf", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448360692, "stop": 1754448378327, "duration": 17635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试could you please search an for me能正常执行", "uid": "4a731551f351aafd", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754447779841, "stop": 1754447794688, "duration": 14847}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set compatibility mode返回正确的不支持响应", "uid": "494274bea1d2dec1", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454330251, "stop": 1754454343621, "duration": 13370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试i wanna be rich能正常执行", "uid": "63a2a142ebf8c9ee", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448170003, "stop": 1754448186489, "duration": 16486}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set font size返回正确的不支持响应", "uid": "575d4153a349b439", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454579451, "stop": 1754454593442, "duration": 13991}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "cbf0dcc5d9990cc2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754446668758, "stop": 1754446684884, "duration": 16126}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试summarize content on this page能正常执行", "uid": "73152c33f6262dbc", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448712091, "stop": 1754448726036, "duration": 13945}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start run能正常执行", "uid": "32a5aa19059db7ec", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754447430794, "stop": 1754447456077, "duration": 25283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "fee8f542d5d430f2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453325062, "stop": 1754453348609, "duration": 23547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试whatsapp能正常执行", "uid": "aaf2e2359901cf45", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "broken", "time": {"start": 1754451704338, "stop": 1754451719424, "duration": 15086}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试more settings返回正确的不支持响应", "uid": "4a822568f803c00a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453452902, "stop": 1754453469776, "duration": 16874}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "11613cef42c356cf", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450294402, "stop": 1754450310643, "duration": 16241}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigate from to red square能正常执行", "uid": "6480df8a6c46a6db", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451457990, "stop": 1754451478299, "duration": 20309}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试play football video by youtube", "uid": "9f2b049c8f60a56c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453709213, "stop": 1754453726412, "duration": 17199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试take notes on how to build a treehouse能正常执行", "uid": "c6a43475c0c2675", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448824871, "stop": 1754448838812, "duration": 13941}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试countdown 5 min能正常执行", "uid": "8712b4ac10ccc2e7", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449574015, "stop": 1754449589731, "duration": 15716}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试who is harry potter能正常执行", "uid": "b030e1b3bc64bf48", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449163121, "stop": 1754449179047, "duration": 15926}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switched to data mode能正常执行", "uid": "4ce79f74a58dc18e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450743050, "stop": 1754450758408, "duration": 15358}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "f6f7df8d1ba4a4a1", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452849649, "stop": 1754452863794, "duration": 14145}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试take a joke能正常执行", "uid": "8f31162e952b18d0", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448767966, "stop": 1754448782951, "duration": 14985}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Switch to Barrage Notification能正常执行", "uid": "987fe19014f79729", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450500684, "stop": 1754450514283, "duration": 13599}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试search the address in the image能正常执行", "uid": "2df96fbe56ee275e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754454057296, "stop": 1754454072495, "duration": 15199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试give me some money能正常执行", "uid": "f137a3f44d2dcec2", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447836770, "stop": 1754447853314, "duration": 16544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart panel返回正确的不支持响应", "uid": "8c0d794b3e011ef9", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455048351, "stop": 1754455062107, "duration": 13756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "f418a21c3f8bcc0d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451818320, "stop": 1754451818320, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试i want to watch fireworks能正常执行", "uid": "c178e4faa8523e73", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448236986, "stop": 1754448253582, "duration": 16596}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试play political news", "uid": "c9ad78195ec09a80", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448392398, "stop": 1754448422888, "duration": 30490}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试introduce yourself能正常执行", "uid": "d7d0df09dd27c956", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448267716, "stop": 1754448282495, "duration": 14779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop run能正常执行", "uid": "3a66753f7b7cbcdf", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754448626044, "stop": 1754448654749, "duration": 28705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer能正常执行", "uid": "80279af86354810e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447808985, "stop": 1754447822724, "duration": 13739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set personal hotspot返回正确的不支持响应", "uid": "c6a06f0569096cad", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454775037, "stop": 1754454789125, "duration": 14088}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试power saving能正常执行", "uid": "45a77846489fea0", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450007334, "stop": 1754450022955, "duration": 15621}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "75b158406577622d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454856985, "stop": 1754454872199, "duration": 15214}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set ultra power saving返回正确的不支持响应", "uid": "fe566ec100eab021", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455156956, "stop": 1754455171944, "duration": 14988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "bbf642526ea09a6", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451146850, "stop": 1754451164847, "duration": 17997}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试long screenshot能正常执行", "uid": "46957ed2e17cc77b", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449763285, "stop": 1754449779835, "duration": 16550}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "80bc5ab5763925a4", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754452739108, "stop": 1754452753065, "duration": 13957}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试display the route go company", "uid": "92ee4d46312fe03", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754446729951, "stop": 1754446745234, "duration": 15283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download qq能正常执行", "uid": "ddc18dfac4b48d27", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451355833, "stop": 1754451372839, "duration": 17006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable auto pickup返回正确的不支持响应", "uid": "740c63b3a902e9b8", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754452168780, "stop": 1754452168780, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on the screen record能正常执行", "uid": "b3bd4eefa66cd793", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449632803, "stop": 1754449651634, "duration": 18831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试video call mom through whatsapp能正常执行", "uid": "334b9b2e3cf14180", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754448881747, "stop": 1754448903362, "duration": 21615}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop music能正常执行", "uid": "4f13cb22f11a7ed0", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754448597918, "stop": 1754448611893, "duration": 13975}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to smart charge能正常执行", "uid": "a1ae04a8c91793c9", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754450715220, "stop": 1754450728748, "duration": 13528}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open dialer能正常执行", "uid": "17b298548e69b1bf", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754446944051, "stop": 1754446966739, "duration": 22688}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set languages返回正确的不支持响应", "uid": "8064999615978c60", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754454635493, "stop": 1754454650849, "duration": 15356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "3b9a61ca339a789e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453570061, "stop": 1754453586358, "duration": 16297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试what·s the weather today？能正常执行", "uid": "44244c2a4bfe7443", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449009816, "stop": 1754449030221, "duration": 20405}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off driving mode返回正确的不支持响应", "uid": "c85c9199aaaea251", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455379237, "stop": 1754455392992, "duration": 13755}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试open bt", "uid": "84f9ea91bdf0b93d", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449920126, "stop": 1754449933865, "duration": 13739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "5e085943d42f8b0c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450069731, "stop": 1754450088254, "duration": 18523}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试whats the weather today能正常执行", "uid": "80bca04c580f2e26", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449128369, "stop": 1754449149343, "duration": 20974}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set battery saver settings返回正确的不支持响应", "uid": "4f99edc2b12b8b45", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454234998, "stop": 1754454251005, "duration": 16007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball能正常执行", "uid": "46a76feb8efb0c84", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451326748, "stop": 1754451341823, "duration": 15075}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "71cd6d041a4b7320", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454885302, "stop": 1754454898804, "duration": 13502}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "b008d3663afb33be", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454993624, "stop": 1754455007813, "duration": 14189}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试decrease the brightness能正常执行", "uid": "a9782300c788ad8c", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754449604272, "stop": 1754449618034, "duration": 13762}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "76652ae732cd656", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754454664856, "stop": 1754454678949, "duration": 14093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试redial", "uid": "ef7a049e326a2e58", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754453935890, "stop": 1754453958417, "duration": 22527}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试start record能正常执行", "uid": "803b6376429b640e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450167254, "stop": 1754450185189, "duration": 17935}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to nfc settings", "uid": "1f1ccbe7a9a3e46f", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453361932, "stop": 1754453382979, "duration": 21047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试reset phone返回正确的不支持响应", "uid": "21927a811f40206b", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754453972515, "stop": 1754453986298, "duration": 13783}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试minimum volume能正常执行", "uid": "743b780e831d526", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754449863875, "stop": 1754449878772, "duration": 14897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "a00dfe1c97e10826", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754454148304, "stop": 1754454164863, "duration": 16559}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check rear camera information能正常执行", "uid": "1c1269d5fcc8834f", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754451958469, "stop": 1754451958469, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试screen record能正常执行", "uid": "1cb174fb389581dd", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754450037016, "stop": 1754450055449, "duration": 18433}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试start walking能正常执行", "uid": "d66688f8f824d308", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754455213655, "stop": 1754455228605, "duration": 14950}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open the newest whatsapp activity", "uid": "ad34c9352740304a", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754451675001, "stop": 1754451690219, "duration": 15218}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试set split-screen apps返回正确的不支持响应", "uid": "634f9fa2a409f31e", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754455103017, "stop": 1754455116832, "duration": 13815}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}, {"name": "测试Search for addresses on the screen能正常执行", "uid": "aec6e1a96ee9d4f5", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "failed", "time": {"start": 1754454028008, "stop": 1754454043073, "duration": 15065}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试stop playing", "uid": "1ddf4917c0fa3e0f", "parentUid": "8a8693fc074beda5e770a3b3f6d380c4", "status": "passed", "time": {"start": 1754447470350, "stop": 1754447485362, "duration": 15012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke"]}], "uid": "8a8693fc074beda5e770a3b3f6d380c4"}], "uid": "5209b93f42624278d4a59a103348bd3d"}]}