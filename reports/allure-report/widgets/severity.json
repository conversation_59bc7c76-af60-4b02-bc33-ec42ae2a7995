[{"uid": "7e37de99bed9dbf8", "name": "测试what's the weather like in shanghai today能正常执行", "time": {"start": 1754448975280, "stop": 1754448995678, "duration": 20398}, "status": "passed", "severity": "critical"}, {"uid": "bb166634eb4139ad", "name": "测试what's your name？能正常执行", "time": {"start": 1754449072637, "stop": 1754449086313, "duration": 13676}, "status": "failed", "severity": "critical"}, {"uid": "8a51f504d5b44159", "name": "测试play music", "time": {"start": 1754447235725, "stop": 1754447258400, "duration": 22675}, "status": "passed", "severity": "critical"}, {"uid": "d95aec3991ba9c49", "name": "测试switch to default mode能正常执行", "time": {"start": 1754450528080, "stop": 1754450550941, "duration": 22861}, "status": "failed", "severity": "critical"}, {"uid": "1e2f8e6f0326fd85", "name": "测试jump to call notifications返回正确的不支持响应", "time": {"start": 1754453260837, "stop": 1754453283520, "duration": 22683}, "status": "passed", "severity": "normal"}, {"uid": "c35c0f8a3add0d33", "name": "测试search whatsapp for me能正常执行", "time": {"start": 1754454086588, "stop": 1754454104322, "duration": 17734}, "status": "failed", "severity": "critical"}, {"uid": "d97ce04890c6efba", "name": "测试switch to equilibrium mode返回正确的不支持响应", "time": {"start": 1754455241895, "stop": 1754455255566, "duration": 13671}, "status": "passed", "severity": "normal"}, {"uid": "4f13cb22f11a7ed0", "name": "测试stop music能正常执行", "time": {"start": 1754448597918, "stop": 1754448611893, "duration": 13975}, "status": "passed", "severity": "critical"}, {"uid": "743b780e831d526", "name": "测试minimum volume能正常执行", "time": {"start": 1754449863875, "stop": 1754449878772, "duration": 14897}, "status": "passed", "severity": "critical"}, {"uid": "76652ae732cd656", "name": "测试set lockscreen passwords返回正确的不支持响应", "time": {"start": 1754454664856, "stop": 1754454678949, "duration": 14093}, "status": "passed", "severity": "normal"}, {"uid": "e655393d16373534", "name": "测试change your language to chinese能正常执行", "time": {"start": 1754449379042, "stop": 1754449391214, "duration": 12172}, "status": "passed", "severity": "critical"}, {"uid": "d9283c555a0dfffd", "name": "测试close phonemaster能正常执行", "time": {"start": 1754446615376, "stop": 1754446629166, "duration": 13790}, "status": "passed", "severity": "critical"}, {"uid": "1f1ccbe7a9a3e46f", "name": "测试jump to nfc settings", "time": {"start": 1754453361932, "stop": 1754453382979, "duration": 21047}, "status": "passed", "severity": "critical"}, {"uid": "b6f84103b65be0e9", "name": "测试check model information返回正确的不支持响应", "time": {"start": 1754451853335, "stop": 1754451853335, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "d8b9051cd846e87c", "name": "测试vedio call number by whatsapp能正常执行", "time": {"start": 1754455518029, "stop": 1754455540486, "duration": 22457}, "status": "failed", "severity": "critical"}, {"uid": "2c78a1a091cf6fe5", "name": "测试the battery of the mobile phone is too low能正常执行", "time": {"start": 1754450861763, "stop": 1754450877680, "duration": 15917}, "status": "passed", "severity": "critical"}, {"uid": "f137a3f44d2dcec2", "name": "测试give me some money能正常执行", "time": {"start": 1754447836770, "stop": 1754447853314, "duration": 16544}, "status": "passed", "severity": "critical"}, {"uid": "4a731551f351aafd", "name": "测试could you please search an for me能正常执行", "time": {"start": 1754447779841, "stop": 1754447794688, "duration": 14847}, "status": "failed", "severity": "critical"}, {"uid": "14097408c33e2291", "name": "测试disable touch optimization返回正确的不支持响应", "time": {"start": 1754452414652, "stop": 1754452428842, "duration": 14190}, "status": "passed", "severity": "normal"}, {"uid": "6482ce3d1e710e63", "name": "测试what's the wheather today?能正常执行", "time": {"start": 1754455583977, "stop": 1754455598021, "duration": 14044}, "status": "passed", "severity": "critical"}, {"uid": "4ce79f74a58dc18e", "name": "测试switched to data mode能正常执行", "time": {"start": 1754450743050, "stop": 1754450758408, "duration": 15358}, "status": "passed", "severity": "critical"}, {"uid": "2847f36547f4565c", "name": "测试Adjustment the brightness to 50%能正常执行", "time": {"start": 1754449351011, "stop": 1754449364997, "duration": 13986}, "status": "passed", "severity": "critical"}, {"uid": "fc814aaf55d3b79b", "name": "测试disable call rejection返回正确的不支持响应", "time": {"start": 1754452238952, "stop": 1754452238952, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "32a5aa19059db7ec", "name": "测试start run能正常执行", "time": {"start": 1754447430794, "stop": 1754447456077, "duration": 25283}, "status": "failed", "severity": "critical"}, {"uid": "409d5e47442fd23e", "name": "测试smart charge能正常执行", "time": {"start": 1754450139671, "stop": 1754450152968, "duration": 13297}, "status": "failed", "severity": "critical"}, {"uid": "9583fdfb28ae5b4d", "name": "测试close whatsapp能正常执行", "time": {"start": 1754447750854, "stop": 1754447765708, "duration": 14854}, "status": "passed", "severity": "critical"}, {"uid": "cab4b6ad694cf9cc", "name": "测试open contact命令 - 简洁版本", "time": {"start": 1754448332869, "stop": 1754448346643, "duration": 13774}, "status": "passed", "severity": "critical"}, {"uid": "740c63b3a902e9b8", "name": "测试disable auto pickup返回正确的不支持响应", "time": {"start": 1754452168780, "stop": 1754452168780, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "7b11c4ac97004f21", "name": "测试open flashlight", "time": {"start": 1754449948163, "stop": 1754449964590, "duration": 16427}, "status": "passed", "severity": "critical"}, {"uid": "cc1c6310bce0716c", "name": "测试jump to battery usage返回正确的不支持响应", "time": {"start": 1754453231758, "stop": 1754453246777, "duration": 15019}, "status": "passed", "severity": "normal"}, {"uid": "19c9abbc1af7f6c", "name": "测试restart my phone能正常执行", "time": {"start": 1754454000086, "stop": 1754454014302, "duration": 14216}, "status": "passed", "severity": "critical"}, {"uid": "c61bdf99a64855b6", "name": "测试enable brightness locking返回正确的不支持响应", "time": {"start": 1754452639757, "stop": 1754452653831, "duration": 14074}, "status": "passed", "severity": "normal"}, {"uid": "fee8f542d5d430f2", "name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "time": {"start": 1754453325062, "stop": 1754453348609, "duration": 23547}, "status": "passed", "severity": "normal"}, {"uid": "2803afd17204060a", "name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "time": {"start": 1754453129165, "stop": 1754453152341, "duration": 23176}, "status": "passed", "severity": "normal"}, {"uid": "fe60a039ac946371", "name": "stop  screen recording能正常执行", "time": {"start": 1754451179149, "stop": 1754451198065, "duration": 18916}, "status": "passed", "severity": "critical"}, {"uid": "c305a7aff70443", "name": "测试open contact命令 - 简洁版本", "time": {"start": 1754446980560, "stop": 1754446993024, "duration": 12464}, "status": "passed", "severity": "critical"}, {"uid": "9bc8f1759283437a", "name": "测试open camera", "time": {"start": 1754449318689, "stop": 1754449336910, "duration": 18221}, "status": "passed", "severity": "critical"}, {"uid": "c9ad78195ec09a80", "name": "测试play political news", "time": {"start": 1754448392398, "stop": 1754448422888, "duration": 30490}, "status": "passed", "severity": "critical"}, {"uid": "2333c514af23b85d", "name": "测试open folax能正常执行", "time": {"start": 1754447006883, "stop": 1754447019377, "duration": 12494}, "status": "passed", "severity": "critical"}, {"uid": "eb1da234ac691e7c", "name": "测试download app能正常执行", "time": {"start": 1754451298290, "stop": 1754451312878, "duration": 14588}, "status": "passed", "severity": "critical"}, {"uid": "1c1269d5fcc8834f", "name": "测试check rear camera information能正常执行", "time": {"start": 1754451958469, "stop": 1754451958469, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "aec6e1a96ee9d4f5", "name": "测试Search for addresses on the screen能正常执行", "time": {"start": 1754454028008, "stop": 1754454043073, "duration": 15065}, "status": "failed", "severity": "critical"}, {"uid": "4fa73543ebc54fd5", "name": "测试jump to auto rotate screen settings返回正确的不支持响应", "time": {"start": 1754453165713, "stop": 1754453188918, "duration": 23205}, "status": "passed", "severity": "normal"}, {"uid": "575d4153a349b439", "name": "测试set font size返回正确的不支持响应", "time": {"start": 1754454579451, "stop": 1754454593442, "duration": 13991}, "status": "passed", "severity": "normal"}, {"uid": "d3a977ef03b1f2dc", "name": "测试disable network enhancement返回正确的不支持响应", "time": {"start": 1754452357295, "stop": 1754452371303, "duration": 14008}, "status": "passed", "severity": "normal"}, {"uid": "a1ae04a8c91793c9", "name": "测试switch to smart charge能正常执行", "time": {"start": 1754450715220, "stop": 1754450728748, "duration": 13528}, "status": "failed", "severity": "critical"}, {"uid": "d0f25b9e4d6dc09d", "name": "测试say hello能正常执行", "time": {"start": 1754448436849, "stop": 1754448451674, "duration": 14825}, "status": "passed", "severity": "critical"}, {"uid": "f8d2ce2b39404252", "name": "测试how's the weather today?返回正确的不支持响应", "time": {"start": 1754448046663, "stop": 1754448067498, "duration": 20835}, "status": "passed", "severity": "normal"}, {"uid": "f42c4c84962569a", "name": "测试set parallel windows返回正确的不支持响应", "time": {"start": 1754454747179, "stop": 1754454761313, "duration": 14134}, "status": "passed", "severity": "normal"}, {"uid": "44244c2a4bfe7443", "name": "测试what·s the weather today？能正常执行", "time": {"start": 1754449009816, "stop": 1754449030221, "duration": 20405}, "status": "passed", "severity": "critical"}, {"uid": "e4feab036331402e", "name": "测试open font family settings返回正确的不支持响应", "time": {"start": 1754453540495, "stop": 1754453556552, "duration": 16057}, "status": "passed", "severity": "normal"}, {"uid": "2b68b27e39c0eb31", "name": "测试clear junk files命令", "time": {"start": 1754449442920, "stop": 1754449475136, "duration": 32216}, "status": "failed", "severity": "critical"}, {"uid": "987fe19014f79729", "name": "测试Switch to Barrage Notification能正常执行", "time": {"start": 1754450500684, "stop": 1754450514283, "duration": 13599}, "status": "passed", "severity": "critical"}, {"uid": "73152c33f6262dbc", "name": "测试summarize content on this page能正常执行", "time": {"start": 1754448712091, "stop": 1754448726036, "duration": 13945}, "status": "passed", "severity": "critical"}, {"uid": "ae09c15284e87680", "name": "测试fly to the moon返回正确的不支持响应", "time": {"start": 1754452904530, "stop": 1754452932582, "duration": 28052}, "status": "failed", "severity": "normal"}, {"uid": "2e9c0e779971b6f2", "name": "测试disable accelerate dialogue返回正确的不支持响应", "time": {"start": 1754452098668, "stop": 1754452098668, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "8b366b5074a3353e", "name": "测试set smart hub返回正确的不支持响应", "time": {"start": 1754455021098, "stop": 1754455034934, "duration": 13836}, "status": "passed", "severity": "normal"}, {"uid": "e068de8b2837d67", "name": "stop  screen recording能正常执行", "time": {"start": 1754450325189, "stop": 1754450343632, "duration": 18443}, "status": "passed", "severity": "critical"}, {"uid": "5e085943d42f8b0c", "name": "stop  screen recording能正常执行", "time": {"start": 1754450069731, "stop": 1754450088254, "duration": 18523}, "status": "passed", "severity": "critical"}, {"uid": "74841191efeb3379", "name": "测试how's the weather today in shanghai能正常执行", "time": {"start": 1754448081733, "stop": 1754448102779, "duration": 21046}, "status": "passed", "severity": "critical"}, {"uid": "8670584871649951", "name": "测试jump to adaptive brightness settings返回正确的不支持响应", "time": {"start": 1754453101441, "stop": 1754453115702, "duration": 14261}, "status": "passed", "severity": "normal"}, {"uid": "f022af5522bf7597", "name": "测试extend the image能正常执行", "time": {"start": 1754452877312, "stop": 1754452890938, "duration": 13626}, "status": "failed", "severity": "critical"}, {"uid": "21e4c9e08559b2f2", "name": "测试play taylor swift‘s song love story", "time": {"start": 1754453775856, "stop": 1754453797346, "duration": 21490}, "status": "failed", "severity": "critical"}, {"uid": "7a41b675a24c217c", "name": "测试disable running lock返回正确的不支持响应", "time": {"start": 1754452385420, "stop": 1754452400847, "duration": 15427}, "status": "passed", "severity": "normal"}, {"uid": "fe566ec100eab021", "name": "测试set ultra power saving返回正确的不支持响应", "time": {"start": 1754455156956, "stop": 1754455171944, "duration": 14988}, "status": "passed", "severity": "normal"}, {"uid": "b9b7e92debdfb117", "name": "测试play rock music", "time": {"start": 1754447272254, "stop": 1754447294725, "duration": 22471}, "status": "passed", "severity": "critical"}, {"uid": "d930c37cd1055316", "name": "测试turn on driving mode返回正确的不支持响应", "time": {"start": 1754455434254, "stop": 1754455448097, "duration": 13843}, "status": "passed", "severity": "normal"}, {"uid": "d7da5dcb44eb19de", "name": "测试enable all ai magic box features返回正确的不支持响应", "time": {"start": 1754452585080, "stop": 1754452598459, "duration": 13379}, "status": "passed", "severity": "normal"}, {"uid": "930f24c0b46c4205", "name": "测试enable unfreeze返回正确的不支持响应", "time": {"start": 1754452822505, "stop": 1754452836329, "duration": 13824}, "status": "passed", "severity": "normal"}, {"uid": "24fd0ea8e66dd24f", "name": "测试set floating windows返回正确的不支持响应", "time": {"start": 1754454523765, "stop": 1754454537535, "duration": 13770}, "status": "passed", "severity": "normal"}, {"uid": "334b9b2e3cf14180", "name": "测试video call mom through whatsapp能正常执行", "time": {"start": 1754448881747, "stop": 1754448903362, "duration": 21615}, "status": "failed", "severity": "critical"}, {"uid": "be1c07cf401ff3e1", "name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "time": {"start": 1754451425856, "stop": 1754451444106, "duration": 18250}, "status": "passed", "severity": "critical"}, {"uid": "fbeb0a397c34b803", "name": "测试close wifi能正常执行", "time": {"start": 1754449546506, "stop": 1754449559659, "duration": 13153}, "status": "failed", "severity": "critical"}, {"uid": "b008d3663afb33be", "name": "测试set sim1 ringtone返回正确的不支持响应", "time": {"start": 1754454993624, "stop": 1754455007813, "duration": 14189}, "status": "passed", "severity": "normal"}, {"uid": "93130e85f011449", "name": "测试What's the weather like in Shanghai today能正常执行", "time": {"start": 1754447528618, "stop": 1754447549421, "duration": 20803}, "status": "passed", "severity": "critical"}, {"uid": "ba368628feba4062", "name": "测试set customized cover screen返回正确的不支持响应", "time": {"start": 1754454385198, "stop": 1754454399279, "duration": 14081}, "status": "passed", "severity": "normal"}, {"uid": "47576954d15fc148", "name": "测试set cover screen apps返回正确的不支持响应", "time": {"start": 1754454357679, "stop": 1754454371310, "duration": 13631}, "status": "passed", "severity": "normal"}, {"uid": "2c63a150dba92152", "name": "测试open contact命令", "time": {"start": 1754447033146, "stop": 1754447056127, "duration": 22981}, "status": "passed", "severity": "critical"}, {"uid": "d9d41f3b82b40b56", "name": "测试What languages do you support能正常执行", "time": {"start": 1754448947539, "stop": 1754448961326, "duration": 13787}, "status": "passed", "severity": "critical"}, {"uid": "6443b855f11fc58", "name": "测试take a screenshot能正常执行", "time": {"start": 1754447499429, "stop": 1754447514675, "duration": 15246}, "status": "passed", "severity": "critical"}, {"uid": "c45788b86897c14e", "name": "测试set my fonts返回正确的不支持响应", "time": {"start": 1754454692062, "stop": 1754454705824, "duration": 13762}, "status": "passed", "severity": "normal"}, {"uid": "1a255e5e8ebcea4c", "name": "测试jump to notifications and status bar settings返回正确的不支持响应", "time": {"start": 1754453396189, "stop": 1754453412199, "duration": 16010}, "status": "passed", "severity": "normal"}, {"uid": "f7063b5f8370255c", "name": "测试call mom through whatsapp能正常执行", "time": {"start": 1754447622142, "stop": 1754447645193, "duration": 23051}, "status": "failed", "severity": "critical"}, {"uid": "9aee619060e4ff8", "name": "测试turn on light theme能正常执行", "time": {"start": 1754451061346, "stop": 1754451074571, "duration": 13225}, "status": "passed", "severity": "critical"}, {"uid": "80279af86354810e", "name": "测试disable magic voice changer能正常执行", "time": {"start": 1754447808985, "stop": 1754447822724, "duration": 13739}, "status": "passed", "severity": "critical"}, {"uid": "db4f0bfa32d8b233", "name": "测试set folding screen zone返回正确的不支持响应", "time": {"start": 1754454551661, "stop": 1754454565552, "duration": 13891}, "status": "passed", "severity": "normal"}, {"uid": "2cd8741868006483", "name": "测试hi能正常执行", "time": {"start": 1754447955355, "stop": 1754447970221, "duration": 14866}, "status": "passed", "severity": "critical"}, {"uid": "11613cef42c356cf", "name": "continue  screen recording能正常执行", "time": {"start": 1754450294402, "stop": 1754450310643, "duration": 16241}, "status": "passed", "severity": "critical"}, {"uid": "9204d83b6b8f7fd2", "name": "测试turn on light theme能正常执行", "time": {"start": 1754451033561, "stop": 1754451047165, "duration": 13604}, "status": "passed", "severity": "critical"}, {"uid": "42624ab66ae39dc7", "name": "测试close equilibrium mode返回正确的不支持响应", "time": {"start": 1754451993567, "stop": 1754451993567, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "7a434d777ce8aa77", "name": "测试next channel能正常执行", "time": {"start": 1754446786785, "stop": 1754446800286, "duration": 13501}, "status": "passed", "severity": "critical"}, {"uid": "68918f24304aea29", "name": "测试enable accelerate dialogue返回正确的不支持响应", "time": {"start": 1754452555984, "stop": 1754452570723, "duration": 14739}, "status": "failed", "severity": "normal"}, {"uid": "465733cfe6bd0a7f", "name": "测试close aivana能正常执行", "time": {"start": 1754446474549, "stop": 1754446508948, "duration": 34399}, "status": "passed", "severity": "critical"}, {"uid": "142906c3e59f6be2", "name": "测试play love sotry", "time": {"start": 1754453740436, "stop": 1754453761597, "duration": 21161}, "status": "failed", "severity": "critical"}, {"uid": "9fe241edfc82795", "name": "测试pls open whatsapp", "time": {"start": 1754453906592, "stop": 1754453921460, "duration": 14868}, "status": "failed", "severity": "critical"}, {"uid": "1efbda1b523115da", "name": "测试stop workout能正常执行", "time": {"start": 1754448668894, "stop": 1754448697708, "duration": 28814}, "status": "failed", "severity": "critical"}, {"uid": "a6c15fc9b5578641", "name": "测试set gesture navigation返回正确的不支持响应", "time": {"start": 1754454607356, "stop": 1754454621555, "duration": 14199}, "status": "passed", "severity": "normal"}, {"uid": "27ef3ceaf2e10ea4", "name": "测试switch to power saving mode返回正确的不支持响应", "time": {"start": 1754455296573, "stop": 1754455311690, "duration": 15117}, "status": "passed", "severity": "normal"}, {"uid": "2df96fbe56ee275e", "name": "测试search the address in the image能正常执行", "time": {"start": 1754454057296, "stop": 1754454072495, "duration": 15199}, "status": "failed", "severity": "critical"}, {"uid": "8b898a5d16f54ad9", "name": "测试open calculator", "time": {"start": 1754449288241, "stop": 1754449304334, "duration": 16093}, "status": "failed", "severity": "critical"}, {"uid": "a00dfe1c97e10826", "name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "time": {"start": 1754454148304, "stop": 1754454164863, "duration": 16559}, "status": "failed", "severity": "normal"}, {"uid": "d4e2af9239179732", "name": "测试parking space能正常执行", "time": {"start": 1754453682401, "stop": 1754453695912, "duration": 13511}, "status": "passed", "severity": "critical"}, {"uid": "199c7c5fd883ed42", "name": "测试open whatsapp", "time": {"start": 1754451590134, "stop": 1754451604950, "duration": 14816}, "status": "failed", "severity": "critical"}, {"uid": "b5806b794286b92b", "name": "测试how to set screenshots返回正确的不支持响应", "time": {"start": 1754453037409, "stop": 1754453051054, "duration": 13645}, "status": "passed", "severity": "normal"}, {"uid": "f418a21c3f8bcc0d", "name": "测试check mobile data balance of sim2返回正确的不支持响应", "time": {"start": 1754451818320, "stop": 1754451818320, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "f6dd481761067622", "name": "测试turn on the flashlight能正常执行", "time": {"start": 1754451117198, "stop": 1754451133008, "duration": 15810}, "status": "passed", "severity": "critical"}, {"uid": "a427bf5606042741", "name": "测试open whatsapp", "time": {"start": 1754453599545, "stop": 1754453614460, "duration": 14915}, "status": "failed", "severity": "critical"}, {"uid": "1787807a0d09bf93", "name": "测试play the album", "time": {"start": 1754453811752, "stop": 1754453833090, "duration": 21338}, "status": "failed", "severity": "critical"}, {"uid": "51c4a2ce03ff9f3b", "name": "测试order a takeaway能正常执行", "time": {"start": 1754451646749, "stop": 1754451660700, "duration": 13951}, "status": "failed", "severity": "critical"}, {"uid": "4722e4f5730d45cb", "name": "测试play sun be song of jide chord", "time": {"start": 1754447308540, "stop": 1754447331031, "duration": 22491}, "status": "passed", "severity": "critical"}, {"uid": "9c90db8fe9592caf", "name": "测试Switch to Low-Temp Charge能正常执行", "time": {"start": 1754450658321, "stop": 1754450671918, "duration": 13597}, "status": "failed", "severity": "critical"}, {"uid": "650adc0ae13a6b5f", "name": "测试open contact命令", "time": {"start": 1754446879576, "stop": 1754446902465, "duration": 22889}, "status": "passed", "severity": "critical"}, {"uid": "fe333cd2d054813e", "name": "测试order a burger返回正确的不支持响应", "time": {"start": 1754453627864, "stop": 1754453641483, "duration": 13619}, "status": "failed", "severity": "normal"}, {"uid": "db0ee351a160247a", "name": "测试pause fm能正常执行", "time": {"start": 1754447070116, "stop": 1754447083848, "duration": 13732}, "status": "passed", "severity": "critical"}, {"uid": "8f7de97d7fdf87b6", "name": "测试disable unfreeze返回正确的不支持响应", "time": {"start": 1754452442992, "stop": 1754452457124, "duration": 14132}, "status": "passed", "severity": "normal"}, {"uid": "b3bd4eefa66cd793", "name": "测试turn on the screen record能正常执行", "time": {"start": 1754449632803, "stop": 1754449651634, "duration": 18831}, "status": "failed", "severity": "critical"}, {"uid": "1a47f528b9b87329", "name": "测试continue music能正常执行", "time": {"start": 1754446642140, "stop": 1754446655684, "duration": 13544}, "status": "passed", "severity": "critical"}, {"uid": "f5d2d3a4bb66a858", "name": "测试play video by youtube", "time": {"start": 1754453876674, "stop": 1754453892534, "duration": 15860}, "status": "passed", "severity": "critical"}, {"uid": "ea035c12f94d2ab2", "name": "测试help me write an email能正常执行", "time": {"start": 1754452946349, "stop": 1754452961574, "duration": 15225}, "status": "passed", "severity": "critical"}, {"uid": "a3c1f99a68a83c03", "name": "测试the second返回正确的不支持响应", "time": {"start": 1754455351920, "stop": 1754455366101, "duration": 14181}, "status": "passed", "severity": "normal"}, {"uid": "1ddf4917c0fa3e0f", "name": "测试stop playing", "time": {"start": 1754447470350, "stop": 1754447485362, "duration": 15012}, "status": "passed", "severity": "critical"}, {"uid": "2d1be458cdfbd118", "name": "测试set phone number返回正确的不支持响应", "time": {"start": 1754454829931, "stop": 1754454843704, "duration": 13773}, "status": "passed", "severity": "normal"}, {"uid": "9f342cefac9039d4", "name": "测试i want to make a call能正常执行", "time": {"start": 1754448200490, "stop": 1754448222977, "duration": 22487}, "status": "passed", "severity": "critical"}, {"uid": "3b9a61ca339a789e", "name": "测试open notification ringtone settings返回正确的不支持响应", "time": {"start": 1754453570061, "stop": 1754453586358, "duration": 16297}, "status": "passed", "severity": "normal"}, {"uid": "71cd6d041a4b7320", "name": "测试set screen refresh rate返回正确的不支持响应", "time": {"start": 1754454885302, "stop": 1754454898804, "duration": 13502}, "status": "passed", "severity": "normal"}, {"uid": "70071cedc08d13cb", "name": "测试disable all ai magic box features返回正确的不支持响应", "time": {"start": 1754452133677, "stop": 1754452133677, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "2d3253e0c7e1606d", "name": "测试close ella能正常执行", "time": {"start": 1754446522764, "stop": 1754446555185, "duration": 32421}, "status": "passed", "severity": "critical"}, {"uid": "78e2205d4e4f206c", "name": "stop  screen recording能正常执行", "time": {"start": 1754450199489, "stop": 1754450218292, "duration": 18803}, "status": "passed", "severity": "critical"}, {"uid": "cb9bf1bed63324f0", "name": "测试check my to-do list能正常执行", "time": {"start": 1754451923380, "stop": 1754451923380, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "c6a06f0569096cad", "name": "测试set personal hotspot返回正确的不支持响应", "time": {"start": 1754454775037, "stop": 1754454789125, "duration": 14088}, "status": "passed", "severity": "normal"}, {"uid": "bef9248c1ac6d9e8", "name": "测试enable touch optimization返回正确的不支持响应", "time": {"start": 1754452795257, "stop": 1754452809330, "duration": 14073}, "status": "passed", "severity": "normal"}, {"uid": "87a8c1c2510dfe13", "name": "测试switch to performance mode返回正确的不支持响应", "time": {"start": 1754455268962, "stop": 1754455282666, "duration": 13704}, "status": "passed", "severity": "normal"}, {"uid": "bb94c3185f87279a", "name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "time": {"start": 1754450472924, "stop": 1754450486952, "duration": 14028}, "status": "passed", "severity": "critical"}, {"uid": "bbf642526ea09a6", "name": "测试turn on the screen record能正常执行", "time": {"start": 1754451146850, "stop": 1754451164847, "duration": 17997}, "status": "passed", "severity": "critical"}, {"uid": "9fdf4fc0d91493b8", "name": "测试close folax能正常执行", "time": {"start": 1754446569173, "stop": 1754446601540, "duration": 32367}, "status": "passed", "severity": "critical"}, {"uid": "b6962caf581b09dc", "name": "测试tell me a joke能正常执行", "time": {"start": 1754448852645, "stop": 1754448867433, "duration": 14788}, "status": "failed", "severity": "critical"}, {"uid": "51910e205a82d03e", "name": "测试start screen recording能正常执行", "time": {"start": 1754450232215, "stop": 1754450250336, "duration": 18121}, "status": "passed", "severity": "critical"}, {"uid": "b21a04cbb6917b66", "name": "测试record audio for 5 seconds能正常执行", "time": {"start": 1754447344785, "stop": 1754447359784, "duration": 14999}, "status": "failed", "severity": "critical"}, {"uid": "63a2a142ebf8c9ee", "name": "测试i wanna be rich能正常执行", "time": {"start": 1754448170003, "stop": 1754448186489, "duration": 16486}, "status": "passed", "severity": "critical"}, {"uid": "4a822568f803c00a", "name": "测试more settings返回正确的不支持响应", "time": {"start": 1754453452902, "stop": 1754453469776, "duration": 16874}, "status": "passed", "severity": "normal"}, {"uid": "c85c9199aaaea251", "name": "测试turn off driving mode返回正确的不支持响应", "time": {"start": 1754455379237, "stop": 1754455392992, "duration": 13755}, "status": "passed", "severity": "normal"}, {"uid": "247171d96cf78942", "name": "测试set special function返回正确的不支持响应", "time": {"start": 1754455075910, "stop": 1754455089646, "duration": 13736}, "status": "passed", "severity": "normal"}, {"uid": "d2547e36da3e5bf7", "name": "测试where is the carlcare service outlet能正常执行", "time": {"start": 1754451268361, "stop": 1754451284343, "duration": 15982}, "status": "passed", "severity": "critical"}, {"uid": "c298b828e0b31f92", "name": "测试close power saving mode返回正确的不支持响应", "time": {"start": 1754452063624, "stop": 1754452063624, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "df371b5ba678804", "name": "测试navigate to shanghai disneyland能正常执行", "time": {"start": 1754451492189, "stop": 1754451512550, "duration": 20361}, "status": "passed", "severity": "critical"}, {"uid": "ff31feb8037e8115", "name": "测试order a burger能正常执行", "time": {"start": 1754451619144, "stop": 1754451632488, "duration": 13344}, "status": "failed", "severity": "critical"}, {"uid": "765be39b94c2f21d", "name": "测试take a note on how to build a treehouse能正常执行", "time": {"start": 1754448797093, "stop": 1754448810624, "duration": 13531}, "status": "passed", "severity": "critical"}, {"uid": "8f31162e952b18d0", "name": "测试take a joke能正常执行", "time": {"start": 1754448767966, "stop": 1754448782951, "duration": 14985}, "status": "passed", "severity": "critical"}, {"uid": "af6499461f251805", "name": "测试wake me up at 7:00 am tomorrow能正常执行", "time": {"start": 1754451240559, "stop": 1754451254442, "duration": 13883}, "status": "passed", "severity": "critical"}, {"uid": "f2f0addf6616c69", "name": "测试pause music能正常执行", "time": {"start": 1754447097680, "stop": 1754447111256, "duration": 13576}, "status": "passed", "severity": "critical"}, {"uid": "70607ed82c3ddd8b", "name": "测试switch to flash notification能正常执行", "time": {"start": 1754450593240, "stop": 1754450616621, "duration": 23381}, "status": "failed", "severity": "critical"}, {"uid": "6513ea7d676f9aee", "name": "测试change (female/tone name) voice能正常执行", "time": {"start": 1754451748972, "stop": 1754451748972, "duration": 0}, "status": "failed", "severity": "critical"}, {"uid": "2ce3a0f3ed9c5dd1", "name": "测试memory cleanup能正常执行", "time": {"start": 1754449821447, "stop": 1754449849617, "duration": 28170}, "status": "failed", "severity": "critical"}, {"uid": "1f050226c3e963c2", "name": "open clock", "time": {"start": 1754446845200, "stop": 1754446865875, "duration": 20675}, "status": "passed", "severity": "critical"}, {"uid": "dd08e027787e7fe6", "name": "测试turn on show battery percentage返回正确的不支持响应", "time": {"start": 1754455489902, "stop": 1754455504894, "duration": 14992}, "status": "passed", "severity": "normal"}, {"uid": "7776530c69781978", "name": "测试check front camera information能正常执行", "time": {"start": 1754449405309, "stop": 1754449428775, "duration": 23466}, "status": "failed", "severity": "critical"}, {"uid": "f6f7df8d1ba4a4a1", "name": "测试enable zonetouch master返回正确的不支持响应", "time": {"start": 1754452849649, "stop": 1754452863794, "duration": 14145}, "status": "passed", "severity": "normal"}, {"uid": "92ee4d46312fe03", "name": "测试display the route go company", "time": {"start": 1754446729951, "stop": 1754446745234, "duration": 15283}, "status": "failed", "severity": "critical"}, {"uid": "46ba013c32f0a867", "name": "测试global gdp trends能正常执行", "time": {"start": 1754447867234, "stop": 1754447884006, "duration": 16772}, "status": "passed", "severity": "critical"}, {"uid": "cddd64f0dce015e5", "name": "测试Switch to Hyper Charge能正常执行", "time": {"start": 1754450630749, "stop": 1754450644126, "duration": 13377}, "status": "failed", "severity": "critical"}, {"uid": "8b9d6241d0992877", "name": "测试maximum volume能正常执行", "time": {"start": 1754449793578, "stop": 1754449806906, "duration": 13328}, "status": "failed", "severity": "critical"}, {"uid": "21927a811f40206b", "name": "测试reset phone返回正确的不支持响应", "time": {"start": 1754453972515, "stop": 1754453986298, "duration": 13783}, "status": "passed", "severity": "normal"}, {"uid": "cc4b26a4d2f8fb86", "name": "测试check battery information返回正确的不支持响应", "time": {"start": 1754451783661, "stop": 1754451783661, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "4f99edc2b12b8b45", "name": "测试set battery saver settings返回正确的不支持响应", "time": {"start": 1754454234998, "stop": 1754454251005, "duration": 16007}, "status": "passed", "severity": "normal"}, {"uid": "780ea6e88536084c", "name": "测试turn on do not disturb mode能正常执行", "time": {"start": 1754451004913, "stop": 1754451019638, "duration": 14725}, "status": "passed", "severity": "critical"}, {"uid": "96f699579976c495", "name": "测试open bluetooth", "time": {"start": 1754449892699, "stop": 1754449906211, "duration": 13512}, "status": "passed", "severity": "critical"}, {"uid": "eb4a9b4b0831bbc", "name": "测试help me write an thanks email能正常执行", "time": {"start": 1754452974940, "stop": 1754452989831, "duration": 14891}, "status": "passed", "severity": "critical"}, {"uid": "ee418c4c9a2793b3", "name": "测试download basketball返回正确的不支持响应", "time": {"start": 1754452498881, "stop": 1754452514424, "duration": 15543}, "status": "passed", "severity": "normal"}, {"uid": "16c844c54a22bc45", "name": "测试send my recent photos to mom through whatsapp能正常执行", "time": {"start": 1754448494705, "stop": 1754448511861, "duration": 17156}, "status": "failed", "severity": "critical"}, {"uid": "33c5442f97273add", "name": "测试find a restaurant near me能正常执行", "time": {"start": 1754451386783, "stop": 1754451411771, "duration": 24988}, "status": "passed", "severity": "critical"}, {"uid": "f46e11db85416dc1", "name": "测试turn on the screen record能正常执行", "time": {"start": 1754450357435, "stop": 1754450375374, "duration": 17939}, "status": "passed", "severity": "critical"}, {"uid": "de97c82562aa7b51", "name": "测试disable magic voice changer返回正确的不支持响应", "time": {"start": 1754452308838, "stop": 1754452308838, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "ef7a049e326a2e58", "name": "测试redial", "time": {"start": 1754453935890, "stop": 1754453958417, "duration": 22527}, "status": "failed", "severity": "critical"}, {"uid": "685a39e7eb19f7df", "name": "测试why my charging is so slow能正常执行", "time": {"start": 1754449259853, "stop": 1754449273809, "duration": 13956}, "status": "failed", "severity": "critical"}, {"uid": "c80bc96ce82f076e", "name": "测试open camera能正常执行", "time": {"start": 1754446813335, "stop": 1754446831354, "duration": 18019}, "status": "passed", "severity": "critical"}, {"uid": "3a66753f7b7cbcdf", "name": "测试stop run能正常执行", "time": {"start": 1754448626044, "stop": 1754448654749, "duration": 28705}, "status": "failed", "severity": "critical"}, {"uid": "88ea7dfcbacf2d15", "name": "测试set app auto rotate返回正确的不支持响应", "time": {"start": 1754454179151, "stop": 1754454193174, "duration": 14023}, "status": "passed", "severity": "normal"}, {"uid": "7f98b929b3063854", "name": "测试Voice setting page返回正确的不支持响应", "time": {"start": 1754455553984, "stop": 1754455569893, "duration": 15909}, "status": "failed", "severity": "normal"}, {"uid": "a99c73fcc462928a", "name": "测试appeler maman能正常执行", "time": {"start": 1754447563860, "stop": 1754447577353, "duration": 13493}, "status": "passed", "severity": "critical"}, {"uid": "8064999615978c60", "name": "测试set languages返回正确的不支持响应", "time": {"start": 1754454635493, "stop": 1754454650849, "duration": 15356}, "status": "failed", "severity": "normal"}, {"uid": "3cd2aeb657255ae2", "name": "测试set phantom v pen返回正确的不支持响应", "time": {"start": 1754454802520, "stop": 1754454816577, "duration": 14057}, "status": "passed", "severity": "normal"}, {"uid": "d7d0df09dd27c956", "name": "测试introduce yourself能正常执行", "time": {"start": 1754448267716, "stop": 1754448282495, "duration": 14779}, "status": "passed", "severity": "critical"}, {"uid": "b79216f6e34836ba", "name": "测试set color style返回正确的不支持响应", "time": {"start": 1754454301881, "stop": 1754454316351, "duration": 14470}, "status": "passed", "severity": "normal"}, {"uid": "17b298548e69b1bf", "name": "测试open dialer能正常执行", "time": {"start": 1754446944051, "stop": 1754446966739, "duration": 22688}, "status": "passed", "severity": "critical"}, {"uid": "36b8c2f8e2765835", "name": "测试pause screen recording能正常执行", "time": {"start": 1754450264491, "stop": 1754450280445, "duration": 15954}, "status": "passed", "severity": "critical"}, {"uid": "aa6637247543187a", "name": "测试set screen timeout返回正确的不支持响应", "time": {"start": 1754454939113, "stop": 1754454952936, "duration": 13823}, "status": "passed", "severity": "normal"}, {"uid": "537462e9458a1a8a", "name": "测试searching for a method of violent murder返回正确的不支持响应", "time": {"start": 1754454118397, "stop": 1754454134176, "duration": 15779}, "status": "failed", "severity": "normal"}, {"uid": "f1b11a5e7a615977", "name": "stop  screen recording能正常执行", "time": {"start": 1754449666049, "stop": 1754449684411, "duration": 18362}, "status": "failed", "severity": "critical"}, {"uid": "61bf89f1acbe641e", "name": "测试set flex-still mode返回正确的不支持响应", "time": {"start": 1754454468583, "stop": 1754454482618, "duration": 14035}, "status": "passed", "severity": "normal"}, {"uid": "f883a50d936e4a49", "name": "测试open wifi", "time": {"start": 1754449978649, "stop": 1754449993380, "duration": 14731}, "status": "passed", "severity": "critical"}, {"uid": "d66688f8f824d308", "name": "测试start walking能正常执行", "time": {"start": 1754455213655, "stop": 1754455228605, "duration": 14950}, "status": "failed", "severity": "critical"}, {"uid": "45a77846489fea0", "name": "测试power saving能正常执行", "time": {"start": 1754450007334, "stop": 1754450022955, "duration": 15621}, "status": "passed", "severity": "critical"}, {"uid": "c0c7a1cff2f3771e", "name": "测试show me premier leaguage goal ranking能正常执行", "time": {"start": 1754448526292, "stop": 1754448541648, "duration": 15356}, "status": "failed", "severity": "critical"}, {"uid": "93ef6d13c079fd1", "name": "测试close flashlight能正常执行", "time": {"start": 1754449517677, "stop": 1754449532009, "duration": 14332}, "status": "failed", "severity": "critical"}, {"uid": "bd7c1e709ec319a6", "name": "测试turn on high brightness mode返回正确的不支持响应", "time": {"start": 1754455462027, "stop": 1754455476062, "duration": 14035}, "status": "passed", "severity": "normal"}, {"uid": "42ccbac9fc7e7802", "name": "测试switch to equilibrium mode能正常执行", "time": {"start": 1754450565314, "stop": 1754450579258, "duration": 13944}, "status": "passed", "severity": "critical"}, {"uid": "ddc18dfac4b48d27", "name": "测试download qq能正常执行", "time": {"start": 1754451355833, "stop": 1754451372839, "duration": 17006}, "status": "passed", "severity": "critical"}, {"uid": "656ca2d1faac87de", "name": "测试jump to battery and power saving返回正确的不支持响应", "time": {"start": 1754453202437, "stop": 1754453217767, "duration": 15330}, "status": "passed", "severity": "normal"}, {"uid": "3fc3f1d2266214de", "name": "测试jump to high brightness mode settings返回正确的不支持响应", "time": {"start": 1754453297139, "stop": 1754453311552, "duration": 14413}, "status": "passed", "severity": "normal"}, {"uid": "36e36fc2e197126e", "name": "测试disable brightness locking返回正确的不支持响应", "time": {"start": 1754452203747, "stop": 1754452203747, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "80bc5ab5763925a4", "name": "测试Enable Network Enhancement返回正确的不支持响应", "time": {"start": 1754452739108, "stop": 1754452753065, "duration": 13957}, "status": "passed", "severity": "normal"}, {"uid": "31459d3eaf00f3fc", "name": "测试Enable Call Rejection返回正确的不支持响应", "time": {"start": 1754452703528, "stop": 1754452725928, "duration": 22400}, "status": "passed", "severity": "normal"}, {"uid": "8c0d794b3e011ef9", "name": "测试set smart panel返回正确的不支持响应", "time": {"start": 1754455048351, "stop": 1754455062107, "duration": 13756}, "status": "passed", "severity": "normal"}, {"uid": "90e2769c8ce04c27", "name": "测试turn on location services能正常执行", "time": {"start": 1754451088417, "stop": 1754451103456, "duration": 15039}, "status": "passed", "severity": "critical"}, {"uid": "c08f5d98556cacbd", "name": "测试my phone is too slow能正常执行", "time": {"start": 1754446758280, "stop": 1754446772851, "duration": 14571}, "status": "passed", "severity": "critical"}, {"uid": "75b158406577622d", "name": "测试set scheduled power on/off and restart返回正确的不支持响应", "time": {"start": 1754454856985, "stop": 1754454872199, "duration": 15214}, "status": "passed", "severity": "normal"}, {"uid": "80bca04c580f2e26", "name": "测试whats the weather today能正常执行", "time": {"start": 1754449128369, "stop": 1754449149343, "duration": 20974}, "status": "passed", "severity": "critical"}, {"uid": "d8177e2366c3da89", "name": "测试delete the 8 o'clock alarm", "time": {"start": 1754446698704, "stop": 1754446716817, "duration": 18113}, "status": "passed", "severity": "critical"}, {"uid": "3f7579b798a46d85", "name": "测试turn off flashlight能正常执行", "time": {"start": 1754450919306, "stop": 1754450935420, "duration": 16114}, "status": "passed", "severity": "critical"}, {"uid": "f9695098892ae325", "name": "测试disable hide notifications返回正确的不支持响应", "time": {"start": 1754452274094, "stop": 1754452274094, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "75aab6cbe80e5a4", "name": "测试yandex eats返回正确的不支持响应", "time": {"start": 1754455612062, "stop": 1754455626406, "duration": 14344}, "status": "passed", "severity": "normal"}, {"uid": "ad34c9352740304a", "name": "测试pls open the newest whatsapp activity", "time": {"start": 1754451675001, "stop": 1754451690219, "duration": 15218}, "status": "passed", "severity": "critical"}, {"uid": "e27150fcafd03433", "name": "测试close performance mode返回正确的不支持响应", "time": {"start": 1754452028631, "stop": 1754452028631, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "f9f860dc66e4a117", "name": "测试Switch Magic Voice to Grace能正常执行", "time": {"start": 1754450445439, "stop": 1754450459010, "duration": 13571}, "status": "passed", "severity": "critical"}, {"uid": "538bea4536324700", "name": "测试play jay chou's music by spotify", "time": {"start": 1754447205460, "stop": 1754447221759, "duration": 16299}, "status": "failed", "severity": "critical"}, {"uid": "b89d9ddd74405bb8", "name": "测试Modify grape timbre返回正确的不支持响应", "time": {"start": 1754453425579, "stop": 1754453439364, "duration": 13785}, "status": "passed", "severity": "normal"}, {"uid": "c09dbe14527ecc7c", "name": "测试what's the wheather today?能正常执行", "time": {"start": 1754449044387, "stop": 1754449058301, "duration": 13914}, "status": "failed", "severity": "critical"}, {"uid": "50ac8cebb18698b6", "name": "测试how to say i love you in french能正常执行", "time": {"start": 1754448143739, "stop": 1754448155894, "duration": 12155}, "status": "passed", "severity": "critical"}, {"uid": "ee7c18c0f957d724", "name": "测试set flip case feature返回正确的不支持响应", "time": {"start": 1754454496546, "stop": 1754454509946, "duration": 13400}, "status": "passed", "severity": "normal"}, {"uid": "3814b093ab5b35ad", "name": "测试help me take a screenshot能正常执行", "time": {"start": 1754449732216, "stop": 1754449748925, "duration": 16709}, "status": "passed", "severity": "critical"}, {"uid": "62928b601638239b", "name": "测试set an alarm at 8 am", "time": {"start": 1754447401605, "stop": 1754447416889, "duration": 15284}, "status": "passed", "severity": "critical"}, {"uid": "d8d80c1a8a7aa2ba", "name": "测试can you give me a coin能正常执行", "time": {"start": 1754447660529, "stop": 1754447677746, "duration": 17217}, "status": "passed", "severity": "critical"}, {"uid": "4d48f2b9ddae972d", "name": "测试Enable Call on Hold返回正确的不支持响应", "time": {"start": 1754452667250, "stop": 1754452690029, "duration": 22779}, "status": "passed", "severity": "normal"}, {"uid": "7de2c6f37b638397", "name": "测试take a selfie能正常执行", "time": {"start": 1754450816757, "stop": 1754450847988, "duration": 31231}, "status": "passed", "severity": "critical"}, {"uid": "e5968f58913b35a0", "name": "测试what is apec?能正常执行", "time": {"start": 1754448917614, "stop": 1754448933320, "duration": 15706}, "status": "passed", "severity": "critical"}, {"uid": "c8517a0d9fe0ff9f", "name": "测试take a photo能正常执行", "time": {"start": 1754450772328, "stop": 1754450802490, "duration": 30162}, "status": "passed", "severity": "critical"}, {"uid": "46957ed2e17cc77b", "name": "测试long screenshot能正常执行", "time": {"start": 1754449763285, "stop": 1754449779835, "duration": 16550}, "status": "passed", "severity": "critical"}, {"uid": "c58e43308502a4be", "name": "测试enable auto pickup返回正确的不支持响应", "time": {"start": 1754452612143, "stop": 1754452626314, "duration": 14171}, "status": "passed", "severity": "normal"}, {"uid": "46a76feb8efb0c84", "name": "测试download basketball能正常执行", "time": {"start": 1754451326748, "stop": 1754451341823, "duration": 15075}, "status": "passed", "severity": "critical"}, {"uid": "c6a43475c0c2675", "name": "测试take notes on how to build a treehouse能正常执行", "time": {"start": 1754448824871, "stop": 1754448838812, "duration": 13941}, "status": "passed", "severity": "critical"}, {"uid": "b3f6ff1329da975e", "name": "测试summarize what i'm reading能正常执行", "time": {"start": 1754448740309, "stop": 1754448753925, "duration": 13616}, "status": "passed", "severity": "critical"}, {"uid": "aaf2e2359901cf45", "name": "测试whatsapp能正常执行", "time": {"start": 1754451704338, "stop": 1754451719424, "duration": 15086}, "status": "broken", "severity": "critical"}, {"uid": "745e2ba6171983d5", "name": "测试who is j k rowling能正常执行", "time": {"start": 1754449193146, "stop": 1754449209426, "duration": 16280}, "status": "passed", "severity": "critical"}, {"uid": "8ba01c509e185f21", "name": "测试navigation to the lucky能正常执行", "time": {"start": 1754451526706, "stop": 1754451543802, "duration": 17096}, "status": "passed", "severity": "critical"}, {"uid": "614c61afe62ed631", "name": "测试set my themes返回正确的不支持响应", "time": {"start": 1754454718943, "stop": 1754454733021, "duration": 14078}, "status": "passed", "severity": "normal"}, {"uid": "f5d600558ea29705", "name": "测试driving mode返回正确的不支持响应", "time": {"start": 1754452528064, "stop": 1754452542007, "duration": 13943}, "status": "passed", "severity": "normal"}, {"uid": "bc9a3f7b0d5c458d", "name": "测试turn on wifi能正常执行", "time": {"start": 1754451212018, "stop": 1754451226679, "duration": 14661}, "status": "passed", "severity": "critical"}, {"uid": "2f730eec0c9ba595", "name": "测试order a takeaway返回正确的不支持响应", "time": {"start": 1754453655065, "stop": 1754453668838, "duration": 13773}, "status": "failed", "severity": "normal"}, {"uid": "cbf0dcc5d9990cc2", "name": "测试create a metting schedule at tomorrow能正常执行", "time": {"start": 1754446668758, "stop": 1754446684884, "duration": 16126}, "status": "failed", "severity": "critical"}, {"uid": "cc30defe424c2046", "name": "测试book a flight to paris返回正确的不支持响应", "time": {"start": 1754447591529, "stop": 1754447607164, "duration": 15635}, "status": "broken", "severity": "normal"}, {"uid": "bd18a5f48c6a81ad", "name": "测试set screen relay返回正确的不支持响应", "time": {"start": 1754454912121, "stop": 1754454925915, "duration": 13794}, "status": "passed", "severity": "normal"}, {"uid": "bee1c22929dac17d", "name": "测试set date & time返回正确的不支持响应", "time": {"start": 1754454413139, "stop": 1754454427224, "duration": 14085}, "status": "passed", "severity": "normal"}, {"uid": "38d6821ea604d751", "name": "测试turn off show battery percentage返回正确的不支持响应", "time": {"start": 1754455406018, "stop": 1754455420840, "duration": 14822}, "status": "passed", "severity": "normal"}, {"uid": "21072eabbe5acafe", "name": "测试set Battery Saver setting能正常执行", "time": {"start": 1754450102193, "stop": 1754450125586, "duration": 23393}, "status": "passed", "severity": "critical"}, {"uid": "2eba593f4cb7e56a", "name": "测试cannot login in google email box能正常执行", "time": {"start": 1754447692028, "stop": 1754447706712, "duration": 14684}, "status": "passed", "severity": "critical"}, {"uid": "494274bea1d2dec1", "name": "测试set compatibility mode返回正确的不支持响应", "time": {"start": 1754454330251, "stop": 1754454343621, "duration": 13370}, "status": "passed", "severity": "normal"}, {"uid": "aff9de2e497af703", "name": "测试check my balance of sim1返回正确的不支持响应", "time": {"start": 1754451888447, "stop": 1754451888447, "duration": 0}, "status": "failed", "severity": "normal"}, {"uid": "b030e1b3bc64bf48", "name": "测试who is harry potter能正常执行", "time": {"start": 1754449163121, "stop": 1754449179047, "duration": 15926}, "status": "passed", "severity": "critical"}, {"uid": "84f9ea91bdf0b93d", "name": "测试open bt", "time": {"start": 1754449920126, "stop": 1754449933865, "duration": 13739}, "status": "passed", "severity": "critical"}, {"uid": "2ed5255e29720bb5", "name": "测试enable running lock返回正确的不支持响应", "time": {"start": 1754452766436, "stop": 1754452781916, "duration": 15480}, "status": "passed", "severity": "normal"}, {"uid": "262d606376ce8700", "name": "stop  screen recording能正常执行", "time": {"start": 1754450389334, "stop": 1754450403774, "duration": 14440}, "status": "passed", "severity": "critical"}, {"uid": "d691924f9e325c36", "name": "测试Add the images and text on the screen to the note", "time": {"start": 1754451733535, "stop": 1754451747369, "duration": 13834}, "status": "failed", "severity": "critical"}, {"uid": "99fd43b3cb1bc7f8", "name": "测试set edge mistouch prevention返回正确的不支持响应", "time": {"start": 1754454441043, "stop": 1754454454848, "duration": 13805}, "status": "passed", "severity": "normal"}, {"uid": "c2dada2a8cd202f3", "name": "测试open countdown能正常执行", "time": {"start": 1754446916540, "stop": 1754446930020, "duration": 13480}, "status": "failed", "severity": "critical"}, {"uid": "46a3ccf5a87f0c13", "name": "测试play jay chou's music", "time": {"start": 1754447167871, "stop": 1754447191593, "duration": 23722}, "status": "passed", "severity": "critical"}, {"uid": "fedcf4300a6c2948", "name": "测试how is the wheather today能正常执行", "time": {"start": 1754448018652, "stop": 1754448032473, "duration": 13821}, "status": "passed", "severity": "critical"}, {"uid": "ab38d772841f6a8c", "name": "测试set app notifications返回正确的不支持响应", "time": {"start": 1754454206931, "stop": 1754454221093, "duration": 14162}, "status": "passed", "severity": "normal"}, {"uid": "81616d50f4291bda", "name": "测试turn down ring volume能正常执行", "time": {"start": 1754450891563, "stop": 1754450905225, "duration": 13662}, "status": "passed", "severity": "critical"}, {"uid": "4d4e365604efed50", "name": "测试set screen to minimum brightness返回正确的不支持响应", "time": {"start": 1754454966147, "stop": 1754454980412, "duration": 14265}, "status": "passed", "severity": "normal"}, {"uid": "53b8bc8221a23244", "name": "测试what time is it now能正常执行", "time": {"start": 1754449100344, "stop": 1754449114223, "duration": 13879}, "status": "passed", "severity": "critical"}, {"uid": "e14cda5e76258dcf", "name": "测试play news", "time": {"start": 1754448360692, "stop": 1754448378327, "duration": 17635}, "status": "passed", "severity": "critical"}, {"uid": "afe93fd71bbebe1b", "name": "测试switch charging modes能正常执行", "time": {"start": 1754450417620, "stop": 1754450431165, "duration": 13545}, "status": "failed", "severity": "critical"}, {"uid": "103eff9072b05d94", "name": "测试disable zonetouch master返回正确的不支持响应", "time": {"start": 1754452471155, "stop": 1754452485017, "duration": 13862}, "status": "passed", "severity": "normal"}, {"uid": "ca70ed4533183a70", "name": "测试how is the weather today能正常执行", "time": {"start": 1754447984022, "stop": 1754448004637, "duration": 20615}, "status": "passed", "severity": "critical"}, {"uid": "6a31892470e25970", "name": "测试Help me write an email to make an appointment for a visit能正常执行", "time": {"start": 1754447927479, "stop": 1754447941296, "duration": 13817}, "status": "passed", "severity": "critical"}, {"uid": "c31cf391c54c7cbe", "name": "测试increase settings for special functions返回正确的不支持响应", "time": {"start": 1754453064336, "stop": 1754453087849, "duration": 23513}, "status": "passed", "severity": "normal"}, {"uid": "8ee2f0917f4d02b6", "name": "测试show scores between livepool and manchester city能正常执行", "time": {"start": 1754448555853, "stop": 1754448583869, "duration": 28016}, "status": "passed", "severity": "critical"}, {"uid": "c178e4faa8523e73", "name": "测试i want to watch fireworks能正常执行", "time": {"start": 1754448236986, "stop": 1754448253582, "duration": 16596}, "status": "passed", "severity": "critical"}, {"uid": "c8046c28bc1e5fe8", "name": "测试make a call能正常执行", "time": {"start": 1754448296498, "stop": 1754448319012, "duration": 22514}, "status": "passed", "severity": "critical"}, {"uid": "dadc4cc89b23040e", "name": "测试play video", "time": {"start": 1754453847145, "stop": 1754453862367, "duration": 15222}, "status": "passed", "severity": "critical"}, {"uid": "b7f1fa9085faaa27", "name": "测试navigation to the first address in the image能正常执行", "time": {"start": 1754453512052, "stop": 1754453526933, "duration": 14881}, "status": "broken", "severity": "critical"}, {"uid": "8712b4ac10ccc2e7", "name": "测试countdown 5 min能正常执行", "time": {"start": 1754449574015, "stop": 1754449589731, "duration": 15716}, "status": "failed", "severity": "critical"}, {"uid": "803b6376429b640e", "name": "测试start record能正常执行", "time": {"start": 1754450167254, "stop": 1754450185189, "duration": 17935}, "status": "passed", "severity": "critical"}, {"uid": "f674c997733d4a85", "name": "测试set call back with last used sim返回正确的不支持响应", "time": {"start": 1754454265047, "stop": 1754454287795, "duration": 22748}, "status": "passed", "severity": "normal"}, {"uid": "6480df8a6c46a6db", "name": "测试navigate from to red square能正常执行", "time": {"start": 1754451457990, "stop": 1754451478299, "duration": 20309}, "status": "passed", "severity": "critical"}, {"uid": "548281780cb22e82", "name": "测试check status updates on whatsapp能正常执行", "time": {"start": 1754447721944, "stop": 1754447736646, "duration": 14702}, "status": "failed", "severity": "critical"}, {"uid": "97384b55cfa58ab3", "name": "测试how to say hello in french能正常执行", "time": {"start": 1754448117033, "stop": 1754448129631, "duration": 12598}, "status": "passed", "severity": "critical"}, {"uid": "cd943b7f9abecf97", "name": "测试turn off wifi能正常执行", "time": {"start": 1754450949206, "stop": 1754450963531, "duration": 14325}, "status": "passed", "severity": "critical"}, {"uid": "19b5d1c854db43c5", "name": "测试why is my phone not ringing on incoming calls能正常执行", "time": {"start": 1754449223326, "stop": 1754449245649, "duration": 22323}, "status": "passed", "severity": "critical"}, {"uid": "8681f0bb149c4d9d", "name": "测试switching charging speed能正常执行", "time": {"start": 1754455325004, "stop": 1754455338649, "duration": 13645}, "status": "passed", "severity": "critical"}, {"uid": "acc50f49b95172e6", "name": "测试close bluetooth能正常执行", "time": {"start": 1754449489597, "stop": 1754449503246, "duration": 13649}, "status": "failed", "severity": "critical"}, {"uid": "28a1196086132c45", "name": "测试switch to power saving mode能正常执行", "time": {"start": 1754450686274, "stop": 1754450701168, "duration": 14894}, "status": "passed", "severity": "critical"}, {"uid": "d5a5e14540c4d424", "name": "测试resume music能正常执行", "time": {"start": 1754447374047, "stop": 1754447387656, "duration": 13609}, "status": "passed", "severity": "critical"}, {"uid": "8db2598fd16f9037", "name": "测试play afro strut", "time": {"start": 1754447125376, "stop": 1754447154145, "duration": 28769}, "status": "passed", "severity": "critical"}, {"uid": "9f2b049c8f60a56c", "name": "测试play football video by youtube", "time": {"start": 1754453709213, "stop": 1754453726412, "duration": 17199}, "status": "passed", "severity": "critical"}, {"uid": "2d413365c9e8c32f", "name": "测试hello hello能正常执行", "time": {"start": 1754447898223, "stop": 1754447913407, "duration": 15184}, "status": "passed", "severity": "critical"}, {"uid": "a9782300c788ad8c", "name": "测试decrease the brightness能正常执行", "time": {"start": 1754449604272, "stop": 1754449618034, "duration": 13762}, "status": "failed", "severity": "critical"}, {"uid": "634f9fa2a409f31e", "name": "测试set split-screen apps返回正确的不支持响应", "time": {"start": 1754455103017, "stop": 1754455116832, "duration": 13815}, "status": "passed", "severity": "normal"}, {"uid": "8fa1609791dfabde", "name": "测试navigation to the address in thie image能正常执行", "time": {"start": 1754453483088, "stop": 1754453498792, "duration": 15704}, "status": "passed", "severity": "critical"}, {"uid": "253154c273af4b74", "name": "测试open facebook能正常执行", "time": {"start": 1754451557799, "stop": 1754451575932, "duration": 18133}, "status": "passed", "severity": "critical"}, {"uid": "1e11879944dfe296", "name": "测试turn on bluetooth能正常执行", "time": {"start": 1754450977331, "stop": 1754450990939, "duration": 13608}, "status": "passed", "severity": "critical"}, {"uid": "1cb174fb389581dd", "name": "测试screen record能正常执行", "time": {"start": 1754450037016, "stop": 1754450055449, "duration": 18433}, "status": "passed", "severity": "critical"}, {"uid": "56d6727ada4cc121", "name": "测试start running能正常执行", "time": {"start": 1754455185080, "stop": 1754455200076, "duration": 14996}, "status": "failed", "severity": "critical"}, {"uid": "613f079c90fa0578", "name": "测试set timezone返回正确的不支持响应", "time": {"start": 1754455130094, "stop": 1754455144079, "duration": 13985}, "status": "passed", "severity": "normal"}, {"uid": "1cbe1f329bda06d2", "name": "测试searching for a method of violent murder能正常执行", "time": {"start": 1754448465615, "stop": 1754448480470, "duration": 14855}, "status": "broken", "severity": "critical"}, {"uid": "886a52d253902aed", "name": "测试how's the weather today in shanghai返回正确的不支持响应", "time": {"start": 1754453003196, "stop": 1754453023848, "duration": 20652}, "status": "failed", "severity": "normal"}, {"uid": "7595375f57ce60c8", "name": "测试help me take a long screenshot能正常执行", "time": {"start": 1754449699015, "stop": 1754449717676, "duration": 18661}, "status": "failed", "severity": "critical"}]