{"f416bca94fc67372d77ac2dd1f3e4517": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d0f25b9e4d6dc09d", "status": "passed", "time": {"start": 1754448436849, "stop": 1754448451674, "duration": 14825}}]}, "a192659952d6d75342a1c692afadb96d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a3c1f99a68a83c03", "status": "passed", "time": {"start": 1754455351920, "stop": 1754455366101, "duration": 14181}}]}, "7c50481bc992b9ff109abbeeeece073a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f418a21c3f8bcc0d", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B2F30D0>.wait_for_page_load", "time": {"start": 1754451818320, "stop": 1754451818320, "duration": 0}}]}, "8a87a1d0f534afc4770901f3d1dfa316": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "90e2769c8ce04c27", "status": "passed", "time": {"start": 1754451088417, "stop": 1754451103456, "duration": 15039}}]}, "5050d8dc816d181ca0c76dc56c8cb5f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c31cf391c54c7cbe", "status": "passed", "time": {"start": 1754453064336, "stop": 1754453087849, "duration": 23513}}]}, "6ecc7e0fc961d0d4e7e46672c033625a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "63a2a142ebf8c9ee", "status": "passed", "time": {"start": 1754448170003, "stop": 1754448186489, "duration": 16486}}]}, "4276e587385154206726240ad06acd24": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cc1c6310bce0716c", "status": "passed", "time": {"start": 1754453231758, "stop": 1754453246777, "duration": 15019}}]}, "8acd3c85f9c9d0b7f252da4466c049e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "45a77846489fea0", "status": "passed", "time": {"start": 1754450007334, "stop": 1754450022955, "duration": 15621}}]}, "b59687eed5ccb758650c7c0d96ed6bc1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d66688f8f824d308", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754455213655, "stop": 1754455228605, "duration": 14950}}]}, "81532994f5fd70057b844223aa96c49e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "21e4c9e08559b2f2", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"I'm glad to open Youtube Music for you\", 'You need to select music to play.']\nassert False", "time": {"start": 1754453775856, "stop": 1754453797346, "duration": 21490}}]}, "7a670647c2336e6a5a5d07824fe89da6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f6f7df8d1ba4a4a1", "status": "passed", "time": {"start": 1754452849649, "stop": 1754452863794, "duration": 14145}}]}, "8e367b8da758818b9a0fe21deca7ec48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2d1be458cdfbd118", "status": "passed", "time": {"start": 1754454829931, "stop": 1754454843704, "duration": 13773}}]}, "c50847e2010bac3c5a9bb7ff0b690fb6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2df96fbe56ee275e", "status": "failed", "statusDetails": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', \"Dialogue Explore Refresh What is Ask About Screen? De<PERSON><PERSON>'s $228M Spurs Deal Storm Floris: Plane's 145 KM/H Landing search the address in the image I am sorry, I am unable to search for the address in the image. Generated by AI, for reference only Moon mission costs Artemis program timeline SpaceX lunar plans DeepSeek-R1 Feel free to ask me any questions… 12:21\"]'\nassert None", "time": {"start": 1754454057296, "stop": 1754454072495, "duration": 15199}}]}, "70c9bd8c4aab57e96eb06acb93ca2223": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ea035c12f94d2ab2", "status": "passed", "time": {"start": 1754452946349, "stop": 1754452961574, "duration": 15225}}]}, "57c053de6acd628d4b4cd1230b702a40": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e27150fcafd03433", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6BA090>.wait_for_page_load", "time": {"start": 1754452028631, "stop": 1754452028631, "duration": 0}}]}, "1a5cbbb97cbe59e003ae71750a8d910f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "745e2ba6171983d5", "status": "passed", "time": {"start": 1754449193146, "stop": 1754449209426, "duration": 16280}}]}, "de0ed312f350c708e7a00bb74aeaac0f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "62928b601638239b", "status": "passed", "time": {"start": 1754447401605, "stop": 1754447416889, "duration": 15284}}]}, "fc477656b55eea3a3906a5bdcaa93554": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ad34c9352740304a", "status": "passed", "time": {"start": 1754451675001, "stop": 1754451690219, "duration": 15218}}]}, "3eec462c8da39eaf95742ed9ab45b7c7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f9695098892ae325", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B24FBD0>.wait_for_page_load", "time": {"start": 1754452274094, "stop": 1754452274094, "duration": 0}}]}, "d45827de1782723ad9f8cd9d38f067dc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f674c997733d4a85", "status": "passed", "time": {"start": 1754454265047, "stop": 1754454287795, "duration": 22748}}]}, "6748f677dff755a4da95c520c3f05506": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2eba593f4cb7e56a", "status": "passed", "time": {"start": 1754447692028, "stop": 1754447706712, "duration": 14684}}]}, "209a33b821beee1291ce6952c4af7243": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f5d2d3a4bb66a858", "status": "passed", "time": {"start": 1754453876674, "stop": 1754453892534, "duration": 15860}}]}, "9b6faa79e3fe09fed639b1092082745b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e655393d16373534", "status": "passed", "time": {"start": 1754449379042, "stop": 1754449391214, "duration": 12172}}]}, "19cc9ff22947964a912a8f57a87a3c68": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4a731551f351aafd", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Search Information']\nassert False", "time": {"start": 1754447779841, "stop": 1754447794688, "duration": 14847}}]}, "1bf9bd9c91ab7da6f818ff587cfff7da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c9ad78195ec09a80", "status": "passed", "time": {"start": 1754448392398, "stop": 1754448422888, "duration": 30490}}]}, "e76af38ac3a594aa2b7d7173d57e98ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aa6637247543187a", "status": "passed", "time": {"start": 1754454939113, "stop": 1754454952936, "duration": 13823}}]}, "b68db9e7b007fe641926babf537afa6c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a9782300c788ad8c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Brightness goes down']\nassert False", "time": {"start": 1754449604272, "stop": 1754449618034, "duration": 13762}}]}, "7cd08c87d5de8ec73ac863e8a636c8aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "743b780e831d526", "status": "passed", "time": {"start": 1754449863875, "stop": 1754449878772, "duration": 14897}}]}, "f0ba0ad0a7e0160d42d963e5a0186d00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "78e2205d4e4f206c", "status": "passed", "time": {"start": 1754450199489, "stop": 1754450218292, "duration": 18803}}]}, "063471c2e7f2d00ecd08e780860e0cf2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4ce79f74a58dc18e", "status": "passed", "time": {"start": 1754450743050, "stop": 1754450758408, "duration": 15358}}]}, "464c8ea2a15f7ff86b8e0a347a821945": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f2f0addf6616c69", "status": "passed", "time": {"start": 1754447097680, "stop": 1754447111256, "duration": 13576}}]}, "6d49aaf4a5e11b32b961aa0aa3dbbf6e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4a822568f803c00a", "status": "passed", "time": {"start": 1754453452902, "stop": 1754453469776, "duration": 16874}}]}, "5dff8ffa0041df33df80919398086e48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "28a1196086132c45", "status": "passed", "time": {"start": 1754450686274, "stop": 1754450701168, "duration": 14894}}]}, "f4d12b1367b35df96178a58e48fe8f5e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "92ee4d46312fe03", "status": "failed", "statusDetails": "AssertionError: maps: 初始=False, 最终=False, 响应='['display the route go company', 'No company address set up yet.', '', '']'\nassert False", "time": {"start": 1754446729951, "stop": 1754446745234, "duration": 15283}}]}, "dee08db8cb0f1293bf864f56326992d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "46ba013c32f0a867", "status": "passed", "time": {"start": 1754447867234, "stop": 1754447884006, "duration": 16772}}]}, "34f3c9cc9098f792051e7099b7a9fdc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "87a8c1c2510dfe13", "status": "passed", "time": {"start": 1754455268962, "stop": 1754455282666, "duration": 13704}}]}, "578e52c6d5e868d5464682b454971c51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "eb1da234ac691e7c", "status": "passed", "time": {"start": 1754451298290, "stop": 1754451312878, "duration": 14588}}]}, "306cbf11cdbcb045eb3c3c716515b1d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "eb4a9b4b0831bbc", "status": "passed", "time": {"start": 1754452974940, "stop": 1754452989831, "duration": 14891}}]}, "f622c7c4831272dc58cb99e6af8d9943": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "be1c07cf401ff3e1", "status": "passed", "time": {"start": 1754451425856, "stop": 1754451444106, "duration": 18250}}]}, "5fc780d1e7f790011f0e4a521e125a16": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8c0d794b3e011ef9", "status": "passed", "time": {"start": 1754455048351, "stop": 1754455062107, "duration": 13756}}]}, "e2beda2a0bda4155b33d47f14bdcb9ed": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b79216f6e34836ba", "status": "passed", "time": {"start": 1754454301881, "stop": 1754454316351, "duration": 14470}}]}, "c5050ea089fe0f7a5b962119cd32b32e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a99c73fcc462928a", "status": "passed", "time": {"start": 1754447563860, "stop": 1754447577353, "duration": 13493}}]}, "2b244b852ff1236f560ec792596ae556": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fe333cd2d054813e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754453627864, "stop": 1754453641483, "duration": 13619}}]}, "f5346ff0fa4cb76e4b6ceea6116693ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cab4b6ad694cf9cc", "status": "passed", "time": {"start": 1754448332869, "stop": 1754448346643, "duration": 13774}}]}, "148d3ba280bfe2b41b8464beec5f6763": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8a51f504d5b44159", "status": "passed", "time": {"start": 1754447235725, "stop": 1754447258400, "duration": 22675}}]}, "5fe7611f5b7d3ef438ce938b66e0b99f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "51910e205a82d03e", "status": "passed", "time": {"start": 1754450232215, "stop": 1754450250336, "duration": 18121}}]}, "8d12bedb52d3f000f4269afc25f3fe30": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "44244c2a4bfe7443", "status": "passed", "time": {"start": 1754449009816, "stop": 1754449030221, "duration": 20405}}]}, "fe3d09fe0bad56e7804ef2f5ea49d283": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7595375f57ce60c8", "status": "failed", "statusDetails": "AssertionError: 文件不存在！\nassert False", "time": {"start": 1754449699015, "stop": 1754449717676, "duration": 18661}}]}, "2bf170e8c0013ab361afb23f8f059db8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4d48f2b9ddae972d", "status": "passed", "time": {"start": 1754452667250, "stop": 1754452690029, "duration": 22779}}]}, "04cef4934fe29e90ca0248af7b395794": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f9f860dc66e4a117", "status": "passed", "time": {"start": 1754450445439, "stop": 1754450459010, "duration": 13571}}]}, "b3a1d4f5c4ea6e3e176798cf3deda55b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ee418c4c9a2793b3", "status": "passed", "time": {"start": 1754452498881, "stop": 1754452514424, "duration": 15543}}]}, "acca7d0b06a28ad8e6ccfe3c35828ce1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "80bca04c580f2e26", "status": "passed", "time": {"start": 1754449128369, "stop": 1754449149343, "duration": 20974}}]}, "b911308f3c1fe764715d778a884946c2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "75aab6cbe80e5a4", "status": "passed", "time": {"start": 1754455612062, "stop": 1754455626406, "duration": 14344}}]}, "2ecac23eb3f511651fafc6ba6a3725f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2c78a1a091cf6fe5", "status": "passed", "time": {"start": 1754450861763, "stop": 1754450877680, "duration": 15917}}]}, "6bdbaaabff6497c5d3be4727f1a7cd8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6443b855f11fc58", "status": "passed", "time": {"start": 1754447499429, "stop": 1754447514675, "duration": 15246}}]}, "9ba28642fd60826e21a949609570a951": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "537462e9458a1a8a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Oops, out of my reach, ask me again after I've learned it\"]\nassert False", "time": {"start": 1754454118397, "stop": 1754454134176, "duration": 15779}}]}, "d6d97ebce763bf8ead601650bfb2383c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "80279af86354810e", "status": "passed", "time": {"start": 1754447808985, "stop": 1754447822724, "duration": 13739}}]}, "aff947fee562ec2636c3ce68a270b88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1e11879944dfe296", "status": "passed", "time": {"start": 1754450977331, "stop": 1754450990939, "duration": 13608}}]}, "e8c3c7bb72cf538a9e89a7b790c5e689": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8681f0bb149c4d9d", "status": "passed", "time": {"start": 1754455325004, "stop": 1754455338649, "duration": 13645}}]}, "edb3a77ed85c79b290dd8cce24f372c0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b3f6ff1329da975e", "status": "passed", "time": {"start": 1754448740309, "stop": 1754448753925, "duration": 13616}}]}, "969451307307a13b4d89a24bb46ad0bb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "61bf89f1acbe641e", "status": "passed", "time": {"start": 1754454468583, "stop": 1754454482618, "duration": 14035}}]}, "b60d4c80cd1df80873da3fea78736e6a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7e37de99bed9dbf8", "status": "passed", "time": {"start": 1754448975280, "stop": 1754448995678, "duration": 20398}}]}, "6c46a38570672e3c21f37ef82690d639": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7de2c6f37b638397", "status": "passed", "time": {"start": 1754450816757, "stop": 1754450847988, "duration": 31231}}]}, "79b68fd9ac84793c5f55250aad03649a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "987fe19014f79729", "status": "passed", "time": {"start": 1754450500684, "stop": 1754450514283, "duration": 13599}}]}, "ff945a5d436679bddd13261b231955ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "24fd0ea8e66dd24f", "status": "passed", "time": {"start": 1754454523765, "stop": 1754454537535, "duration": 13770}}]}, "ad18e983dce31052b87b7404f3b347ce": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2847f36547f4565c", "status": "passed", "time": {"start": 1754449351011, "stop": 1754449364997, "duration": 13986}}]}, "83e3a1b41834e87017b680efd7c16b92": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8ee2f0917f4d02b6", "status": "passed", "time": {"start": 1754448555853, "stop": 1754448583869, "duration": 28016}}]}, "b07852caec1a6673427b80552f64be85": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f1b11a5e7a615977", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "time": {"start": 1754449666049, "stop": 1754449684411, "duration": 18362}}]}, "891e31ec8bf99ceed4f462ce0c8629db": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "740c63b3a902e9b8", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B690C10>.wait_for_page_load", "time": {"start": 1754452168780, "stop": 1754452168780, "duration": 0}}]}, "084048a337d3081654dc4414f67fce70": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "614c61afe62ed631", "status": "passed", "time": {"start": 1754454718943, "stop": 1754454733021, "duration": 14078}}]}, "039e454ca4c329751543f1bfbb5e008e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "df371b5ba678804", "status": "passed", "time": {"start": 1754451492189, "stop": 1754451512550, "duration": 20361}}]}, "d8a3659601151a79f3b71ba4e47cafee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d930c37cd1055316", "status": "passed", "time": {"start": 1754455434254, "stop": 1754455448097, "duration": 13843}}]}, "f2f6762c5ec83e110ace25b47e3112d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f8d2ce2b39404252", "status": "passed", "time": {"start": 1754448046663, "stop": 1754448067498, "duration": 20835}}]}, "eb142151b4b5ba4125a1a866dc2b58ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f42c4c84962569a", "status": "passed", "time": {"start": 1754454747179, "stop": 1754454761313, "duration": 14134}}]}, "e82a80866bdbe9a7e1ac367f20c977b5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dd08e027787e7fe6", "status": "passed", "time": {"start": 1754455489902, "stop": 1754455504894, "duration": 14992}}]}, "ff8706df57207971727cf6e1326d4a26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3b9a61ca339a789e", "status": "passed", "time": {"start": 1754453570061, "stop": 1754453586358, "duration": 16297}}]}, "95e68fb1d20b8d7ff190c67b0bbc2ee8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1efbda1b523115da", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"You haven't started working out yet\"]\nassert False", "time": {"start": 1754448668894, "stop": 1754448697708, "duration": 28814}}]}, "a19924fb0a564cf26596907610c0f678": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d7d0df09dd27c956", "status": "passed", "time": {"start": 1754448267716, "stop": 1754448282495, "duration": 14779}}]}, "503ff57584874e8387e6b367bfa70c8c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "de97c82562aa7b51", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B31BB10>.wait_for_page_load", "time": {"start": 1754452308838, "stop": 1754452308838, "duration": 0}}]}, "c190fe929896ea57ed1e33f8bc5bf113": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3f7579b798a46d85", "status": "passed", "time": {"start": 1754450919306, "stop": 1754450935420, "duration": 16114}}]}, "cf0ebfd1b4e2ab43e2f516ad6a1a6917": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "19b5d1c854db43c5", "status": "passed", "time": {"start": 1754449223326, "stop": 1754449245649, "duration": 22323}}]}, "89b134ac1374e88187e793daf9f8fcab": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "76652ae732cd656", "status": "passed", "time": {"start": 1754454664856, "stop": 1754454678949, "duration": 14093}}]}, "8b2d3084bb429ea5def5db416bbf10a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9fdf4fc0d91493b8", "status": "passed", "time": {"start": 1754446569173, "stop": 1754446601540, "duration": 32367}}]}, "9da64d3434f91a12d693ed9c71b62e87": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2333c514af23b85d", "status": "passed", "time": {"start": 1754447006883, "stop": 1754447019377, "duration": 12494}}]}, "8c62567d8b8a27f77124afc90fa44336": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d8177e2366c3da89", "status": "passed", "time": {"start": 1754446698704, "stop": 1754446716817, "duration": 18113}}]}, "5cf50058091f34fd1ed89d0b8f717355": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bbf642526ea09a6", "status": "passed", "time": {"start": 1754451146850, "stop": 1754451164847, "duration": 17997}}]}, "f4da532f5d62abff197a05947efc027a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9f342cefac9039d4", "status": "passed", "time": {"start": 1754448200490, "stop": 1754448222977, "duration": 22487}}]}, "de5ff490f92fc399976d91fe0edc371e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "51c4a2ce03ff9f3b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754451646749, "stop": 1754451660700, "duration": 13951}}]}, "733cc57b9e666f7c16017a85f41c410d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "96f699579976c495", "status": "passed", "time": {"start": 1754449892699, "stop": 1754449906211, "duration": 13512}}]}, "f1bf796cd6804ca9b19a3e3f949a04ba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2803afd17204060a", "status": "passed", "time": {"start": 1754453129165, "stop": 1754453152341, "duration": 23176}}]}, "e60dab4e55edacbecf632d4d22f368e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4f99edc2b12b8b45", "status": "passed", "time": {"start": 1754454234998, "stop": 1754454251005, "duration": 16007}}]}, "a34b87ce6db1ff744d0ab6c172eb93da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "19c9abbc1af7f6c", "status": "passed", "time": {"start": 1754454000086, "stop": 1754454014302, "duration": 14216}}]}, "7413abdce214459d0e44671ef65b660b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2b68b27e39c0eb31", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['done']\nassert False", "time": {"start": 1754449442920, "stop": 1754449475136, "duration": 32216}}]}, "71c64c122f83e6fd138db517bbda4aef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fe566ec100eab021", "status": "passed", "time": {"start": 1754455156956, "stop": 1754455171944, "duration": 14988}}]}, "f7282303534c1c8599c3343608e6f453": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fedcf4300a6c2948", "status": "passed", "time": {"start": 1754448018652, "stop": 1754448032473, "duration": 13821}}]}, "bfd4a9e37b70dca0b14b0ccf5246fc4a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b5806b794286b92b", "status": "passed", "time": {"start": 1754453037409, "stop": 1754453051054, "duration": 13645}}]}, "49ae31ee7fe8baa7f1604fd83d56bb68": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b6f84103b65be0e9", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B5EDA90>.wait_for_page_load", "time": {"start": 1754451853335, "stop": 1754451853335, "duration": 0}}]}, "0e5513d569c7270e4332e484218ae36b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "68918f24304aea29", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754452555984, "stop": 1754452570723, "duration": 14739}}]}, "80dab16fde357aadc4387b5d440ed276": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a00dfe1c97e10826", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754454148304, "stop": 1754454164863, "duration": 16559}}]}, "fee3033814a8b17ff8c8abe6bbcdc839": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e14cda5e76258dcf", "status": "passed", "time": {"start": 1754448360692, "stop": 1754448378327, "duration": 17635}}]}, "ea48444c2b0789e59a64850ccfab3722": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cbf0dcc5d9990cc2", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The following event has been added for you.']\nassert False", "time": {"start": 1754446668758, "stop": 1754446684884, "duration": 16126}}]}, "7d3b4e67344145885187c529ee88a9aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c45788b86897c14e", "status": "passed", "time": {"start": 1754454692062, "stop": 1754454705824, "duration": 13762}}]}, "ca9dd7f70b2888aafceb94247d7986f0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3cd2aeb657255ae2", "status": "passed", "time": {"start": 1754454802520, "stop": 1754454816577, "duration": 14057}}]}, "c796c03cca51cea23bdc87f3f9d6fa95": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8670584871649951", "status": "passed", "time": {"start": 1754453101441, "stop": 1754453115702, "duration": 14261}}]}, "7fb2c589f1fda51205a5af1b549d5045": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7776530c69781978", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754449405309, "stop": 1754449428775, "duration": 23466}}]}, "e14cd5a605f26d24de7f5f63d4667c68": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a6c15fc9b5578641", "status": "passed", "time": {"start": 1754454607356, "stop": 1754454621555, "duration": 14199}}]}, "27be46b03402511b8980a7b755c58d58": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d691924f9e325c36", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754451733535, "stop": 1754451747369, "duration": 13834}}]}, "54b47105d42d2a9f18eec071fba40c73": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2d3253e0c7e1606d", "status": "passed", "time": {"start": 1754446522764, "stop": 1754446555185, "duration": 32421}}]}, "56528a816381bba6ed1ca007f557362f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f022af5522bf7597", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754452877312, "stop": 1754452890938, "duration": 13626}}]}, "f05a6eb960fbbc415e4c605538080373": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1cbe1f329bda06d2", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1754448465615, "stop": 1754448480470, "duration": 14855}}]}, "2b2dcc407b5c428f968f62d94fe8025c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "99fd43b3cb1bc7f8", "status": "passed", "time": {"start": 1754454441043, "stop": 1754454454848, "duration": 13805}}]}, "c0f5aa9491c612f6ca4b065dfff62ae7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ae09c15284e87680", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Sorry, I can't help with that\"]\nassert False", "time": {"start": 1754452904530, "stop": 1754452932582, "duration": 28052}}]}, "ae0ee984c3712fd05ea04b52289e14fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c80bc96ce82f076e", "status": "passed", "time": {"start": 1754446813335, "stop": 1754446831354, "duration": 18019}}]}, "7c32e753573a480d7d5c09abab43469e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "650adc0ae13a6b5f", "status": "passed", "time": {"start": 1754446879576, "stop": 1754446902465, "duration": 22889}}]}, "cb21afc40e4aec32b847936756c8ba6e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "538bea4536324700", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Spotify is not installed yet. Please download the app and try again.']\nassert False", "time": {"start": 1754447205460, "stop": 1754447221759, "duration": 16299}}]}, "918c52f1eb9803594ff76c724b43d5f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "33c5442f97273add", "status": "passed", "time": {"start": 1754451386783, "stop": 1754451411771, "duration": 24988}}]}, "cf3df9e3e259f083301aa2ec640729fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c305a7aff70443", "status": "passed", "time": {"start": 1754446980560, "stop": 1754446993024, "duration": 12464}}]}, "cc44ad4097a589726631a345e0cd01ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "97384b55cfa58ab3", "status": "passed", "time": {"start": 1754448117033, "stop": 1754448129631, "duration": 12598}}]}, "dbd7a7f96e1740fa05f50ed6fa7becfb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "613f079c90fa0578", "status": "passed", "time": {"start": 1754455130094, "stop": 1754455144079, "duration": 13985}}]}, "4bda544c08fa4bc5494c7dda01d4cc77": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "38d6821ea604d751", "status": "passed", "time": {"start": 1754455406018, "stop": 1754455420840, "duration": 14822}}]}, "e32881dd9d54414fa74d523ef27b055c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "780ea6e88536084c", "status": "passed", "time": {"start": 1754451004913, "stop": 1754451019638, "duration": 14725}}]}, "488c24d02f5d5348f35881280e505f32": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8fa1609791dfabde", "status": "passed", "time": {"start": 1754453483088, "stop": 1754453498792, "duration": 15704}}]}, "217ee9f7b3be9f625903076716d45106": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d4e2af9239179732", "status": "passed", "time": {"start": 1754453682401, "stop": 1754453695912, "duration": 13511}}]}, "0a0a3640b2ba4adce516043bd9362070": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c08f5d98556cacbd", "status": "passed", "time": {"start": 1754446758280, "stop": 1754446772851, "duration": 14571}}]}, "87f3dc53ab72c729262e053c16a3dbcb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1a47f528b9b87329", "status": "passed", "time": {"start": 1754446642140, "stop": 1754446655684, "duration": 13544}}]}, "540cff5d6d552c22ec37f66efd17315f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1e2f8e6f0326fd85", "status": "passed", "time": {"start": 1754453260837, "stop": 1754453283520, "duration": 22683}}]}, "d4ded95517fa8a5af49f09554cc49725": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d7da5dcb44eb19de", "status": "passed", "time": {"start": 1754452585080, "stop": 1754452598459, "duration": 13379}}]}, "a5a3cc08eb97e600c97acb65a7439ec0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b008d3663afb33be", "status": "passed", "time": {"start": 1754454993624, "stop": 1754455007813, "duration": 14189}}]}, "bd9c64dabd06671b98d60748492be267": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f7063b5f8370255c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Calling Mom...']\nassert False", "time": {"start": 1754447622142, "stop": 1754447645193, "duration": 23051}}]}, "6075008522e5d0ae1667c4ac4be759eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b9b7e92debdfb117", "status": "passed", "time": {"start": 1754447272254, "stop": 1754447294725, "duration": 22471}}]}, "c2a64f07232d43585d1dfee25c2f9407": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c09dbe14527ecc7c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Chong Qing Shi is Fair today. The high is forecast as 37°C and the low as 28°C.']\nassert False", "time": {"start": 1754449044387, "stop": 1754449058301, "duration": 13914}}]}, "3215de286c6ddd59d6e52a44f2a9967d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f5d600558ea29705", "status": "passed", "time": {"start": 1754452528064, "stop": 1754452542007, "duration": 13943}}]}, "8b6d374ba70006ef7591c8e0bf72bb00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d97ce04890c6efba", "status": "passed", "time": {"start": 1754455241895, "stop": 1754455255566, "duration": 13671}}]}, "657acdf17dda1a11abf6946763f6ed52": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "80bc5ab5763925a4", "status": "passed", "time": {"start": 1754452739108, "stop": 1754452753065, "duration": 13957}}]}, "104cf8a7ef102b6850b6d14f4cb14052": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ba368628feba4062", "status": "passed", "time": {"start": 1754454385198, "stop": 1754454399279, "duration": 14081}}]}, "1bc9389e45f0f75c30d3dfb39134948d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "71cd6d041a4b7320", "status": "passed", "time": {"start": 1754454885302, "stop": 1754454898804, "duration": 13502}}]}, "329fa4b06eb0b0d769c2c418ed03dab7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1ddf4917c0fa3e0f", "status": "passed", "time": {"start": 1754447470350, "stop": 1754447485362, "duration": 15012}}]}, "66496441d7401e453a580b4a8c23d111": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7b11c4ac97004f21", "status": "passed", "time": {"start": 1754449948163, "stop": 1754449964590, "duration": 16427}}]}, "92c8c8e017b096314ffde2f610a6791e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "21072eabbe5acafe", "status": "passed", "time": {"start": 1754450102193, "stop": 1754450125586, "duration": 23393}}]}, "169e5b613c0fec2cebd053175998bf17": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1f050226c3e963c2", "status": "passed", "time": {"start": 1754446845200, "stop": 1754446865875, "duration": 20675}}]}, "44b646d68146a0c48da2623a58b17f6f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d5a5e14540c4d424", "status": "passed", "time": {"start": 1754447374047, "stop": 1754447387656, "duration": 13609}}]}, "37d8f85ba7c46a46b390c4fc5ab20de7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c2dada2a8cd202f3", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['5 minutes', '10 minutes', '20 minutes']\nassert False", "time": {"start": 1754446916540, "stop": 1754446930020, "duration": 13480}}]}, "c046a1c6e6cc8effa10641e329b1cfad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "247171d96cf78942", "status": "passed", "time": {"start": 1754455075910, "stop": 1754455089646, "duration": 13736}}]}, "4f538fc772535a0c0811ad87d3aa9494": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c6a06f0569096cad", "status": "passed", "time": {"start": 1754454775037, "stop": 1754454789125, "duration": 14088}}]}, "8814f1dafa698e785ee1f58faa6e745d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "aec6e1a96ee9d4f5", "status": "failed", "statusDetails": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', \"12:20 Dialogue Explore <PERSON><PERSON><PERSON> at UFC 108 What is Ask About Screen? <PERSON> Backs <PERSON>'s Ad, Critiques Swift Search for addresses on the screen I am sorry, I am unable to search for addresses on the screen. Generated by AI, for reference only Artemis program timeline SpaceX lunar missions Moon mission costs analysis DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert None", "time": {"start": 1754454028008, "stop": 1754454043073, "duration": 15065}}]}, "459c099a876d1129ddcb7cb28663b756": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3814b093ab5b35ad", "status": "passed", "time": {"start": 1754449732216, "stop": 1754449748925, "duration": 16709}}]}, "d9f01ef1af79559082ce9e9b2e40295f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "465733cfe6bd0a7f", "status": "passed", "time": {"start": 1754446474549, "stop": 1754446508948, "duration": 34399}}]}, "6f2c4144233271771cdd01a5c48ea3ca": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "46a76feb8efb0c84", "status": "passed", "time": {"start": 1754451326748, "stop": 1754451341823, "duration": 15075}}]}, "984a0fd313bba0ca2f20f0bbff732eb8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c85c9199aaaea251", "status": "passed", "time": {"start": 1754455379237, "stop": 1754455392992, "duration": 13755}}]}, "4f84a6588e41dde581e4eef4fccd6344": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d2547e36da3e5bf7", "status": "passed", "time": {"start": 1754451268361, "stop": 1754451284343, "duration": 15982}}]}, "7acb737855a3a3110ed556a3e5fe1256": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "803b6376429b640e", "status": "passed", "time": {"start": 1754450167254, "stop": 1754450185189, "duration": 17935}}]}, "99709ca7d9951f6f7049b49ea81d0cd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f883a50d936e4a49", "status": "passed", "time": {"start": 1754449978649, "stop": 1754449993380, "duration": 14731}}]}, "a10a49f420542e4994781140da324399": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1787807a0d09bf93", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"I'm glad to open Youtube Music for you\", 'You need to select music to play.']\nassert False", "time": {"start": 1754453811752, "stop": 1754453833090, "duration": 21338}}]}, "0d9f0969c1336e077b7ada5963a2516a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fbeb0a397c34b803", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Wi-<PERSON> is turned off now']\nassert False", "time": {"start": 1754449546506, "stop": 1754449559659, "duration": 13153}}]}, "ecfbca0f5d1122fac0e0543e38291ce2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f46e11db85416dc1", "status": "passed", "time": {"start": 1754450357435, "stop": 1754450375374, "duration": 17939}}]}, "b89775573784e6ef95769309baebeae4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ddc18dfac4b48d27", "status": "passed", "time": {"start": 1754451355833, "stop": 1754451372839, "duration": 17006}}]}, "876e77318cece5d1079b726f0c97bc45": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9583fdfb28ae5b4d", "status": "passed", "time": {"start": 1754447750854, "stop": 1754447765708, "duration": 14854}}]}, "600ddf60808e2a751a4a4742a65811c7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d8b9051cd846e87c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Please tell me the name or number to call.']\nassert False", "time": {"start": 1754455518029, "stop": 1754455540486, "duration": 22457}}]}, "8c5a5747e91f2cb0412111d5027bb7ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f6dd481761067622", "status": "passed", "time": {"start": 1754451117198, "stop": 1754451133008, "duration": 15810}}]}, "b5e1711cce3102fc710ff74e18bf9129": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "634f9fa2a409f31e", "status": "passed", "time": {"start": 1754455103017, "stop": 1754455116832, "duration": 13815}}]}, "3333dd58fd9312a504ae6bc6edf830af": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "685a39e7eb19f7df", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['These suggestions are for your reference']\nassert False", "time": {"start": 1754449259853, "stop": 1754449273809, "duration": 13956}}]}, "e4bab2ec1074fdbc8b4508dfad12adfc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2ce3a0f3ed9c5dd1", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['done']\nassert False", "time": {"start": 1754449821447, "stop": 1754449849617, "duration": 28170}}]}, "bf94eb080274f830f3097dd5adde1ed1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "81616d50f4291bda", "status": "passed", "time": {"start": 1754450891563, "stop": 1754450905225, "duration": 13662}}]}, "09f397887d36f3ef1e86e3c78272f1d6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ff31feb8037e8115", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754451619144, "stop": 1754451632488, "duration": 13344}}]}, "fd79b0d35f1f4639521f70b269d3aadc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e4feab036331402e", "status": "passed", "time": {"start": 1754453540495, "stop": 1754453556552, "duration": 16057}}]}, "d18ed3013dc7867440fb611fb474ca05": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "93ef6d13c079fd1", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Flashlight is turned off now']\nassert False", "time": {"start": 1754449517677, "stop": 1754449532009, "duration": 14332}}]}, "599b7a465f619c38a4638073f59c38c0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bd7c1e709ec319a6", "status": "passed", "time": {"start": 1754455462027, "stop": 1754455476062, "duration": 14035}}]}, "3a27fef360a79f638f96f0461df262da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ee7c18c0f957d724", "status": "passed", "time": {"start": 1754454496546, "stop": 1754454509946, "duration": 13400}}]}, "2428ad915810150c12838b88ee13f49c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c8046c28bc1e5fe8", "status": "passed", "time": {"start": 1754448296498, "stop": 1754448319012, "duration": 22514}}]}, "16f38913a9d7e0ffc4c5ac51d5acf5c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9204d83b6b8f7fd2", "status": "passed", "time": {"start": 1754451033561, "stop": 1754451047165, "duration": 13604}}]}, "18fb8c43c609a9825fe52e528761fd1b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1cb174fb389581dd", "status": "passed", "time": {"start": 1754450037016, "stop": 1754450055449, "duration": 18433}}]}, "cf4d81285cd0bfb49bf81dabe5eaa538": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9bc8f1759283437a", "status": "passed", "time": {"start": 1754449318689, "stop": 1754449336910, "duration": 18221}}]}, "519d11d818a361bc75d5af94c6a68b28": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "53b8bc8221a23244", "status": "passed", "time": {"start": 1754449100344, "stop": 1754449114223, "duration": 13879}}]}, "9511be8e6426d5078713c6e78f3b02e3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "84f9ea91bdf0b93d", "status": "passed", "time": {"start": 1754449920126, "stop": 1754449933865, "duration": 13739}}]}, "fc75b92fb4a100575b2c948dd6c5a008": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bef9248c1ac6d9e8", "status": "passed", "time": {"start": 1754452795257, "stop": 1754452809330, "duration": 14073}}]}, "ae86b8d534909e1e7c8c7adb4ee39e5c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9aee619060e4ff8", "status": "passed", "time": {"start": 1754451061346, "stop": 1754451074571, "duration": 13225}}]}, "57acf2797af332487c1fdb9a53a30e4f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c58e43308502a4be", "status": "passed", "time": {"start": 1754452612143, "stop": 1754452626314, "duration": 14171}}]}, "c0d6ce0b7c5e41242c01a6e0c0186608": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bb166634eb4139ad", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"I'm <PERSON>\"]\nassert False", "time": {"start": 1754449072637, "stop": 1754449086313, "duration": 13676}}]}, "4b05cca85015ddfc6c9bfc65934f9bcc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ef7a049e326a2e58", "status": "failed", "statusDetails": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', 'No call record', '', '']'\nassert False", "time": {"start": 1754453935890, "stop": 1754453958417, "duration": 22527}}]}, "643a7bbfbf5c5eedbae7ae814fbc8b52": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a1ae04a8c91793c9", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1754450715220, "stop": 1754450728748, "duration": 13528}}]}, "3dae350db69abded432b3e7f5f8463c8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b6962caf581b09dc", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Here's a joke for you\"]\nassert False", "time": {"start": 1754448852645, "stop": 1754448867433, "duration": 14788}}]}, "b9bb05ac1dcf8926da63d4ecbb1524cd": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "acc50f49b95172e6", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Bluetooth is turned off now']\nassert False", "time": {"start": 1754449489597, "stop": 1754449503246, "duration": 13649}}]}, "92e2909ea81e82011e43342b4fc06c3b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bee1c22929dac17d", "status": "passed", "time": {"start": 1754454413139, "stop": 1754454427224, "duration": 14085}}]}, "772728b3468560788490a3673352724d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c6a43475c0c2675", "status": "passed", "time": {"start": 1754448824871, "stop": 1754448838812, "duration": 13941}}]}, "57a9b2e6f318afd186b838ed42ebd55c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2ed5255e29720bb5", "status": "passed", "time": {"start": 1754452766436, "stop": 1754452781916, "duration": 15480}}]}, "4933b925ec694ecfcd17b3423ac28184": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "253154c273af4b74", "status": "passed", "time": {"start": 1754451557799, "stop": 1754451575932, "duration": 18133}}]}, "fead54f5f2ccb14942215ef4a8f481bf": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8b898a5d16f54ad9", "status": "failed", "statusDetails": "AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nassert None", "time": {"start": 1754449288241, "stop": 1754449304334, "duration": 16093}}]}, "3d685d9ca6a0d7795be3c96921595318": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d3a977ef03b1f2dc", "status": "passed", "time": {"start": 1754452357295, "stop": 1754452371303, "duration": 14008}}]}, "28f9087c186df37701fba71f366c084e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "af6499461f251805", "status": "passed", "time": {"start": 1754451240559, "stop": 1754451254442, "duration": 13883}}]}, "bb51fd67dac102de95e755be72996bd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7a41b675a24c217c", "status": "passed", "time": {"start": 1754452385420, "stop": 1754452400847, "duration": 15427}}]}, "9458f45d3c37d9141658da9964a470f5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ab38d772841f6a8c", "status": "passed", "time": {"start": 1754454206931, "stop": 1754454221093, "duration": 14162}}]}, "51b4b46de04a8c1e37077a9f688cb490": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a427bf5606042741", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754453599545, "stop": 1754453614460, "duration": 14915}}]}, "e21c5dda6a9f09862a68c3a0bcda554a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "75b158406577622d", "status": "passed", "time": {"start": 1754454856985, "stop": 1754454872199, "duration": 15214}}]}, "2ead070abca949d328b727d85148a577": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b3bd4eefa66cd793", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording started']\nassert False", "time": {"start": 1754449632803, "stop": 1754449651634, "duration": 18831}}]}, "dc901cadfe1de0042de7c0f7461a804e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "42624ab66ae39dc7", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E389C42CD0>.wait_for_page_load", "time": {"start": 1754451993567, "stop": 1754451993567, "duration": 0}}]}, "543965b4120af95548616c95b1b70ef1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8f31162e952b18d0", "status": "passed", "time": {"start": 1754448767966, "stop": 1754448782951, "duration": 14985}}]}, "3e1e4da6344de7cdf40fa1d59c43dcc3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "73152c33f6262dbc", "status": "passed", "time": {"start": 1754448712091, "stop": 1754448726036, "duration": 13945}}]}, "c04d9357fdaf44e6ee27f8a97ece6c5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "db4f0bfa32d8b233", "status": "passed", "time": {"start": 1754454551661, "stop": 1754454565552, "duration": 13891}}]}, "5d0294174a7d609e38392f61f2170810": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1c1269d5fcc8834f", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B655110>.wait_for_page_load", "time": {"start": 1754451958469, "stop": 1754451958469, "duration": 0}}]}, "18415b75388fbfdac9a7e4232373c000": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2cd8741868006483", "status": "passed", "time": {"start": 1754447955355, "stop": 1754447970221, "duration": 14866}}]}, "564bd7767ef18af66db8dd5bf804e4f7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "142906c3e59f6be2", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"I'm glad to open Youtube Music for you\", 'You need to select music to play.']\nassert False", "time": {"start": 1754453740436, "stop": 1754453761597, "duration": 21161}}]}, "3bafa6d8eb5b49bc5b77f1784275285e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "262d606376ce8700", "status": "passed", "time": {"start": 1754450389334, "stop": 1754450403774, "duration": 14440}}]}, "3004e41c81a7ebd857f79d043aaf59df": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ca70ed4533183a70", "status": "passed", "time": {"start": 1754447984022, "stop": 1754448004637, "duration": 20615}}]}, "fa8d6ac5c42acf5f644e6f5370a9a773": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "70071cedc08d13cb", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E3894F0D50>.wait_for_page_load", "time": {"start": 1754452133677, "stop": 1754452133677, "duration": 0}}]}, "544fc8b021d2dbcaf295cd05b798f816": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4d4e365604efed50", "status": "passed", "time": {"start": 1754454966147, "stop": 1754454980412, "duration": 14265}}]}, "53ec4c77118606257c016fc2f7b22065": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "93130e85f011449", "status": "passed", "time": {"start": 1754447528618, "stop": 1754447549421, "duration": 20803}}]}, "a0efcebc4cee6024e690bd290b4f3fbb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "27ef3ceaf2e10ea4", "status": "passed", "time": {"start": 1754455296573, "stop": 1754455311690, "duration": 15117}}]}, "12eb3852c333145c5906579f2346c37a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6a31892470e25970", "status": "passed", "time": {"start": 1754447927479, "stop": 1754447941296, "duration": 13817}}]}, "1695232002b2ad29ffa1faf52965470d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8f7de97d7fdf87b6", "status": "passed", "time": {"start": 1754452442992, "stop": 1754452457124, "duration": 14132}}]}, "c451b6579afcff68472c518910d1ab7a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "886a52d253902aed", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754453003196, "stop": 1754453023848, "duration": 20652}}]}, "2060dd1cfd03194548c0456a10798266": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f137a3f44d2dcec2", "status": "passed", "time": {"start": 1754447836770, "stop": 1754447853314, "duration": 16544}}]}, "43a8e6496d8d78f2b8bc066858f8bdd9": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2f730eec0c9ba595", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754453655065, "stop": 1754453668838, "duration": 13773}}]}, "75c9edd252211f9e74fd8c1a2faeefd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5e085943d42f8b0c", "status": "passed", "time": {"start": 1754450069731, "stop": 1754450088254, "duration": 18523}}]}, "569e770c250388bfbcf64d0cbbb8b351": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cb9bf1bed63324f0", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B62E450>.wait_for_page_load", "time": {"start": 1754451923380, "stop": 1754451923380, "duration": 0}}]}, "7c74ed3e622ad29dd79be61222ad59bc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "409d5e47442fd23e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1754450139671, "stop": 1754450152968, "duration": 13297}}]}, "1615e8617cafbed9e30baf38018d96b0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "21927a811f40206b", "status": "passed", "time": {"start": 1754453972515, "stop": 1754453986298, "duration": 13783}}]}, "4ae696581fe41611547bc10ddba4f526": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c178e4faa8523e73", "status": "passed", "time": {"start": 1754448236986, "stop": 1754448253582, "duration": 16596}}]}, "5c75e7ecaa2fe55a9b9666aae0ca1b5a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "56d6727ada4cc121", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['done']\nassert False", "time": {"start": 1754455185080, "stop": 1754455200076, "duration": 14996}}]}, "8bcc4c0c2b314e79a7177168f7d787b8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "aff9de2e497af703", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B5E48D0>.wait_for_page_load", "time": {"start": 1754451888447, "stop": 1754451888447, "duration": 0}}]}, "56a09613cdb882018377e1c2c4e78472": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b030e1b3bc64bf48", "status": "passed", "time": {"start": 1754449163121, "stop": 1754449179047, "duration": 15926}}]}, "acdb323f998d6127fbebbc545f6e8a59": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2c63a150dba92152", "status": "passed", "time": {"start": 1754447033146, "stop": 1754447056127, "duration": 22981}}]}, "c076d6e18e779bfeb810e69b30339aa2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c8517a0d9fe0ff9f", "status": "passed", "time": {"start": 1754450772328, "stop": 1754450802490, "duration": 30162}}]}, "edbb2ef8b0440e0325be2bfae4eb0bee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "36e36fc2e197126e", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B594090>.wait_for_page_load", "time": {"start": 1754452203747, "stop": 1754452203747, "duration": 0}}]}, "bd4d204a449f3a4013b03af9a9101446": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "74841191efeb3379", "status": "passed", "time": {"start": 1754448081733, "stop": 1754448102779, "duration": 21046}}]}, "e8f03971277a71512b5ebaad612bc964": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6480df8a6c46a6db", "status": "passed", "time": {"start": 1754451457990, "stop": 1754451478299, "duration": 20309}}]}, "4cfe8e55b2a91a62bbf1141ffc0cc530": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2e9c0e779971b6f2", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B692F50>.wait_for_page_load", "time": {"start": 1754452098668, "stop": 1754452098668, "duration": 0}}]}, "d8a01bf3d9b9092622318d8d22f17d9e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6513ea7d676f9aee", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B372250>.wait_for_page_load", "time": {"start": 1754451748972, "stop": 1754451748972, "duration": 0}}]}, "75c56947a7b1061f7ec858fb20919b50": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8ba01c509e185f21", "status": "passed", "time": {"start": 1754451526706, "stop": 1754451543802, "duration": 17096}}]}, "b3fa1d22b59def5f059cd9b0eefbe2b0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2d413365c9e8c32f", "status": "passed", "time": {"start": 1754447898223, "stop": 1754447913407, "duration": 15184}}]}, "d18ea588937139bb162adb1092a66013": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bc9a3f7b0d5c458d", "status": "passed", "time": {"start": 1754451212018, "stop": 1754451226679, "duration": 14661}}]}, "548362627ce690e10e5f8ca35d247c62": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8b9d6241d0992877", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Media volume has been set to the maximum']\nassert False", "time": {"start": 1754449793578, "stop": 1754449806906, "duration": 13328}}]}, "a4af452e0448ec3c1ecc9afcc30459be": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "afe93fd71bbebe1b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1754450417620, "stop": 1754450431165, "duration": 13545}}]}, "861f0c94cdad5a5d60cd9fc71e2429d6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "548281780cb22e82", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Redirecting to']\nassert False", "time": {"start": 1754447721944, "stop": 1754447736646, "duration": 14702}}]}, "61e923bb9b35a687b231b0c27b5ec620": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8b366b5074a3353e", "status": "passed", "time": {"start": 1754455021098, "stop": 1754455034934, "duration": 13836}}]}, "30903d6e764eebda77a45c5af4464d00": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3a66753f7b7cbcdf", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"You haven't started working out yet\"]\nassert False", "time": {"start": 1754448626044, "stop": 1754448654749, "duration": 28705}}]}, "9c301cfc137fb94f119957b5f74291ec": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7f98b929b3063854", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Sorry, I couldn't locate the setting\"]\nassert False", "time": {"start": 1754455553984, "stop": 1754455569893, "duration": 15909}}]}, "7ba4a9d343c0f63e9f654ce03ea4fa51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bd18a5f48c6a81ad", "status": "passed", "time": {"start": 1754454912121, "stop": 1754454925915, "duration": 13794}}]}, "e865942f74e70950eccebd8243dd6035": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "334b9b2e3cf14180", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Calling Mom...']\nassert False", "time": {"start": 1754448881747, "stop": 1754448903362, "duration": 21615}}]}, "42bb23fa5566b20ae050e85bbee099ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c61bdf99a64855b6", "status": "passed", "time": {"start": 1754452639757, "stop": 1754452653831, "duration": 14074}}]}, "ffb0a39af30beaa699329479ec564117": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8db2598fd16f9037", "status": "passed", "time": {"start": 1754447125376, "stop": 1754447154145, "duration": 28769}}]}, "fca782bf64e9cf595a09003471d4cc31": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fee8f542d5d430f2", "status": "passed", "time": {"start": 1754453325062, "stop": 1754453348609, "duration": 23547}}]}, "ebd9dc4871a5e78e68997fd53d4e9d06": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fe60a039ac946371", "status": "passed", "time": {"start": 1754451179149, "stop": 1754451198065, "duration": 18916}}]}, "9c84b087eb7d9fde94ed5bb5370b275b": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b7f1fa9085faaa27", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1754453512052, "stop": 1754453526933, "duration": 14881}}]}, "0c44c94f08feed70addcec44e96bda5a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d9d41f3b82b40b56", "status": "passed", "time": {"start": 1754448947539, "stop": 1754448961326, "duration": 13787}}]}, "ffd7dc86cbeda13ca78bbca09f06422a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8712b4ac10ccc2e7", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754449574015, "stop": 1754449589731, "duration": 15716}}]}, "613ef0933e4be87696bbedd56b4f0052": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cc4b26a4d2f8fb86", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B490B90>.wait_for_page_load", "time": {"start": 1754451783661, "stop": 1754451783661, "duration": 0}}]}, "6df22ddc82daabcf8389e60296dc694e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c0c7a1cff2f3771e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Premier League Goals Ranking']\nassert False", "time": {"start": 1754448526292, "stop": 1754448541648, "duration": 15356}}]}, "17788b061637289a04732fe5840218ee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b21a04cbb6917b66", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Recording completed']\nassert False", "time": {"start": 1754447344785, "stop": 1754447359784, "duration": 14999}}]}, "7c862e3b7376f0fe8afd8d3c5d41a1ab": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6482ce3d1e710e63", "status": "passed", "time": {"start": 1754455583977, "stop": 1754455598021, "duration": 14044}}]}, "861aa58f9a3d0d9c9861d88316e784c5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "db0ee351a160247a", "status": "passed", "time": {"start": 1754447070116, "stop": 1754447083848, "duration": 13732}}]}, "1ca8d9c300d55584cdcf637ede08bdba": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fc814aaf55d3b79b", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B52D3D0>.wait_for_page_load", "time": {"start": 1754452238952, "stop": 1754452238952, "duration": 0}}]}, "1d15cba90ae0426fa12e3218f1c542a6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7a434d777ce8aa77", "status": "passed", "time": {"start": 1754446786785, "stop": 1754446800286, "duration": 13501}}]}, "cda905ef365af8bbcc5fba28f6bde9ea": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e068de8b2837d67", "status": "passed", "time": {"start": 1754450325189, "stop": 1754450343632, "duration": 18443}}]}, "0b659537bc9c9b47c2c23f702fadd56b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "14097408c33e2291", "status": "passed", "time": {"start": 1754452414652, "stop": 1754452428842, "duration": 14190}}]}, "bc062eca91b16841cac5c9865921b5c1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d9283c555a0dfffd", "status": "passed", "time": {"start": 1754446615376, "stop": 1754446629166, "duration": 13790}}]}, "b7e448432379b6f8a430f1cbdb3ee3fb": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "16c844c54a22bc45", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "time": {"start": 1754448494705, "stop": 1754448511861, "duration": 17156}}]}, "d13fb0644c88e807e0b804b1986ee770": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dadc4cc89b23040e", "status": "passed", "time": {"start": 1754453847145, "stop": 1754453862367, "duration": 15222}}]}, "ff8d76af98b9fdfaa206acbf87daa843": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "31459d3eaf00f3fc", "status": "passed", "time": {"start": 1754452703528, "stop": 1754452725928, "duration": 22400}}]}, "1da800483d0bd7f8dbe657a8d5c37f76": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c298b828e0b31f92", "status": "failed", "statusDetails": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = wait_for_page_load(timeout=15)\n +    where wait_for_page_load = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001E38B6AF210>.wait_for_page_load", "time": {"start": 1754452063624, "stop": 1754452063624, "duration": 0}}]}, "6f7052acfdd45e34e5dded44ad87416e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "46957ed2e17cc77b", "status": "passed", "time": {"start": 1754449763285, "stop": 1754449779835, "duration": 16550}}]}, "00e9182de9c9d3297d90ff42d6771a57": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8064999615978c60", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1754454635493, "stop": 1754454650849, "duration": 15356}}]}, "abcb20b3882e9c0dbf1add7f63082581": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9fe241edfc82795", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754453906592, "stop": 1754453921460, "duration": 14868}}]}, "0c4bd81bf0dbac094265e3ac47550bbd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e5968f58913b35a0", "status": "passed", "time": {"start": 1754448917614, "stop": 1754448933320, "duration": 15706}}]}, "d41a9c89a400d807309a9cecf36c0728": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4722e4f5730d45cb", "status": "passed", "time": {"start": 1754447308540, "stop": 1754447331031, "duration": 22491}}]}, "354fa8bae544cb695857ab141abd74eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9f2b049c8f60a56c", "status": "passed", "time": {"start": 1754453709213, "stop": 1754453726412, "duration": 17199}}]}, "6c315a350a546e1382e435255d28245b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "575d4153a349b439", "status": "passed", "time": {"start": 1754454579451, "stop": 1754454593442, "duration": 13991}}]}, "b4e75f584d82368436f820de28f92cfd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "46a3ccf5a87f0c13", "status": "passed", "time": {"start": 1754447167871, "stop": 1754447191593, "duration": 23722}}]}, "753ba105235625e906d023bd3aaa0821": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9c90db8fe9592caf", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1754450658321, "stop": 1754450671918, "duration": 13597}}]}, "d8bd499fa9e4e04741c5c255fac9036d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1a255e5e8ebcea4c", "status": "passed", "time": {"start": 1754453396189, "stop": 1754453412199, "duration": 16010}}]}, "936ae2bf6db744b69d4acf28b22f7646": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "17b298548e69b1bf", "status": "passed", "time": {"start": 1754446944051, "stop": 1754446966739, "duration": 22688}}]}, "780a5f59d7531d4a5862437bbbfcf535": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b89d9ddd74405bb8", "status": "passed", "time": {"start": 1754453425579, "stop": 1754453439364, "duration": 13785}}]}, "436193bc4e8c44d21b6520da0589f88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "656ca2d1faac87de", "status": "passed", "time": {"start": 1754453202437, "stop": 1754453217767, "duration": 15330}}]}, "ab2195315637668cad08b0606ef7ff17": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4fa73543ebc54fd5", "status": "passed", "time": {"start": 1754453165713, "stop": 1754453188918, "duration": 23205}}]}, "794f685415bbcd702feae0b55a4dd537": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4f13cb22f11a7ed0", "status": "passed", "time": {"start": 1754448597918, "stop": 1754448611893, "duration": 13975}}]}, "7b1fce8b7d3ff59dca969b439bb82f75": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cc30defe424c2046", "status": "broken", "statusDetails": "TypeError: a bytes-like object is required, not 'dict'", "time": {"start": 1754447591529, "stop": 1754447607164, "duration": 15635}}]}, "48a2a80bfed06f0c82b99a0aaa26e252": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3fc3f1d2266214de", "status": "passed", "time": {"start": 1754453297139, "stop": 1754453311552, "duration": 14413}}]}, "b2f52f3c587a626460e5698cac861baf": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "70607ed82c3ddd8b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754450593240, "stop": 1754450616621, "duration": 23381}}]}, "78164cec6ab8359be9416229a4882ef9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d8d80c1a8a7aa2ba", "status": "passed", "time": {"start": 1754447660529, "stop": 1754447677746, "duration": 17217}}]}, "f06396240414bb7ac3c5c049002eef1e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c35c0f8a3add0d33", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754454086588, "stop": 1754454104322, "duration": 17734}}]}, "78de5607a6208f59723ba6cf4fcf09c4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "50ac8cebb18698b6", "status": "passed", "time": {"start": 1754448143739, "stop": 1754448155894, "duration": 12155}}]}, "1ab60ce22774c460e557aaa3b3f9120a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "36b8c2f8e2765835", "status": "passed", "time": {"start": 1754450264491, "stop": 1754450280445, "duration": 15954}}]}, "eda16efd838471b84f33f12ec91662c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "88ea7dfcbacf2d15", "status": "passed", "time": {"start": 1754454179151, "stop": 1754454193174, "duration": 14023}}]}, "aecf9a6f67cd29766190cbcc133448d2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "494274bea1d2dec1", "status": "passed", "time": {"start": 1754454330251, "stop": 1754454343621, "duration": 13370}}]}, "ffd9b4e8a27f1469e05050bd5989e500": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cddd64f0dce015e5", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1754450630749, "stop": 1754450644126, "duration": 13377}}]}, "9420fd606a04614c6f09bf36f2873f93": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bb94c3185f87279a", "status": "passed", "time": {"start": 1754450472924, "stop": 1754450486952, "duration": 14028}}]}, "45b57073b776ed5666f2f20a47a4638f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "42ccbac9fc7e7802", "status": "passed", "time": {"start": 1754450565314, "stop": 1754450579258, "duration": 13944}}]}, "454f04318d433db60e7e6f2de5790fc3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "11613cef42c356cf", "status": "passed", "time": {"start": 1754450294402, "stop": 1754450310643, "duration": 16241}}]}, "c45aa63628fe12b375ba7e65c39d93b1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "765be39b94c2f21d", "status": "passed", "time": {"start": 1754448797093, "stop": 1754448810624, "duration": 13531}}]}, "fae56e9bcf9e0511ef4a7c93775731e3": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "aaf2e2359901cf45", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1754451704338, "stop": 1754451719424, "duration": 15086}}]}, "154a720f41d8f5a908552e8c7cf8e781": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "47576954d15fc148", "status": "passed", "time": {"start": 1754454357679, "stop": 1754454371310, "duration": 13631}}]}, "7d4e24549f0551613cc723b633b9d3c8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cd943b7f9abecf97", "status": "passed", "time": {"start": 1754450949206, "stop": 1754450963531, "duration": 14325}}]}, "afa6af304cfb25a990764680de5fa777": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "930f24c0b46c4205", "status": "passed", "time": {"start": 1754452822505, "stop": 1754452836329, "duration": 13824}}]}, "d927afc2c4987ea6c13c14173a35bc45": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1f1ccbe7a9a3e46f", "status": "passed", "time": {"start": 1754453361932, "stop": 1754453382979, "duration": 21047}}]}, "fcabf101e08450542157f8740eeec9a7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "199c7c5fd883ed42", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754451590134, "stop": 1754451604950, "duration": 14816}}]}, "d094a0b21c0bd532e6db707dcbab5564": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "103eff9072b05d94", "status": "passed", "time": {"start": 1754452471155, "stop": 1754452485017, "duration": 13862}}]}, "92553bffc13049b2d4fa1afe9cf89498": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "32a5aa19059db7ec", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nassert None", "time": {"start": 1754447430794, "stop": 1754447456077, "duration": 25283}}]}, "d2d9aa669417404f06e84a7a4387c55a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d95aec3991ba9c49", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1754450528080, "stop": 1754450550941, "duration": 22861}}]}}