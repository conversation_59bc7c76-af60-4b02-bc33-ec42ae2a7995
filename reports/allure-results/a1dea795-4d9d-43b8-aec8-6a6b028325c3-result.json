{"name": "测试whats the weather today能正常执行", "status": "passed", "description": "whats the weather today", "steps": [{"name": "执行命令: whats the weather today", "status": "passed", "steps": [{"name": "执行命令: whats the weather today", "status": "passed", "start": 1754496541296, "stop": 1754496561948}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "12ec9798-723d-46fe-a6a7-5eef4dca77a4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "52d4e02f-7aed-4239-9560-7fab03650819-attachment.png", "type": "image/png"}], "start": 1754496561948, "stop": 1754496562115}], "start": 1754496541296, "stop": 1754496562115}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496562115, "stop": 1754496562117}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "be1fe449-32e9-41de-8112-b3268af1a04c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b798fe5d-151c-4dec-8f7e-221c4316d8aa-attachment.png", "type": "image/png"}], "start": 1754496562117, "stop": 1754496562274}], "attachments": [{"name": "stdout", "source": "227e80ab-9bcd-44cc-b8c0-fbdc7876dd7d-attachment.txt", "type": "text/plain"}], "start": 1754496541296, "stop": 1754496562275, "uuid": "bf79f561-af04-492c-9db8-3be9fd9a9d00", "historyId": "acca7d0b06a28ad8e6ccfe3c35828ce1", "testCaseId": "acca7d0b06a28ad8e6ccfe3c35828ce1", "fullName": "testcases.test_ella.dialogue.test_whats_the_weather_today.TestEllaWhatsWeatherToday#test_whats_the_weather_today", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_whats_the_weather_today"}, {"name": "subSuite", "value": "TestEllaWhatsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_whats_the_weather_today"}]}