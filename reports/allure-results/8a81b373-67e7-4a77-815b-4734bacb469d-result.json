{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "status": "passed", "description": "Help me write an email to make an appointment for a visit", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "status": "passed", "steps": [{"name": "执行命令: Help me write an email to make an appointment for a visit", "status": "passed", "start": 1754495316724, "stop": 1754495331525}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "be2d4981-a122-4bcd-b903-99269c1951de-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7b6da42a-2c15-47a5-849d-b7bfca69f3db-attachment.png", "type": "image/png"}], "start": 1754495331525, "stop": 1754495331736}], "start": 1754495316724, "stop": 1754495331737}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495331737, "stop": 1754495331739}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "75904cd7-1379-4242-b46f-5a5afc916dd9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ea49e1b3-3b44-40d6-9e50-ed76cc491067-attachment.png", "type": "image/png"}], "start": 1754495331739, "stop": 1754495331924}], "attachments": [{"name": "stdout", "source": "db513e81-8a89-48b4-a186-c22fe42c6cd6-attachment.txt", "type": "text/plain"}], "start": 1754495316724, "stop": 1754495331925, "uuid": "ebfb83ea-ab51-4474-b0f7-7526b3ea097a", "historyId": "12eb3852c333145c5906579f2346c37a", "testCaseId": "12eb3852c333145c5906579f2346c37a", "fullName": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit.TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit#test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_help_me_write_an_email_to_make_an_appointment_for_a_visit"}]}