{"name": "测试continue music能正常执行", "status": "passed", "description": "continue music", "steps": [{"name": "执行命令: continue music", "status": "passed", "steps": [{"name": "执行命令: continue music", "status": "passed", "start": 1754493987047, "stop": 1754494000680}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "258312b9-c64b-411a-b882-c68b675a449d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dfde2311-9810-4a7a-802e-153412a3a59b-attachment.png", "type": "image/png"}], "start": 1754494000680, "stop": 1754494000922}], "start": 1754493987047, "stop": 1754494000922}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754494000922, "stop": 1754494000924}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b67e9559-5ed1-4826-a232-5d8e30011f52-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1d531302-894b-4f19-baa2-eb09cee76655-attachment.png", "type": "image/png"}], "start": 1754494000924, "stop": 1754494001097}], "attachments": [{"name": "stdout", "source": "d37dc334-28d4-41db-bdc3-b3c5928d8fe5-attachment.txt", "type": "text/plain"}], "start": 1754493987047, "stop": 1754494001097, "uuid": "0e22ace5-02a7-4b1c-9b72-5f47caddd780", "historyId": "87f3dc53ab72c729262e053c16a3dbcb", "testCaseId": "87f3dc53ab72c729262e053c16a3dbcb", "fullName": "testcases.test_ella.component_coupling.test_continue_music.TestEllaContinueMusic#test_continue_music", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_continue_music"}, {"name": "subSuite", "value": "TestEllaContinueMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_continue_music"}]}