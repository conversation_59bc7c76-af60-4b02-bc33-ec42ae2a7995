{"name": "测试Search for addresses on the screen能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', '以下是一些在屏幕上搜索地址的常见方法：\\n\\n使用地图应用 ：像高德地图、百度地图、腾讯地图等手机地图应用都具备地址搜索功能。你只需打开相应的地图应用，在搜索框中输入你要查找的地址，地图就会为你显示相关的位置信息以及导航选项等。例如，在高德地图中，你可以通过点击搜索栏，输入地址关键词，如具体的街道名称、小区名字、商家名称等，然后从搜索结果中选择你想要的地址12478。\\n利用浏览器搜索 ：在手机浏览器的搜索框中输入地址相关信息，浏览器会根据你输入的内容，在网页中查找与之匹配的地址信息。比如，你可以输入城市名+具体地址，搜索引擎会尽力为你提供相关的网页链接，这些链接可能包含你所需要的地址详情，如商家的具体位置介绍、某个地方的具体坐标等。\\n借助特定的搜索工具或应用 ：有些专门的地址搜索工具或应用程序，它们可能针对特定的领域或需求提供更精准的地址搜索服务。例如，一些房产类应用可以帮助你搜索特定区域的房屋地址及相关信息；物流类应用能让你查找快递网点的地址等。', '对话 发现 8 篇参考资料 以下是一些在屏幕上搜索地址的常见方法：&#10;&#10;使用地图应用 ：像高德地图、百度地图、腾讯地图等手机地图应用都具备地址搜索功能。你只需打开相应的地图应用，在搜索框中输入你要查找的地址，地图就会为你显示相关的位置信息以及导航选项等。例如，在高德地图中，你可以通过点击搜索栏，输入地址关键词，如具体的街道名称、小区名字、商家名称等，然后从搜索结果中选择你想要的地址12478。&#10;利用浏览器搜索 ：在手机浏览器的搜索框中输入地址相关信息，浏览器会根据你输入的内容，在网页中查找与之匹配的地址信息。比如，你可以输入城市名+具体地址，搜索引擎会尽力为你提供相关的网页链接，这些链接可能包含你所需要的地址详情，如商家的具体位置介绍、某个地方的具体坐标等。&#10;借助特定的搜索工具或应用 ：有些专门的地址搜索工具或应用程序，它们可能针对特定的领域或需求提供更精准的地址搜索服务。例如，一些房产类应用可以帮助你搜索特定区域的房屋地址及相关信息；物流类应用能让你查找快递网点的地址等。 DeepSeek-R1 有问题尽管问我… 1:31']'\nassert None", "trace": "self = <testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen object at 0x000001BE0D38CCD0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0FB22850>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_for_addresses_on_the_screen(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', '以下是一些在屏幕上搜索地址的常见方法：\\n\\n使用地图应用 ：像高德地图、百度地图、腾讯地图等手机地图应用都具备地址搜索功能。你只需打开相应的地图应用，在搜索框中输入你要查找的地址，地图就会为你显示相关的位置信息以及导航选项等。例如，在高德地图中，你可以通过点击搜索栏，输入地址关键词，如具体的街道名称、小区名字、商家名称等，然后从搜索结果中选择你想要的地址12478。\\n利用浏览器搜索 ：在手机浏览器的搜索框中输入地址相关信息，浏览器会根据你输入的内容，在网页中查找与之匹配的地址信息。比如，你可以输入城市名+具体地址，搜索引擎会尽力为你提供相关的网页链接，这些链接可能包含你所需要的地址详情，如商家的具体位置介绍、某个地方的具体坐标等。\\n借助特定的搜索工具或应用 ：有些专门的地址搜索工具或应用程序，它们可能针对特定的领域或需求提供更精准的地址搜索服务。例如，一些房产类应用可以帮助你搜索特定区域的房屋地址及相关信息；物流类应用能让你查找快递网点的地址等。', '对话 发现 8 篇参考资料 以下是一些在屏幕上搜索地址的常见方法：&#10;&#10;使用地图应用 ：像高德地图、百度地图、腾讯地图等手机地图应用都具备地址搜索功能。你只需打开相应的地图应用，在搜索框中输入你要查找的地址，地图就会为你显示相关的位置信息以及导航选项等。例如，在高德地图中，你可以通过点击搜索栏，输入地址关键词，如具体的街道名称、小区名字、商家名称等，然后从搜索结果中选择你想要的地址12478。&#10;利用浏览器搜索 ：在手机浏览器的搜索框中输入地址相关信息，浏览器会根据你输入的内容，在网页中查找与之匹配的地址信息。比如，你可以输入城市名+具体地址，搜索引擎会尽力为你提供相关的网页链接，这些链接可能包含你所需要的地址详情，如商家的具体位置介绍、某个地方的具体坐标等。&#10;借助特定的搜索工具或应用 ：有些专门的地址搜索工具或应用程序，它们可能针对特定的领域或需求提供更精准的地址搜索服务。例如，一些房产类应用可以帮助你搜索特定区域的房屋地址及相关信息；物流类应用能让你查找快递网点的地址等。 DeepSeek-R1 有问题尽管问我… 1:31']'\nE           assert None\n\ntestcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py:36: AssertionError"}, "description": "Search for addresses on the screen", "steps": [{"name": "执行命令: Search for addresses on the screen", "status": "passed", "steps": [{"name": "执行命令: Search for addresses on the screen", "status": "passed", "start": 1754501471953, "stop": 1754501488018}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1ec2bab6-4dad-42da-ae6d-c36d77526e86-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5250e3eb-4e19-4b1b-8fb9-fe81ac1ac96c-attachment.png", "type": "image/png"}], "start": 1754501488018, "stop": 1754501488260}], "start": 1754501471952, "stop": 1754501488260}, {"name": "验证GoogleMap应用已打开", "status": "failed", "statusDetails": {"message": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['Search for addresses on the screen', '', '', '', '以下是一些在屏幕上搜索地址的常见方法：\\n\\n使用地图应用 ：像高德地图、百度地图、腾讯地图等手机地图应用都具备地址搜索功能。你只需打开相应的地图应用，在搜索框中输入你要查找的地址，地图就会为你显示相关的位置信息以及导航选项等。例如，在高德地图中，你可以通过点击搜索栏，输入地址关键词，如具体的街道名称、小区名字、商家名称等，然后从搜索结果中选择你想要的地址12478。\\n利用浏览器搜索 ：在手机浏览器的搜索框中输入地址相关信息，浏览器会根据你输入的内容，在网页中查找与之匹配的地址信息。比如，你可以输入城市名+具体地址，搜索引擎会尽力为你提供相关的网页链接，这些链接可能包含你所需要的地址详情，如商家的具体位置介绍、某个地方的具体坐标等。\\n借助特定的搜索工具或应用 ：有些专门的地址搜索工具或应用程序，它们可能针对特定的领域或需求提供更精准的地址搜索服务。例如，一些房产类应用可以帮助你搜索特定区域的房屋地址及相关信息；物流类应用能让你查找快递网点的地址等。', '对话 发现 8 篇参考资料 以下是一些在屏幕上搜索地址的常见方法：&#10;&#10;使用地图应用 ：像高德地图、百度地图、腾讯地图等手机地图应用都具备地址搜索功能。你只需打开相应的地图应用，在搜索框中输入你要查找的地址，地图就会为你显示相关的位置信息以及导航选项等。例如，在高德地图中，你可以通过点击搜索栏，输入地址关键词，如具体的街道名称、小区名字、商家名称等，然后从搜索结果中选择你想要的地址12478。&#10;利用浏览器搜索 ：在手机浏览器的搜索框中输入地址相关信息，浏览器会根据你输入的内容，在网页中查找与之匹配的地址信息。比如，你可以输入城市名+具体地址，搜索引擎会尽力为你提供相关的网页链接，这些链接可能包含你所需要的地址详情，如商家的具体位置介绍、某个地方的具体坐标等。&#10;借助特定的搜索工具或应用 ：有些专门的地址搜索工具或应用程序，它们可能针对特定的领域或需求提供更精准的地址搜索服务。例如，一些房产类应用可以帮助你搜索特定区域的房屋地址及相关信息；物流类应用能让你查找快递网点的地址等。 DeepSeek-R1 有问题尽管问我… 1:31']'\nassert None\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_search_for_addresses_on_the_screen.py\", line 36, in test_search_for_addresses_on_the_screen\n    assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1754501488260, "stop": 1754501488260}], "attachments": [{"name": "stdout", "source": "99b61d7c-37ac-4938-9192-1d5da8dfab7c-attachment.txt", "type": "text/plain"}], "start": 1754501471952, "stop": 1754501488261, "uuid": "72eda591-ac16-4bf8-8e9a-5bdbbeb4114e", "historyId": "8814f1dafa698e785ee1f58faa6e745d", "testCaseId": "8814f1dafa698e785ee1f58faa6e745d", "fullName": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen.TestEllaSearchAddressesScreen#test_search_for_addresses_on_the_screen", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_for_addresses_on_the_screen"}, {"name": "subSuite", "value": "TestEllaSearchAddressesScreen"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_for_addresses_on_the_screen"}]}