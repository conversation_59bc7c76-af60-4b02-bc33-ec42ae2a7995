{"name": "测试open flashlight", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open flashlight", "status": "passed", "steps": [{"name": "执行命令: open flashlight", "status": "passed", "start": 1754497369635, "stop": 1754497384315}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1a7e1971-aaf2-4289-8f2b-9c80188a8a41-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "97159949-30da-4e5b-85e3-30e8181ee80a-attachment.png", "type": "image/png"}], "start": 1754497384316, "stop": 1754497384519}], "start": 1754497369635, "stop": 1754497384519}, {"name": "验证响应包含Done", "status": "passed", "start": 1754497384519, "stop": 1754497384520}, {"name": "验证flashlight已打开", "status": "passed", "start": 1754497384520, "stop": 1754497384520}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e4742066-2096-4eb2-b2ca-ea5aad958f51-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a72ef826-cc58-4c0e-94e0-c6767d8ebdc6-attachment.png", "type": "image/png"}], "start": 1754497384520, "stop": 1754497384713}], "attachments": [{"name": "stdout", "source": "4a858204-8a34-40c8-8a24-5fab6d643043-attachment.txt", "type": "text/plain"}], "start": 1754497369635, "stop": 1754497384714, "uuid": "2c9606df-0667-44e3-ab5f-93cf9b3d3e8f", "historyId": "66496441d7401e453a580b4a8c23d111", "testCaseId": "66496441d7401e453a580b4a8c23d111", "fullName": "testcases.test_ella.system_coupling.test_open_flashlight.TestEllaCommandConcise#test_open_flashlight", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_open_flashlight"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_open_flashlight"}]}