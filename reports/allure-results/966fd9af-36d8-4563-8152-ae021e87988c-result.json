{"name": "测试what·s the weather today？能正常执行", "status": "passed", "description": "what·s the weather today？", "steps": [{"name": "执行命令: what·s the weather today？", "status": "passed", "steps": [{"name": "执行命令: what·s the weather today？", "status": "passed", "start": 1754496420494, "stop": 1754496441411}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1596d469-403b-4129-b1dd-feab36b09039-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6ba57d4f-22bc-4a55-85d2-34b1d6023cd1-attachment.png", "type": "image/png"}], "start": 1754496441411, "stop": 1754496441605}], "start": 1754496420494, "stop": 1754496441605}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496441605, "stop": 1754496441607}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0b841e99-a0bf-4c02-abb0-2b4603156e64-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "94b6fde0-8ec4-42e3-9d1e-95101fab5420-attachment.png", "type": "image/png"}], "start": 1754496441607, "stop": 1754496441790}], "attachments": [{"name": "stdout", "source": "acb3e225-9381-4bc8-bcfa-e83147608f46-attachment.txt", "type": "text/plain"}], "start": 1754496420494, "stop": 1754496441790, "uuid": "23ba887c-5798-4831-a330-ce9eb9fe969e", "historyId": "8d12bedb52d3f000f4269afc25f3fe30", "testCaseId": "8d12bedb52d3f000f4269afc25f3fe30", "fullName": "testcases.test_ella.dialogue.test_what_s_the_weather_today.TestEllaWhatSWeatherToday#test_what_s_the_weather_today", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_s_the_weather_today"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_s_the_weather_today"}]}