{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['check mobile data balance of sim2 SIM 2 not detected', 'Please check and try again']\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_check_mobile_data_balance_of_sim.TestEllaCheckMobileDataBalanceSim object at 0x000001BE0D2BB190>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0F258710>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_check_mobile_data_balance_of_sim(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\unsupported_commands\\test_check_mobile_data_balance_of_sim.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.unsupported_commands.test_check_mobile_data_balance_of_sim.TestEllaCheckMobileDataBalanceSim object at 0x000001BE0D2BB190>\nexpected_text = ['check mobile data balance of sim2 SIM 2 not detected', 'Please check and try again'], response_text = ['check mobile data balance of sim2', '没有SIM 卡 2 ，请确认后重试', '', '']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['check mobile data balance of sim2 SIM 2 not detected', 'Please check and try again']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:923: AssertionError"}, "description": "验证check mobile data balance of sim2指令返回预期的不支持响应", "steps": [{"name": "执行命令: check mobile data balance of sim2", "status": "passed", "steps": [{"name": "执行命令: check mobile data balance of sim2", "status": "passed", "start": 1754499273543, "stop": 1754499285792}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e672e4e3-442c-433e-a49b-c6e88725af6b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "58b71626-28b3-47a4-9865-8476200cb08b-attachment.png", "type": "image/png"}], "start": 1754499285792, "stop": 1754499285968}], "start": 1754499273542, "stop": 1754499285969}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['check mobile data balance of sim2 SIM 2 not detected', 'Please check and try again']\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_check_mobile_data_balance_of_sim.py\", line 32, in test_check_mobile_data_balance_of_sim\n    result = self.verify_expected_in_response(expected_text, response_text)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 923, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n"}, "start": 1754499285969, "stop": 1754499285971}], "attachments": [{"name": "stdout", "source": "f8cb4571-83fc-453f-a93f-ac38b44491a9-attachment.txt", "type": "text/plain"}], "start": 1754499273542, "stop": 1754499285972, "uuid": "bdd17f28-9bc6-46cc-8a74-2843e2db02ad", "historyId": "7c50481bc992b9ff109abbeeeece073a", "testCaseId": "7c50481bc992b9ff109abbeeeece073a", "fullName": "testcases.test_ella.unsupported_commands.test_check_mobile_data_balance_of_sim.TestEllaCheckMobileDataBalanceSim#test_check_mobile_data_balance_of_sim", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_mobile_data_balance_of_sim"}, {"name": "subSuite", "value": "TestEllaCheckMobileDataBalanceSim"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_mobile_data_balance_of_sim"}]}