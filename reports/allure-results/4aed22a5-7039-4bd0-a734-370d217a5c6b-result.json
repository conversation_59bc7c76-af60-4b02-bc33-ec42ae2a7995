{"name": "测试close whatsapp能正常执行", "status": "passed", "description": "close whatsapp", "steps": [{"name": "执行命令: close whatsapp", "status": "passed", "steps": [{"name": "执行命令: close whatsapp", "status": "passed", "start": 1754495124479, "stop": 1754495139867}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ffdd8d4e-ff68-4992-a3fd-0ae40e07a1ab-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "426bd104-74b2-4ad3-ae10-cc91f4e51783-attachment.png", "type": "image/png"}], "start": 1754495139867, "stop": 1754495140036}], "start": 1754495124479, "stop": 1754495140037}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495140037, "stop": 1754495140038}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c1b1023f-bcf6-41d3-ae68-5bbf005eda05-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c4fd2167-5bc7-4847-bab9-966dd180dcc3-attachment.png", "type": "image/png"}], "start": 1754495140038, "stop": 1754495140225}], "attachments": [{"name": "stdout", "source": "3068f1f1-4f3b-4eb6-98bd-63e8fa5bc40d-attachment.txt", "type": "text/plain"}], "start": 1754495124479, "stop": 1754495140225, "uuid": "a179afa2-0ddd-4ea1-9e0d-04d11f9ad86f", "historyId": "876e77318cece5d1079b726f0c97bc45", "testCaseId": "876e77318cece5d1079b726f0c97bc45", "fullName": "testcases.test_ella.dialogue.test_close_whatsapp.TestEllaCloseWhatsapp#test_close_whatsapp", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_close_whatsapp"}, {"name": "subSuite", "value": "TestEllaCloseWhatsapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_close_whatsapp"}]}