{"name": "测试next channel能正常执行", "status": "passed", "description": "next channel", "steps": [{"name": "执行命令: next channel", "status": "passed", "steps": [{"name": "执行命令: next channel", "status": "passed", "start": 1754494138698, "stop": 1754494152479}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "81b56bde-4f43-4751-b1c4-a398f8c43d75-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d6c2c7e5-08cb-4ad3-bd2e-c3ff5cee8ed9-attachment.png", "type": "image/png"}], "start": 1754494152479, "stop": 1754494152693}], "start": 1754494138698, "stop": 1754494152693}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754494152693, "stop": 1754494152695}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "74d15c99-1002-421b-9741-4943af8c34ea-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3d317734-29c6-4a9c-878d-b7b355ea8c2f-attachment.png", "type": "image/png"}], "start": 1754494152695, "stop": 1754494152892}], "attachments": [{"name": "stdout", "source": "1d841a88-7c57-4b00-8b23-e8592db49e40-attachment.txt", "type": "text/plain"}], "start": 1754494138698, "stop": 1754494152892, "uuid": "59c88c2b-3eb4-41f6-9f85-39838bfd3732", "historyId": "1d15cba90ae0426fa12e3218f1c542a6", "testCaseId": "1d15cba90ae0426fa12e3218f1c542a6", "fullName": "testcases.test_ella.component_coupling.test_next_channel.TestEllaNextChannel#test_next_channel", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_next_channel"}, {"name": "subSuite", "value": "TestEllaNextChannel"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_next_channel"}]}