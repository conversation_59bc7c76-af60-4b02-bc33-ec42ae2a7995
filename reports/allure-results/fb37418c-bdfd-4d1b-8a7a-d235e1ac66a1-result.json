{"name": "测试play music", "status": "passed", "description": "测试play music指令", "steps": [{"name": "执行命令: play music", "status": "passed", "steps": [{"name": "执行命令: play music", "status": "passed", "start": 1754494599541, "stop": 1754494622228}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "45f007fa-6fb3-4d0d-a5e9-65604d798803-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9de0d785-3cbd-4174-89b1-84a53cf8e55b-attachment.png", "type": "image/png"}], "start": 1754494622228, "stop": 1754494622405}], "start": 1754494599541, "stop": 1754494622405}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754494622405, "stop": 1754494622406}, {"name": "验证visha已打开", "status": "passed", "start": 1754494622406, "stop": 1754494622406}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0e7256c9-0c34-4d11-8664-33791d349fe7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c3dea069-85a9-4b80-b353-4ad565aa1685-attachment.png", "type": "image/png"}], "start": 1754494622406, "stop": 1754494622587}], "attachments": [{"name": "stdout", "source": "d72ebfdc-ff07-4c4d-b23c-d684549c07fb-attachment.txt", "type": "text/plain"}], "start": 1754494599541, "stop": 1754494622587, "uuid": "c2003f48-857c-4e76-9dbe-82f8764cfb94", "historyId": "148d3ba280bfe2b41b8464beec5f6763", "testCaseId": "148d3ba280bfe2b41b8464beec5f6763", "fullName": "testcases.test_ella.component_coupling.test_play_music.TestEllaOpenVisha#test_play_music", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_music"}, {"name": "subSuite", "value": "TestEllaOpen<PERSON>a"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_music"}]}