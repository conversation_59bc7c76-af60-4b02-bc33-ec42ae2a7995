{"name": "测试open camera能正常执行", "status": "passed", "description": "open camera", "steps": [{"name": "执行命令: open camera", "status": "passed", "steps": [{"name": "执行命令: open camera", "status": "passed", "start": 1754494166877, "stop": 1754494184719}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "957ac166-adba-4b5f-9af6-fef315bd38fa-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "82722c8c-dd53-4f03-9c06-3e1cf90a90f0-attachment.png", "type": "image/png"}], "start": 1754494184719, "stop": 1754494184914}], "start": 1754494166877, "stop": 1754494184914}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1754494184914, "stop": 1754494184915}, {"name": "验证已打开", "status": "passed", "start": 1754494184915, "stop": 1754494184915}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2cd86a20-e32a-476a-bf7c-8174245fcb83-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ff704d95-f7ba-48f1-ad24-c54017153bca-attachment.png", "type": "image/png"}], "start": 1754494184915, "stop": 1754494185108}], "attachments": [{"name": "stdout", "source": "6b7f1ac3-4608-40ab-82a5-74a10839f4d8-attachment.txt", "type": "text/plain"}], "start": 1754494166877, "stop": 1754494185108, "uuid": "ce4ec7e0-c26a-4fbc-bb72-a3427f3c2278", "historyId": "ae0ee984c3712fd05ea04b52289e14fe", "testCaseId": "ae0ee984c3712fd05ea04b52289e14fe", "fullName": "testcases.test_ella.component_coupling.test_open_camera.TestEllaCommandConcise#test_open_camera", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_camera"}]}