{"name": "测试What languages do you support能正常执行", "status": "passed", "description": "What languages do you support", "steps": [{"name": "执行命令: What languages do you support", "status": "passed", "steps": [{"name": "执行命令: What languages do you support", "status": "passed", "start": 1754496357358, "stop": 1754496371128}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "13111fe7-23e8-4c16-9d53-a57b081fad23-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "56f85a78-c670-4bfd-812a-8fdd1547d662-attachment.png", "type": "image/png"}], "start": 1754496371128, "stop": 1754496371311}], "start": 1754496357358, "stop": 1754496371312}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496371312, "stop": 1754496371313}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4c5a586c-0af6-4af2-a70c-48f74d30378d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f34b0967-6fe2-45d4-8833-6f3552ccccc9-attachment.png", "type": "image/png"}], "start": 1754496371313, "stop": 1754496371510}], "attachments": [{"name": "stdout", "source": "2aea7dd5-a5b0-4f90-92c0-9be114ff844e-attachment.txt", "type": "text/plain"}], "start": 1754496357357, "stop": 1754496371511, "uuid": "706488e1-0cdb-4671-8a15-c8af6a4e6792", "historyId": "0c44c94f08feed70addcec44e96bda5a", "testCaseId": "0c44c94f08feed70addcec44e96bda5a", "fullName": "testcases.test_ella.dialogue.test_what_languages_do_you_support.TestEllaWhatLanguagesDoYouSupport#test_what_languages_do_you_support", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_languages_do_you_support"}, {"name": "subSuite", "value": "TestEllaWhatLanguagesDoYouSupport"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_languages_do_you_support"}]}