{"name": "测试book a flight to paris返回正确的不支持响应", "status": "broken", "statusDetails": {"message": "TypeError: a bytes-like object is required, not 'dict'", "trace": "self = <testcases.test_ella.dialogue.test_book_a_flight_to_paris.TestEllaBookFlightParis object at 0x000001BE0D6775D0>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0DA374D0>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_book_a_flight_to_paris(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(\"记录测试结果\"):\n            summary = {\n                \"command\": command,\n                \"response_text\": response_text,\n                \"expected_text\": expected_text,\n                \"test_type\": \"unsupported_command\",\n                \"result\": \"PASS\" if result else \"FAIL\"\n            }\n>           self.attach_test_summary(summary)\n\ntestcases\\test_ella\\dialogue\\test_book_a_flight_to_paris.py:43: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntestcases\\test_ella\\base_ella_test.py:846: in attach_test_summary\n    allure.attach(summary, name=\"测试总结\", attachment_type=allure.attachment_type.TEXT)\n.venv\\Lib\\site-packages\\allure_commons\\_allure.py:210: in __call__\n    plugin_manager.hook.attach_data(body=body, name=name, attachment_type=attachment_type, extension=extension)\n.venv\\Lib\\site-packages\\pluggy\\_hooks.py:512: in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n.venv\\Lib\\site-packages\\pluggy\\_manager.py:120: in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n.venv\\Lib\\site-packages\\allure_pytest\\listener.py:252: in attach_data\n    self.allure_logger.attach_data(uuid4(), body, name=name, attachment_type=attachment_type, extension=extension)\n.venv\\Lib\\site-packages\\allure_commons\\reporter.py:165: in attach_data\n    plugin_manager.hook.report_attached_data(body=body, file_name=file_name)\n.venv\\Lib\\site-packages\\pluggy\\_hooks.py:512: in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n.venv\\Lib\\site-packages\\pluggy\\_manager.py:120: in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <allure_commons.logger.AllureFileLogger object at 0x000001BE0D096CD0>\nbody = {'command': 'book a flight to paris', 'expected_text': ['flight', 'paris'], 'response_text': ['book a flight to paris'...ght price comparison Paris flight booking tips DeepSeek-R1 Feel free to ask me any questions…'], 'result': 'PASS', ...}\nfile_name = 'b67d054b-95ea-477b-93c7-f03c5ab68fe0-attachment.txt'\n\n    @hookimpl\n    def report_attached_data(self, body, file_name):\n        destination = self._report_dir / file_name\n        with open(destination, 'wb') as attached_file:\n            if isinstance(body, str):\n                attached_file.write(body.encode('utf-8'))\n            else:\n>               attached_file.write(body)\nE               TypeError: a bytes-like object is required, not 'dict'\n\n.venv\\Lib\\site-packages\\allure_commons\\logger.py:48: TypeError"}, "description": "验证book a flight to paris指令返回预期的不支持响应", "steps": [{"name": "执行命令: book a flight to paris", "status": "passed", "steps": [{"name": "执行命令: book a flight to paris", "status": "passed", "start": 1754494955563, "stop": 1754494983528}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e2e9d4a4-58ed-413b-b671-6485e01b064c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4096bfa0-123a-4c51-9aa2-b44b9359c6b5-attachment.png", "type": "image/png"}], "start": 1754494983528, "stop": 1754494983737}], "start": 1754494955563, "stop": 1754494983737}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1754494983737, "stop": 1754494983738}, {"name": "记录测试结果", "status": "broken", "statusDetails": {"message": "TypeError: a bytes-like object is required, not 'dict'\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\dialogue\\test_book_a_flight_to_paris.py\", line 43, in test_book_a_flight_to_paris\n    self.attach_test_summary(summary)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 846, in attach_test_summary\n    allure.attach(summary, name=\"测试总结\", attachment_type=allure.attachment_type.TEXT)\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 210, in __call__\n    plugin_manager.hook.attach_data(body=body, name=name, attachment_type=attachment_type, extension=extension)\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_hooks.py\", line 512, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 167, in _multicall\n    raise exception\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 121, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\allure_pytest\\listener.py\", line 252, in attach_data\n    self.allure_logger.attach_data(uuid4(), body, name=name, attachment_type=attachment_type, extension=extension)\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\allure_commons\\reporter.py\", line 165, in attach_data\n    plugin_manager.hook.report_attached_data(body=body, file_name=file_name)\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_hooks.py\", line 512, in __call__\n    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_manager.py\", line 120, in _hookexec\n    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 167, in _multicall\n    raise exception\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 121, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\.venv\\Lib\\site-packages\\allure_commons\\logger.py\", line 48, in report_attached_data\n    attached_file.write(body)\n"}, "attachments": [{"name": "测试总结", "source": "b67d054b-95ea-477b-93c7-f03c5ab68fe0-attachment.txt", "type": "text/plain"}], "start": 1754494983738, "stop": 1754494983738}], "attachments": [{"name": "stdout", "source": "2b70986e-65ed-45b5-b11f-beaffca508d2-attachment.txt", "type": "text/plain"}], "start": 1754494955563, "stop": 1754494983740, "uuid": "6cadc19e-1fce-4c32-b364-bf65f3aa1789", "historyId": "7b1fce8b7d3ff59dca969b439bb82f75", "testCaseId": "7b1fce8b7d3ff59dca969b439bb82f75", "fullName": "testcases.test_ella.dialogue.test_book_a_flight_to_paris.TestEllaBookFlightParis#test_book_a_flight_to_paris", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_book_a_flight_to_paris"}, {"name": "subSuite", "value": "TestEllaBookFlightParis"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_book_a_flight_to_paris"}]}