{"name": "测试open contact命令 - 简洁版本", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open app", "status": "passed", "steps": [{"name": "执行命令: open app", "status": "passed", "start": 1754495738950, "stop": 1754495752948}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a9343528-71f1-4b18-92fb-e71c652de59a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8da7d17b-bbe1-49b4-b3c2-fc54ad35d0aa-attachment.png", "type": "image/png"}], "start": 1754495752948, "stop": 1754495753166}], "start": 1754495738950, "stop": 1754495753166}, {"name": "验证响应包含Done", "status": "passed", "start": 1754495753166, "stop": 1754495753168}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a2b9f97d-468c-4760-b93d-343da6570ccb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "bbf38084-d2ca-4d20-a984-3186ceb3987f-attachment.png", "type": "image/png"}], "start": 1754495753168, "stop": 1754495753335}], "attachments": [{"name": "stdout", "source": "8cbafb7f-fbb6-4bc9-b67d-4707ddf61f69-attachment.txt", "type": "text/plain"}], "start": 1754495738950, "stop": 1754495753336, "uuid": "f4dc245c-23c1-435d-b104-ae37cd00702a", "historyId": "f5346ff0fa4cb76e4b6ceea6116693ee", "testCaseId": "f5346ff0fa4cb76e4b6ceea6116693ee", "fullName": "testcases.test_ella.dialogue.test_open_app.TestEllaCommandConcise#test_open_app", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_open_app"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_open_app"}]}