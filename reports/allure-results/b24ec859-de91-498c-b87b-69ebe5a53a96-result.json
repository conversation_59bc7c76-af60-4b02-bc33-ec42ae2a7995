{"name": "测试check my balance of sim1返回正确的不支持响应", "status": "passed", "description": "验证check my balance of sim1指令返回预期的不支持响应", "steps": [{"name": "执行命令: check my balance of sim1", "status": "passed", "steps": [{"name": "执行命令: check my balance of sim1", "status": "passed", "start": 1754499327301, "stop": 1754499340637}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0591f62a-55b3-4937-a355-a8835d45e6c5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "072c1224-2daa-4194-ab68-fcf40d11c316-attachment.png", "type": "image/png"}], "start": 1754499340637, "stop": 1754499340864}], "start": 1754499327301, "stop": 1754499340864}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1754499340864, "stop": 1754499340865}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "44badedd-20cb-4166-908a-74042e7d89da-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b8e10994-d960-41df-976d-976931af393b-attachment.png", "type": "image/png"}], "start": 1754499340865, "stop": 1754499341033}], "attachments": [{"name": "stdout", "source": "92d0e071-65d8-46fa-867f-a1b80d0b2a85-attachment.txt", "type": "text/plain"}], "start": 1754499327300, "stop": 1754499341033, "uuid": "53c33e52-cd9d-4f5d-a18d-7492e7c82df5", "historyId": "8bcc4c0c2b314e79a7177168f7d787b8", "testCaseId": "8bcc4c0c2b314e79a7177168f7d787b8", "fullName": "testcases.test_ella.unsupported_commands.test_check_my_balance_of_sim.TestEllaCheckMyBalanceSim#test_check_my_balance_of_sim", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_my_balance_of_sim"}, {"name": "subSuite", "value": "TestEllaCheckMyBalanceSim"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_my_balance_of_sim"}]}