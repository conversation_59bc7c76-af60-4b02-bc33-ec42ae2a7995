{"name": "测试say hello能正常执行", "status": "passed", "description": "say hello", "steps": [{"name": "执行命令: say hello", "status": "passed", "steps": [{"name": "执行命令: say hello", "status": "passed", "start": 1754495830925, "stop": 1754495846669}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ec622017-9b5a-4840-822c-323184ddfbfa-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8651f95b-faac-4b3b-a593-184b6a2c37aa-attachment.png", "type": "image/png"}], "start": 1754495846669, "stop": 1754495846917}], "start": 1754495830925, "stop": 1754495846917}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495846917, "stop": 1754495846918}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dc2529bc-5664-4234-aa7a-7771938988c0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5e0cc1ef-da7d-43be-8df3-b9d5bf8dd85a-attachment.png", "type": "image/png"}], "start": 1754495846918, "stop": 1754495847085}], "attachments": [{"name": "stdout", "source": "d264cfa5-6050-406c-b774-1c015dae387c-attachment.txt", "type": "text/plain"}], "start": 1754495830924, "stop": 1754495847085, "uuid": "b7bcb2da-c70a-4519-8edc-b636d1a61191", "historyId": "f416bca94fc67372d77ac2dd1f3e4517", "testCaseId": "f416bca94fc67372d77ac2dd1f3e4517", "fullName": "testcases.test_ella.dialogue.test_say_hello.TestEllaSayHello#test_say_hello", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_say_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_say_hello"}]}