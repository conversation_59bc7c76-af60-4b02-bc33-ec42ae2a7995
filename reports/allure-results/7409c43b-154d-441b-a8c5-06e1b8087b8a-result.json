{"name": "测试take notes on how to build a treehouse能正常执行", "status": "passed", "description": "take notes on how to build a treehouse", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take notes on how to build a treehouse", "status": "passed", "start": 1754496231869, "stop": 1754496245964}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ca53d3ef-4a7d-411a-acc3-5195909da551-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fd36e279-c1bd-4fff-9f64-97a4b951fc7a-attachment.png", "type": "image/png"}], "start": 1754496245964, "stop": 1754496246183}], "start": 1754496231869, "stop": 1754496246184}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496246184, "stop": 1754496246186}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5dfe5521-bede-4473-99f7-f60430905b6d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7c2b118c-acaf-4eac-96b4-04a890e4ed44-attachment.png", "type": "image/png"}], "start": 1754496246186, "stop": 1754496246381}], "attachments": [{"name": "stdout", "source": "3e523cde-49d4-4988-bf65-ace40f885fe0-attachment.txt", "type": "text/plain"}], "start": 1754496231869, "stop": 1754496246381, "uuid": "52558569-31b8-4a5a-b5b2-4235b8655a0b", "historyId": "772728b3468560788490a3673352724d", "testCaseId": "772728b3468560788490a3673352724d", "fullName": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse.TestEllaTakeNotesHowBuildTreehouse#test_take_notes_on_how_to_build_a_treehouse", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_notes_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNotesHowBuildTreehouse"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_notes_on_how_to_build_a_treehouse"}]}