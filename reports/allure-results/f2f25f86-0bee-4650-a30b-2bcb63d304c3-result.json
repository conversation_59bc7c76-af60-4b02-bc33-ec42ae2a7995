{"name": "测试play political news", "status": "passed", "description": "测试play political news指令", "steps": [{"name": "执行命令: play political news", "status": "passed", "steps": [{"name": "执行命令: play political news", "status": "passed", "start": 1754495798651, "stop": 1754495816310}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "59f95a45-05ef-4030-9c77-5778c137630c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "dd1db6da-04df-4e77-93ed-c4eafc897cd7-attachment.png", "type": "image/png"}], "start": 1754495816311, "stop": 1754495816490}], "start": 1754495798651, "stop": 1754495816490}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495816491, "stop": 1754495816492}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "61bda034-224b-4a51-bc7d-535b819f54af-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "44fd2bf1-41f1-4447-bb68-887eb176da45-attachment.png", "type": "image/png"}], "start": 1754495816492, "stop": 1754495816661}], "attachments": [{"name": "stdout", "source": "a0d5877e-224e-4ccd-b2e1-4e9894b09a33-attachment.txt", "type": "text/plain"}], "start": 1754495798651, "stop": 1754495816661, "uuid": "d625459e-3730-4a80-b983-23873c196bd1", "historyId": "1bf9bd9c91ab7da6f818ff587cfff7da", "testCaseId": "1bf9bd9c91ab7da6f818ff587cfff7da", "fullName": "testcases.test_ella.dialogue.test_play_political_news.TestEllaOpenPlayPoliticalNews#test_play_political_news", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_political_news"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_political_news"}]}