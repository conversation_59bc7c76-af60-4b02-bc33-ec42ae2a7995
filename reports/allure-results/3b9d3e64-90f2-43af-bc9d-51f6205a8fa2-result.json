{"name": "测试switch to default mode能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_switch_to_default_mode.TestEllaSwitchToDefaultMode object at 0x000001BE0D1BAC10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0F3CAB50>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_switch_to_default_mode(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含在期望中\"):\n            expected_text = self.expected_text\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\system_coupling\\test_switch_to_default_mode.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.system_coupling.test_switch_to_default_mode.TestEllaSwitchToDefaultMode object at 0x000001BE0D1BAC10>, expected_text = ['Done']\nresponse_text = ['switch to default mode', '已执行!', '', '', '[com.android.settings页面内容] Multiple Users | Allow Multiple Users | Users |... new users. Each user has a personal space on your phone for custom Home screens, accounts, apps, settings, and more.']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['Done']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:923: AssertionError"}, "description": "switch to default mode", "steps": [{"name": "执行命令: switch to default mode", "status": "passed", "steps": [{"name": "执行命令: switch to default mode", "status": "passed", "start": 1754497961019, "stop": 1754497978683}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "931705be-5c2a-4824-8502-7a1a6ebe139a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1ff4254d-0f20-45b6-88c6-5972a4bbe2d4-attachment.png", "type": "image/png"}], "start": 1754497978683, "stop": 1754497978924}], "start": 1754497961019, "stop": 1754497978925}, {"name": "验证响应包含在期望中", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Done']\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_switch_to_default_mode.py\", line 32, in test_switch_to_default_mode\n    result = self.verify_expected_in_response(expected_text, response_text)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 923, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n"}, "start": 1754497978925, "stop": 1754497978926}], "attachments": [{"name": "stdout", "source": "585e4ea6-48ba-4048-88a5-538278c1a8b5-attachment.txt", "type": "text/plain"}], "start": 1754497961018, "stop": 1754497978927, "uuid": "f11ce2be-f782-4564-be05-c8a689ae3651", "historyId": "d2d9aa669417404f06e84a7a4387c55a", "testCaseId": "d2d9aa669417404f06e84a7a4387c55a", "fullName": "testcases.test_ella.system_coupling.test_switch_to_default_mode.TestEllaSwitchToDefaultMode#test_switch_to_default_mode", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_switch_to_default_mode"}, {"name": "subSuite", "value": "TestEllaSwitchToDefaultMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_switch_to_default_mode"}]}