{"name": "测试i want to make a call能正常执行", "status": "passed", "description": "i want to make a call", "steps": [{"name": "执行命令: i want to make a call", "status": "passed", "steps": [{"name": "执行命令: i want to make a call", "status": "passed", "start": 1754495602497, "stop": 1754495626079}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0db197ad-c434-40fa-9be3-572dde0a09e1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e0b2456f-b662-4aab-8e01-f7a4cf0dfe5c-attachment.png", "type": "image/png"}], "start": 1754495626079, "stop": 1754495626322}], "start": 1754495602497, "stop": 1754495626323}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495626323, "stop": 1754495626324}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1e03c6ff-1a63-4994-8409-603eac093d01-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3d0a8150-1fa6-425e-81e1-7b89affa2d6a-attachment.png", "type": "image/png"}], "start": 1754495626324, "stop": 1754495626505}], "attachments": [{"name": "stdout", "source": "91217f35-de41-4831-a0e9-01f278c44a1b-attachment.txt", "type": "text/plain"}], "start": 1754495602497, "stop": 1754495626505, "uuid": "6c937e5e-a198-4543-acdd-3d3e7cbe30a1", "historyId": "f4da532f5d62abff197a05947efc027a", "testCaseId": "f4da532f5d62abff197a05947efc027a", "fullName": "testcases.test_ella.dialogue.test_i_want_to_make_a_call.TestEllaIWantMakeCall#test_i_want_to_make_a_call", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_make_a_call"}, {"name": "subSuite", "value": "TestEllaIWantMakeCall"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_make_a_call"}]}