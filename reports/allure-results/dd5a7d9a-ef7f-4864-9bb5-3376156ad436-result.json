{"name": "测试play afro strut", "status": "passed", "description": "测试play afro strut指令", "steps": [{"name": "执行命令: play afro strut", "status": "passed", "steps": [{"name": "执行命令: play afro strut", "status": "passed", "start": 1754494487852, "stop": 1754494516307}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2b046b60-c530-4410-9344-3353d3818973-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b63898bd-6142-430b-bd19-3b18ea5fa43b-attachment.png", "type": "image/png"}], "start": 1754494516307, "stop": 1754494516519}], "start": 1754494487852, "stop": 1754494516519}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754494516519, "stop": 1754494516521}, {"name": "验证play afro strut已打开", "status": "passed", "start": 1754494516521, "stop": 1754494516521}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "cb621d07-852a-4257-8f5e-f850e82537e3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b88fa574-dc9d-4123-a257-b33b7326a81e-attachment.png", "type": "image/png"}], "start": 1754494516521, "stop": 1754494516714}], "attachments": [{"name": "stdout", "source": "666f96ed-c1ea-4b77-8ade-719e9bf5b772-attachment.txt", "type": "text/plain"}], "start": 1754494487851, "stop": 1754494516715, "uuid": "79a40c57-b5e4-4be4-bb8e-6a76c22c3d84", "historyId": "ffb0a39af30beaa699329479ec564117", "testCaseId": "ffb0a39af30beaa699329479ec564117", "fullName": "testcases.test_ella.component_coupling.test_play_afro_strut.TestEllaOpenPlayAfroStrut#test_play_afro_strut", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_afro_strut"}, {"name": "subSuite", "value": "TestEllaOpenPlayAfroStrut"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_afro_strut"}]}