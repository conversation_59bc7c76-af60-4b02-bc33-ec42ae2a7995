{"name": "测试introduce yourself能正常执行", "status": "passed", "description": "introduce yourself", "steps": [{"name": "执行命令: introduce yourself", "status": "passed", "steps": [{"name": "执行命令: introduce yourself", "status": "passed", "start": 1754495671614, "stop": 1754495687358}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "270d88dd-9d8f-4477-ae2e-b312ba9485c4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "56281ea4-cdb5-4b90-9836-9200916ec92d-attachment.png", "type": "image/png"}], "start": 1754495687359, "stop": 1754495687557}], "start": 1754495671614, "stop": 1754495687558}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495687558, "stop": 1754495687559}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "59ee32da-c24f-4002-8c9e-e90486ab16c5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9c634648-2a28-428c-9e51-253569ccea8f-attachment.png", "type": "image/png"}], "start": 1754495687559, "stop": 1754495687723}], "attachments": [{"name": "stdout", "source": "6883cb38-99c3-49e6-8fc3-a7fb186a99b6-attachment.txt", "type": "text/plain"}], "start": 1754495671614, "stop": 1754495687725, "uuid": "1612bdaf-bd9c-4495-84f3-592496611dd8", "historyId": "a19924fb0a564cf26596907610c0f678", "testCaseId": "a19924fb0a564cf26596907610c0f678", "fullName": "testcases.test_ella.dialogue.test_introduce_yourself.TestEllaIntroduceYourself#test_introduce_yourself", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_introduce_yourself"}, {"name": "subSuite", "value": "TestEllaIntroduceYourself"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_introduce_yourself"}]}