{"name": "测试what is apec?能正常执行", "status": "passed", "description": "what is apec?", "steps": [{"name": "执行命令: what is apec?", "status": "passed", "steps": [{"name": "执行命令: what is apec?", "status": "passed", "start": 1754496326566, "stop": 1754496342694}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d06eb837-a742-4cf2-a092-1bf6b3318169-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3fd0456d-5906-4d68-b7cd-7e26d36bd809-attachment.png", "type": "image/png"}], "start": 1754496342694, "stop": 1754496342885}], "start": 1754496326566, "stop": 1754496342887}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496342887, "stop": 1754496342889}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3bf3d6cf-5803-4196-a97f-a7f8860eab80-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b4b23bad-d4e4-40dc-8557-377d905114c9-attachment.png", "type": "image/png"}], "start": 1754496342889, "stop": 1754496343120}], "attachments": [{"name": "stdout", "source": "d82d1467-151b-4816-a548-9cd1ba9b2340-attachment.txt", "type": "text/plain"}], "start": 1754496326566, "stop": 1754496343120, "uuid": "086bb828-0e8b-4cb1-a3fc-bc620478ae3c", "historyId": "0c4bd81bf0dbac094265e3ac47550bbd", "testCaseId": "0c4bd81bf0dbac094265e3ac47550bbd", "fullName": "testcases.test_ella.dialogue.test_what_is_apec.TestEllaWhatIsApec#test_what_is_apec", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_what_is_apec"}, {"name": "subSuite", "value": "TestEllaWhatIsApec"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_what_is_apec"}]}