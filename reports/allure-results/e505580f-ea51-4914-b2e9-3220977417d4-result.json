{"name": "测试open dialer能正常执行", "status": "passed", "description": "open dialer", "steps": [{"name": "执行命令: open dialer", "status": "passed", "steps": [{"name": "执行命令: open dialer", "status": "passed", "start": 1754494300813, "stop": 1754494323724}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a033afe2-62e1-4fad-ad24-7494e1c0bf9e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "609c017f-e0b0-4055-82a9-893a5196a224-attachment.png", "type": "image/png"}], "start": 1754494323724, "stop": 1754494323915}], "start": 1754494300813, "stop": 1754494323916}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1754494323916, "stop": 1754494323917}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "af6da7f2-ca1d-4de0-9a89-04ab6f2869cd-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e91a0f67-9f60-423f-96a4-b7dd73016edb-attachment.png", "type": "image/png"}], "start": 1754494323917, "stop": 1754494324098}], "attachments": [{"name": "stdout", "source": "7ca3a4b9-0c73-45ff-8c82-48ab1d225ac3-attachment.txt", "type": "text/plain"}], "start": 1754494300813, "stop": 1754494324098, "uuid": "71b8c002-552e-4115-ac08-d33a76436fd7", "historyId": "936ae2bf6db744b69d4acf28b22f7646", "testCaseId": "936ae2bf6db744b69d4acf28b22f7646", "fullName": "testcases.test_ella.component_coupling.test_open_dialer.TestEllaCommandConcise#test_open_dialer", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_dialer"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_dialer"}]}