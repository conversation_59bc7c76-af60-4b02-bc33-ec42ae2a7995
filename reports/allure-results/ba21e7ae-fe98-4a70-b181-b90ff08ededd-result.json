{"name": "测试open contact命令", "status": "passed", "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open phone", "status": "passed", "steps": [{"name": "执行命令: open phone", "status": "passed", "start": 1754494392826, "stop": 1754494415921}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "59f0a0c4-53e9-4379-894a-1051440c6901-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b1c8f5f8-fe27-419c-afb0-36f6a8fc9161-attachment.png", "type": "image/png"}], "start": 1754494415921, "stop": 1754494416130}], "start": 1754494392826, "stop": 1754494416130}, {"name": "验证响应包含Done", "status": "passed", "start": 1754494416130, "stop": 1754494416132}, {"name": "验证Dalier应用已打开", "status": "passed", "start": 1754494416132, "stop": 1754494416132}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d7cc7456-561c-4508-84ae-ff7b78900535-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4002d8ed-addf-433e-ad45-cc28c9b917c1-attachment.png", "type": "image/png"}], "start": 1754494416132, "stop": 1754494416319}], "attachments": [{"name": "stdout", "source": "30ee6382-799e-499c-a2ce-ec4c9ec2a1cd-attachment.txt", "type": "text/plain"}], "start": 1754494392826, "stop": 1754494416320, "uuid": "c2453b6d-a5f1-4651-8168-4ee84d068c7c", "historyId": "acdb323f998d6127fbebbc545f6e8a59", "testCaseId": "acdb323f998d6127fbebbc545f6e8a59", "fullName": "testcases.test_ella.component_coupling.test_open_phone.TestEllaContactCommandConcise#test_open_phone", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "联系人控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_phone"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_phone"}]}