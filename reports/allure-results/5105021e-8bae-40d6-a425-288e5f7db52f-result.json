{"name": "测试help me take a long screenshot能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 文件不存在！\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot object at 0x000001BE0D56E9D0>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0DD853D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_help_me_take_a_long_screenshot(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False, verify_files=True\n            )\n    \n        with allure.step(f\"验证文件存在\"):\n>           assert files_status, f\"文件不存在！\"\nE           AssertionError: 文件不存在！\nE           assert False\n\ntestcases\\test_ella\\system_coupling\\test_help_me_take_a_long_screenshot.py:32: AssertionError"}, "description": "help me take a long screenshot", "steps": [{"name": "执行命令: help me take a long screenshot", "status": "passed", "steps": [{"name": "执行命令: help me take a long screenshot", "status": "passed", "start": 1754497115185, "stop": 1754497133998}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e7f3de3f-c179-4600-8887-1244588289be-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "11575f26-0f0b-4174-98ba-6a17498e0bae-attachment.png", "type": "image/png"}], "start": 1754497133998, "stop": 1754497134242}], "start": 1754497115185, "stop": 1754497134242}, {"name": "验证文件存在", "status": "failed", "statusDetails": {"message": "AssertionError: 文件不存在！\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_help_me_take_a_long_screenshot.py\", line 32, in test_help_me_take_a_long_screenshot\n    assert files_status, f\"文件不存在！\"\n"}, "start": 1754497134242, "stop": 1754497134242}], "attachments": [{"name": "stdout", "source": "ae8639d4-5388-4ad1-ba51-356c8b68e0d6-attachment.txt", "type": "text/plain"}], "start": 1754497115185, "stop": 1754497134243, "uuid": "4b9b948a-7bf6-4d8f-8a1c-34c2ebc420f9", "historyId": "fe3d09fe0bad56e7804ef2f5ea49d283", "testCaseId": "fe3d09fe0bad56e7804ef2f5ea49d283", "fullName": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot.TestEllaHelpMeTakeLongScreenshot#test_help_me_take_a_long_screenshot", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_help_me_take_a_long_screenshot"}, {"name": "subSuite", "value": "TestEllaHelpMeTakeLongScreenshot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_help_me_take_a_long_screenshot"}]}