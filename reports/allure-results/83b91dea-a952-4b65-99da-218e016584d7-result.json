{"name": "测试cannot login in google email box能正常执行", "status": "passed", "description": "cannot login in google email box", "steps": [{"name": "执行命令: cannot login in google email box", "status": "passed", "steps": [{"name": "执行命令: cannot login in google email box", "status": "passed", "start": 1754495066602, "stop": 1754495080453}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "da549c25-9333-4126-8a36-4d12ddc15f5d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "64b2c984-e6f5-4f42-8282-2d3cc5b82e0a-attachment.png", "type": "image/png"}], "start": 1754495080453, "stop": 1754495080614}], "start": 1754495066602, "stop": 1754495080614}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495080614, "stop": 1754495080614}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f0729c34-3005-42cf-9833-a7eeb0a9b034-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a4fe8fd4-9272-4fea-83d8-afa4d8ab1af8-attachment.png", "type": "image/png"}], "start": 1754495080614, "stop": 1754495080781}], "attachments": [{"name": "stdout", "source": "7b1296b3-41d8-455c-bbeb-235769c9f69d-attachment.txt", "type": "text/plain"}], "start": 1754495066602, "stop": 1754495080781, "uuid": "f2e3f2ed-d3e2-459a-826b-c93e1567b8b3", "historyId": "6748f677dff755a4da95c520c3f05506", "testCaseId": "6748f677dff755a4da95c520c3f05506", "fullName": "testcases.test_ella.dialogue.test_cannot_login_in_google_email_box.TestEllaCannotLoginGoogleEmailBox#test_cannot_login_in_google_email_box", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_cannot_login_in_google_email_box"}, {"name": "subSuite", "value": "TestEllaCannotLoginGoogleEmailBox"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_cannot_login_in_google_email_box"}]}