{"name": "测试open camera", "status": "passed", "description": "测试open camera指令", "steps": [{"name": "执行命令: open camera", "status": "passed", "steps": [{"name": "执行命令: open camera", "status": "passed", "start": 1754496734110, "stop": 1754496752494}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "02062412-f4c5-40d1-a6e1-8c91eef8ec68-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e69acdec-0364-41c7-97fe-395c5d1dcd6d-attachment.png", "type": "image/png"}], "start": 1754496752494, "stop": 1754496752668}], "start": 1754496734110, "stop": 1754496752669}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496752669, "stop": 1754496752670}, {"name": "验证camera已打开", "status": "passed", "start": 1754496752670, "stop": 1754496752670}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b220fc7f-c41f-4420-bd53-6bf0f6cdce4b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6581ecc7-d141-4381-b1b2-6fb01f379c74-attachment.png", "type": "image/png"}], "start": 1754496752670, "stop": 1754496752864}], "attachments": [{"name": "stdout", "source": "55d6c107-5bc0-46ab-91f8-b18acf89014c-attachment.txt", "type": "text/plain"}], "start": 1754496734110, "stop": 1754496752865, "uuid": "516ae999-4491-475c-afdf-ff56cc6180d0", "historyId": "cf4d81285cd0bfb49bf81dabe5eaa538", "testCaseId": "cf4d81285cd0bfb49bf81dabe5eaa538", "fullName": "testcases.test_ella.open_app.test_open_camera.TestEllaOpenCamera#test_open_camera", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_camera"}, {"name": "subSuite", "value": "TestEllaOpenCamera"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_camera"}]}