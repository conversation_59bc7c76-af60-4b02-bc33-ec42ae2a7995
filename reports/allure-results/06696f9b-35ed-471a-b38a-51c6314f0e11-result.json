{"name": "测试take a selfie能正常执行", "status": "passed", "description": "take a selfie", "steps": [{"name": "执行命令: take a selfie", "status": "passed", "steps": [{"name": "执行命令: take a selfie", "status": "passed", "start": 1754498245291, "stop": 1754498274693}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c5889349-6482-4d1c-89a8-5f0505b9dbd5-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "faf615a2-a890-46de-bbdf-8cb3ecd4ff89-attachment.png", "type": "image/png"}], "start": 1754498274693, "stop": 1754498274855}], "start": 1754498245291, "stop": 1754498274856}, {"name": "验证应用已打开", "status": "passed", "start": 1754498274856, "stop": 1754498274856}, {"name": "验证文件存在", "status": "passed", "start": 1754498274856, "stop": 1754498274856}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4ae02b52-8d8b-458c-a5b4-05e53a221d8b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5340ae4f-2d4b-4c60-9784-6d87fb050982-attachment.png", "type": "image/png"}], "start": 1754498274856, "stop": 1754498275015}], "attachments": [{"name": "stdout", "source": "5873904c-dd1b-4ff8-b34d-00e4c221450c-attachment.txt", "type": "text/plain"}], "start": 1754498245291, "stop": 1754498275015, "uuid": "4f5816b7-db3f-4cfd-aa86-91137fb37e43", "historyId": "6c46a38570672e3c21f37ef82690d639", "testCaseId": "6c46a38570672e3c21f37ef82690d639", "fullName": "testcases.test_ella.system_coupling.test_take_a_selfie.TestEllaTakeSelfie#test_take_a_selfie", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_take_a_selfie"}, {"name": "subSuite", "value": "TestEllaTakeSelfie"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_take_a_selfie"}]}