{"name": "测试open folax能正常执行", "status": "passed", "description": "open folax", "steps": [{"name": "执行命令: open folax", "status": "passed", "steps": [{"name": "执行命令: open folax", "status": "passed", "start": 1754494365181, "stop": 1754494378153}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9281857d-d85c-42c7-ab3c-7b2fe801aa13-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "936a0fcd-4bb3-45d7-b02e-e36a22b09698-attachment.png", "type": "image/png"}], "start": 1754494378153, "stop": 1754494378347}], "start": 1754494365181, "stop": 1754494378348}, {"name": "验证响应包含在期望中", "status": "passed", "start": 1754494378348, "stop": 1754494378349}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e290da0b-cae0-444a-b810-b00601d735ee-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d71a9e74-88bc-4bf2-9006-302eda1f4bc6-attachment.png", "type": "image/png"}], "start": 1754494378349, "stop": 1754494378552}], "attachments": [{"name": "stdout", "source": "4fe8e72e-9146-4e92-b106-89e19f341922-attachment.txt", "type": "text/plain"}], "start": 1754494365180, "stop": 1754494378553, "uuid": "7107db0c-ba92-4b48-95a5-988e450d244b", "historyId": "9da64d3434f91a12d693ed9c71b62e87", "testCaseId": "9da64d3434f91a12d693ed9c71b62e87", "fullName": "testcases.test_ella.component_coupling.test_open_folax.TestEllaCommandConcise#test_open_folax", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_folax"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_folax"}]}