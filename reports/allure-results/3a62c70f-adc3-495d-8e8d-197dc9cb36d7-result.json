{"name": "测试Add the images and text on the screen to the note", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note.TestEllaOpenPlayPoliticalNews object at 0x000001BE0D2AB710>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0F483F10>\n\n    @allure.title(\"测试Add the images and text on the screen to the note\")\n    @allure.description(\"测试Add the images and text on the screen to the note指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_play_political_news(self, ella_app):\n        \"\"\"测试Add the images and text on the screen to the note命令\"\"\"\n        command = \"Add the images and text on the screen to the note\"\n        app_name = 'settings'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = ['Done']\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\unsupported_commands\\test_Add_the_images_and_text_on_the_screen_to_the_note.py:30: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note.TestEllaOpenPlayPoliticalNews object at 0x000001BE0D2AB710>, expected_text = ['Done']\nresponse_text = ['Add the images and text on the screen to the note', '已执行!', '', '', '[com.android.settings页面内容] Digital Assistant Ap...he screen you’re viewing. Some apps support both launcher and voice input services to give you integrated assistance.']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['Done']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:923: AssertionError"}, "description": "测试Add the images and text on the screen to the note指令", "steps": [{"name": "执行命令: Add the images and text on the screen to the note", "status": "passed", "steps": [{"name": "执行命令: Add the images and text on the screen to the note", "status": "passed", "start": 1754499178446, "stop": 1754499195885}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fb977844-c124-4c4c-93c5-d40fcd4399ad-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b7664e61-40df-446f-a944-183d30163c6b-attachment.png", "type": "image/png"}], "start": 1754499195885, "stop": 1754499196085}], "start": 1754499178446, "stop": 1754499196085}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Done']\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_Add_the_images_and_text_on_the_screen_to_the_note.py\", line 30, in test_play_political_news\n    result = self.verify_expected_in_response(expected_text, response_text)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 923, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n"}, "start": 1754499196085, "stop": 1754499196087}], "attachments": [{"name": "stdout", "source": "0510a483-2a3f-4480-9a22-0c0d79922447-attachment.txt", "type": "text/plain"}], "start": 1754499178446, "stop": 1754499196088, "uuid": "f6d4ad0f-5340-4910-bbbe-35eed57fecac", "historyId": "27be46b03402511b8980a7b755c58d58", "testCaseId": "27be46b03402511b8980a7b755c58d58", "fullName": "testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note.TestEllaOpenPlayPoliticalNews#test_play_political_news", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_Add_the_images_and_text_on_the_screen_to_the_note"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note"}]}