{"name": "测试how's the weather today in shanghai能正常执行", "status": "passed", "description": "how's the weather today in shanghai", "steps": [{"name": "执行命令: how's the weather today in shanghai", "status": "passed", "steps": [{"name": "执行命令: how's the weather today in shanghai", "status": "passed", "start": 1754495475027, "stop": 1754495495790}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "901887d5-2691-4b4b-b8dd-3af0358d8ef0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "071a6a37-cf50-46de-85e6-3ddd06abb56c-attachment.png", "type": "image/png"}], "start": 1754495495790, "stop": 1754495496002}], "start": 1754495475027, "stop": 1754495496003}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495496003, "stop": 1754495496004}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "*************-430c-8672-7c9d2a8d91e8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f88948a5-4478-4adf-9a3f-1cd3bd087da4-attachment.png", "type": "image/png"}], "start": 1754495496004, "stop": 1754495496224}], "attachments": [{"name": "stdout", "source": "314b1f48-b733-4c5f-afde-a2a75f7d2bc1-attachment.txt", "type": "text/plain"}], "start": 1754495475027, "stop": 1754495496224, "uuid": "a1f9ad26-9dd9-45f7-8d5f-4c36df1c1448", "historyId": "bd4d204a449f3a4013b03af9a9101446", "testCaseId": "bd4d204a449f3a4013b03af9a9101446", "fullName": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai.TestEllaWhatSWeatherLikeShanghaiToday#test_what_s_the_weather_like_in_shanghai_today", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_s_the_weather_today_in_shanghai"}, {"name": "subSuite", "value": "TestEllaWhatSWeatherLikeShanghaiToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_s_the_weather_today_in_shanghai"}]}