{"name": "测试play news", "status": "passed", "description": "测试play news指令", "steps": [{"name": "执行命令: play news", "status": "passed", "steps": [{"name": "执行命令: play news", "status": "passed", "start": 1754495767309, "stop": 1754495784453}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d8bbc208-a052-4b3b-bd9e-8b2c28749078-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b0e302d5-7faa-4a1c-a45a-e67313935600-attachment.png", "type": "image/png"}], "start": 1754495784453, "stop": 1754495784649}], "start": 1754495767309, "stop": 1754495784649}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495784649, "stop": 1754495784650}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "44794f55-1d2a-455d-be6b-88c8718a4a48-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "df6824a8-7359-4e66-9229-2badcae54c91-attachment.png", "type": "image/png"}], "start": 1754495784650, "stop": 1754495784832}], "attachments": [{"name": "stdout", "source": "d65612ad-241d-4db5-ab4c-1d07f1277bcc-attachment.txt", "type": "text/plain"}], "start": 1754495767308, "stop": 1754495784832, "uuid": "f2cddce4-1a31-457d-9b34-40d323f7f7e0", "historyId": "fee3033814a8b17ff8c8abe6bbcdc839", "testCaseId": "fee3033814a8b17ff8c8abe6bbcdc839", "fullName": "testcases.test_ella.dialogue.test_play_news.TestEllaOpenPlayNews#test_play_news", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_play_news"}, {"name": "subSuite", "value": "TestEllaOpenPlayNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_play_news"}]}