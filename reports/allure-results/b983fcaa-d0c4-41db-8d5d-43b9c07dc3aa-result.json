{"name": "测试open calculator", "status": "failed", "statusDetails": {"message": "AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nassert None", "trace": "self = <test_open_calculator.TestEllaOpenCalculator object at 0x000001BE0D51EE10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0DE6C090>\n\n    @allure.title(\"测试open calculator\")\n    @allure.description(\"测试open calculator指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_calculator(self, ella_app):\n        \"\"\"测试open calculator命令\"\"\"\n        command = \"open calculator\"\n        expected_text = ['Done']\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证calculator已打开\"):\n>           assert final_status, f\"calculator: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nE           assert None\n\ntestcases\\test_ella\\open_app\\test_open_calculator.py:33: AssertionError"}, "description": "测试open calculator指令", "steps": [{"name": "执行命令: open calculator", "status": "passed", "steps": [{"name": "执行命令: open calculator", "status": "passed", "start": 1754496702706, "stop": 1754496719498}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "64e9787a-ccb6-478a-9a7b-147d24acff6c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b1362dba-1c85-44f0-90a3-321482007bc3-attachment.png", "type": "image/png"}], "start": 1754496719498, "stop": 1754496719699}], "start": 1754496702706, "stop": 1754496719700}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496719700, "stop": 1754496719701}, {"name": "验证calculator已打开", "status": "failed", "statusDetails": {"message": "AssertionError: calculator: 初始=None, 最终=None, 响应='['open calculator', 'Done!', '', '', '[com.transsion.calculator页面] 未获取到文本内容']'\nassert None\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\open_app\\test_open_calculator.py\", line 33, in test_open_calculator\n    assert final_status, f\"calculator: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1754496719701, "stop": 1754496719701}], "attachments": [{"name": "stdout", "source": "e8358c81-3161-45f7-bfb5-9a291c18b76d-attachment.txt", "type": "text/plain"}], "start": 1754496702704, "stop": 1754496719702, "uuid": "8663a80f-f7e4-4fcc-90ad-6612fa325320", "historyId": "fead54f5f2ccb14942215ef4a8f481bf", "testCaseId": "fead54f5f2ccb14942215ef4a8f481bf", "fullName": "testcases.test_ella.open_app.test_open_calculator.TestEllaOpenCalculator#test_open_calculator", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.open_app"}, {"name": "suite", "value": "test_open_calculator"}, {"name": "subSuite", "value": "TestEllaOpenCalculator"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.open_app.test_open_calculator"}]}