{"name": "测试start run能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nassert None", "trace": "self = <testcases.test_ella.component_coupling.test_start_run.TestEllaStartRun object at 0x000001BE0D62F390>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0DB15150>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_start_run(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n        expected_text = self.expected_text\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=True, verify_files=False\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证应用已打开\"):\n>           assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nE           assert None\n\ntestcases\\test_ella\\component_coupling\\test_start_run.py:36: AssertionError"}, "description": "start run", "steps": [{"name": "执行命令: start run", "status": "passed", "steps": [{"name": "执行命令: start run", "status": "passed", "start": 1754494793058, "stop": 1754494817282}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4f65b78a-f961-4380-858e-8472fc4508a9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5266d884-e6e6-49f3-95b1-17d194db4985-attachment.png", "type": "image/png"}], "start": 1754494817282, "stop": 1754494817447}], "start": 1754494793058, "stop": 1754494817447}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754494817447, "stop": 1754494817448}, {"name": "验证应用已打开", "status": "failed", "statusDetails": {"message": "AssertionError: 初始=None, 最终=None, 响应='['start run', 'Done!', '', '', '[com.transsion.healthlife页面] 未获取到文本内容']'\nassert None\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\component_coupling\\test_start_run.py\", line 36, in test_start_run\n    assert  final_status, f\"初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1754494817448, "stop": 1754494817448}], "attachments": [{"name": "stdout", "source": "e1dfddd6-1d0b-4c08-b6f8-647155b82613-attachment.txt", "type": "text/plain"}], "start": 1754494793058, "stop": 1754494817449, "uuid": "c5a911dc-99f6-47bc-bd5d-c6e3934ea174", "historyId": "92553bffc13049b2d4fa1afe9cf89498", "testCaseId": "92553bffc13049b2d4fa1afe9cf89498", "fullName": "testcases.test_ella.component_coupling.test_start_run.TestEllaStartRun#test_start_run", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_start_run"}, {"name": "subSuite", "value": "TestEllaStartRun"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_start_run"}]}