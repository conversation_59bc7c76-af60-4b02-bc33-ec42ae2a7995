{"name": "测试find a restaurant near me能正常执行", "status": "passed", "description": "find a restaurant near me", "steps": [{"name": "执行命令: find a restaurant near me", "status": "passed", "steps": [{"name": "执行命令: find a restaurant near me", "status": "passed", "start": 1754498831265, "stop": 1754498853840}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0089b013-2bee-4fc2-8d16-06cc7aefec3b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "299f3969-c228-49d6-a8b3-b759ba672adf-attachment.png", "type": "image/png"}], "start": 1754498853840, "stop": 1754498854043}], "start": 1754498831265, "stop": 1754498854044}, {"name": "验证已打开", "status": "passed", "start": 1754498854044, "stop": 1754498854044}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dd245523-1fe4-4a7f-a619-b253bc0c2226-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6cf49305-8f39-46b3-af5d-a19390b8442c-attachment.png", "type": "image/png"}], "start": 1754498854044, "stop": 1754498854237}], "attachments": [{"name": "stdout", "source": "4cdb0560-a151-4224-8602-13277eddc3fa-attachment.txt", "type": "text/plain"}], "start": 1754498831265, "stop": 1754498854237, "uuid": "8edfa8dc-fb62-4a48-b3c5-7c8322e09ec4", "historyId": "918c52f1eb9803594ff76c724b43d5f8", "testCaseId": "918c52f1eb9803594ff76c724b43d5f8", "fullName": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me.TestEllaFindRestaurantNearMe#test_find_a_restaurant_near_me", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_find_a_restaurant_near_me"}, {"name": "subSuite", "value": "TestEllaFindRestaurantNearMe"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_find_a_restaurant_near_me"}]}