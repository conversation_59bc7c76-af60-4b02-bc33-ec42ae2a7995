{"name": "测试how is the weather today能正常执行", "status": "passed", "description": "how is the weather today", "steps": [{"name": "执行命令: how is the weather today", "status": "passed", "steps": [{"name": "执行命令: how is the weather today", "status": "passed", "start": 1754495376602, "stop": 1754495397153}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e4e74ce9-5a6b-4ece-9f6c-068b0d9aaa82-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "50b58c98-f4da-490a-850c-a659867488c1-attachment.png", "type": "image/png"}], "start": 1754495397153, "stop": 1754495397332}], "start": 1754495376602, "stop": 1754495397332}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495397332, "stop": 1754495397334}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ddf6f1d6-c080-405d-9881-6aab1ca54651-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a9b0b924-7fd3-4882-a972-d433ecf2e0eb-attachment.png", "type": "image/png"}], "start": 1754495397334, "stop": 1754495397514}], "attachments": [{"name": "stdout", "source": "ce42d0b4-b7ca-40e2-b564-e4488cb8cbb9-attachment.txt", "type": "text/plain"}], "start": 1754495376601, "stop": 1754495397515, "uuid": "cee02fc5-b6db-499e-910e-0a0d752afaa9", "historyId": "3004e41c81a7ebd857f79d043aaf59df", "testCaseId": "3004e41c81a7ebd857f79d043aaf59df", "fullName": "testcases.test_ella.dialogue.test_how_is_the_weather_today.TestEllaHowIsWeatherToday#test_how_is_the_weather_today", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_weather_today"}]}