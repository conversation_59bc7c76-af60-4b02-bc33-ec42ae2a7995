{"name": "测试summarize what i'm reading能正常执行", "status": "passed", "description": "summarize what i'm reading", "steps": [{"name": "执行命令: summarize what i'm reading", "status": "passed", "steps": [{"name": "执行命令: summarize what i'm reading", "status": "passed", "start": 1754496146243, "stop": 1754496160035}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fc1ba2c2-f6cf-46ce-aa84-ed9b0885a19a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "257185cf-f5b9-4ab2-a4bd-4390de39ac09-attachment.png", "type": "image/png"}], "start": 1754496160035, "stop": 1754496160267}], "start": 1754496146243, "stop": 1754496160268}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496160268, "stop": 1754496160268}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "785cc92a-1d24-4e75-9555-d9c7242d2fe1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8d962185-4a6d-48b9-8cb9-f5fd10883280-attachment.png", "type": "image/png"}], "start": 1754496160268, "stop": 1754496160452}], "attachments": [{"name": "stdout", "source": "ce361a09-7213-4b5f-9ec2-fd0b7098147d-attachment.txt", "type": "text/plain"}], "start": 1754496146243, "stop": 1754496160452, "uuid": "d44c7bb9-3f56-44ed-889c-954243a66322", "historyId": "edb3a77ed85c79b290dd8cce24f372c0", "testCaseId": "edb3a77ed85c79b290dd8cce24f372c0", "fullName": "testcases.test_ella.dialogue.test_summarize_what_i_m_reading.TestEllaSummarizeWhatIMReading#test_summarize_what_i_m_reading", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_summarize_what_i_m_reading"}, {"name": "subSuite", "value": "TestEllaSummarizeWhatIMReading"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_summarize_what_i_m_reading"}]}