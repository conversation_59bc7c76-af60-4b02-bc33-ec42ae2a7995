{"name": "测试whatsapp能正常执行", "status": "broken", "statusDetails": {"message": "ValueError: too many values to unpack (expected 3)", "trace": "self = <testcases.test_ella.third_coupling.test_whatsapp.TestEllaWhatsapp object at 0x000001BE0D239D10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0F64BF10>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_whatsapp(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, files_status = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\third_coupling\\test_whatsapp.py:26: ValueError"}, "description": "whatsapp", "steps": [{"name": "执行命令: whatsapp", "status": "broken", "statusDetails": {"message": "ValueError: too many values to unpack (expected 3)\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\third_coupling\\test_whatsapp.py\", line 26, in test_whatsapp\n    initial_status, final_status, files_status = self.simple_command_test(\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "执行命令: whatsapp", "status": "passed", "start": 1754499150424, "stop": 1754499164187}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5127dd87-9f18-4e0e-bf26-807f2be81dec-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "184092db-3cc1-4d55-b290-4c37e682f486-attachment.png", "type": "image/png"}], "start": 1754499164187, "stop": 1754499164382}], "start": 1754499150424, "stop": 1754499164383}], "attachments": [{"name": "stdout", "source": "e682a895-4856-4689-adec-cc0cbfdbe323-attachment.txt", "type": "text/plain"}], "start": 1754499150424, "stop": 1754499164384, "uuid": "70deacb7-11de-498d-aeba-255f555caf11", "historyId": "fae56e9bcf9e0511ef4a7c93775731e3", "testCaseId": "fae56e9bcf9e0511ef4a7c93775731e3", "fullName": "testcases.test_ella.third_coupling.test_whatsapp.TestEllaWhatsapp#test_whatsapp", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_whatsapp"}, {"name": "subSuite", "value": "TestEllaW<PERSON>sapp"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_whatsapp"}]}