{"name": "测试who is j k rowling能正常执行", "status": "passed", "description": "who is j k rowling", "steps": [{"name": "执行命令: who is j k rowling", "status": "passed", "steps": [{"name": "执行命令: who is j k rowling", "status": "passed", "start": 1754496606866, "stop": 1754496622526}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "120d5ef0-6c12-4935-aa2a-12a895938ca2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4ba8d878-a6c0-4b2a-978e-bfe6bad28d91-attachment.png", "type": "image/png"}], "start": 1754496622526, "stop": 1754496622718}], "start": 1754496606866, "stop": 1754496622719}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496622719, "stop": 1754496622719}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d63b9b2f-a188-4fe8-ad60-b4ded1413c27-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "03a2c6ff-4c8a-4fc4-ac35-87d8371d4c75-attachment.png", "type": "image/png"}], "start": 1754496622719, "stop": 1754496622934}], "attachments": [{"name": "stdout", "source": "*************-4c6c-b4d3-48cf4db85280-attachment.txt", "type": "text/plain"}], "start": 1754496606866, "stop": 1754496622934, "uuid": "9f3ba2b2-f740-4050-96f0-04b5951f246e", "historyId": "1a5cbbb97cbe59e003ae71750a8d910f", "testCaseId": "1a5cbbb97cbe59e003ae71750a8d910f", "fullName": "testcases.test_ella.dialogue.test_who_is_j_k_rowling.TestEllaWhoIsJKRowling#test_who_is_j_k_rowling", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_who_is_j_k_rowling"}, {"name": "subSuite", "value": "TestEllaWhoIsJKRowling"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_who_is_j_k_rowling"}]}