{"name": "测试how is the wheather today能正常执行", "status": "passed", "description": "how is the wheather today", "steps": [{"name": "执行命令: how is the wheather today", "status": "passed", "steps": [{"name": "执行命令: how is the wheather today", "status": "passed", "start": 1754495411737, "stop": 1754495425651}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1dc7cd10-59a3-4123-b7fa-3e686567e359-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4d81f233-61ae-47f9-a557-ee1422f057e2-attachment.png", "type": "image/png"}], "start": 1754495425651, "stop": 1754495425877}], "start": 1754495411736, "stop": 1754495425878}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495425878, "stop": 1754495425880}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "21778e46-461b-4399-9cca-e1790195ac9d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6f3c74f0-1314-4d2c-a38e-fc8bc3a47d06-attachment.png", "type": "image/png"}], "start": 1754495425880, "stop": 1754495426068}], "attachments": [{"name": "stdout", "source": "0a5cd088-f0bd-402e-8faf-b16735f7e617-attachment.txt", "type": "text/plain"}], "start": 1754495411736, "stop": 1754495426068, "uuid": "51ef27f9-5365-41a2-b6f7-c94b6cc69d1d", "historyId": "f7282303534c1c8599c3343608e6f453", "testCaseId": "f7282303534c1c8599c3343608e6f453", "fullName": "testcases.test_ella.dialogue.test_how_is_the_wheather_today.TestEllaHowIsWheatherToday#test_how_is_the_wheather_today", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_is_the_wheather_today"}, {"name": "subSuite", "value": "TestEllaHowIsWheatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_is_the_wheather_today"}]}