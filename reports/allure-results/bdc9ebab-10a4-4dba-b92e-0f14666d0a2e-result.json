{"name": "测试summarize content on this page能正常执行", "status": "passed", "description": "summarize content on this page", "steps": [{"name": "执行命令: summarize content on this page", "status": "passed", "steps": [{"name": "执行命令: summarize content on this page", "status": "passed", "start": 1754496117768, "stop": 1754496131763}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7317c7af-e3f2-43d2-ad6f-34fc4a097369-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "15d7e867-6ced-4c49-924d-817ef092ebf3-attachment.png", "type": "image/png"}], "start": 1754496131763, "stop": 1754496131921}], "start": 1754496117768, "stop": 1754496131922}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496131922, "stop": 1754496131923}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d6229dca-639e-4329-95d5-c922ce334558-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e71761c2-d22a-40f6-ac9c-817b514a7548-attachment.png", "type": "image/png"}], "start": 1754496131923, "stop": 1754496132090}], "attachments": [{"name": "stdout", "source": "37911bc0-78a9-4b5f-83e3-0e010439a300-attachment.txt", "type": "text/plain"}], "start": 1754496117768, "stop": 1754496132091, "uuid": "167ea252-8cf8-4a5f-bad5-7b1cdeda3717", "historyId": "3e1e4da6344de7cdf40fa1d59c43dcc3", "testCaseId": "3e1e4da6344de7cdf40fa1d59c43dcc3", "fullName": "testcases.test_ella.dialogue.test_summarize_content_on_this_page.TestEllaSummarizeContentThisPage#test_summarize_content_on_this_page", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_summarize_content_on_this_page"}, {"name": "subSuite", "value": "TestEllaSummarizeContentThisPage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_summarize_content_on_this_page"}]}