{"name": "测试set an alarm at 8 am", "status": "passed", "description": "测试set an alarm at 8 am指令", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "steps": [{"name": "执行命令: set an alarm at 8 am", "status": "passed", "start": 1754494763545, "stop": 1754494778673}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8ee4b0ce-d775-4791-85f8-55913bdbc83a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cc6ecc49-ee2b-4bb5-aca0-6bbecb460dd7-attachment.png", "type": "image/png"}], "start": 1754494778673, "stop": 1754494778845}], "start": 1754494763545, "stop": 1754494778845}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754494778845, "stop": 1754494778846}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "528bce26-7a39-4119-ae4e-bfa8d7ed046d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "def33bbf-6239-44ab-8804-1754bca8a856-attachment.png", "type": "image/png"}], "start": 1754494778846, "stop": 1754494778999}], "attachments": [{"name": "stdout", "source": "082da769-e142-4508-9f7b-beb497d47c5b-attachment.txt", "type": "text/plain"}], "start": 1754494763545, "stop": 1754494778999, "uuid": "ec05b659-a62d-4855-8760-5b7edba65db6", "historyId": "de0ed312f350c708e7a00bb74aeaac0f", "testCaseId": "de0ed312f350c708e7a00bb74aeaac0f", "fullName": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am.TestEllaOpenClock#test_set_an_alarm_at_8_am", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_set_an_alarm_at_8_am"}, {"name": "subSuite", "value": "<PERSON><PERSON>lla<PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_set_an_alarm_at_8_am"}]}