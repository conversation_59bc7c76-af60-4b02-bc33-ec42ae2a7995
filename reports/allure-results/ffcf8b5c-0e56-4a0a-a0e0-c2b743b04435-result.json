{"name": "测试navigation to the first address in the image能正常执行", "status": "broken", "statusDetails": {"message": "ValueError: too many values to unpack (expected 3)", "trace": "self = <testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage object at 0x000001BE0D350B10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0F9E6950>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_navigation_to_the_first_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, response_text = self.simple_command_test(\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\nE           ValueError: too many values to unpack (expected 3)\n\ntestcases\\test_ella\\unsupported_commands\\test_navigation_to_the_first_address_in_the_image.py:26: ValueError"}, "description": "navigation to the first address in the image", "steps": [{"name": "执行命令: navigation to the first address in the image", "status": "broken", "statusDetails": {"message": "ValueError: too many values to unpack (expected 3)\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_navigation_to_the_first_address_in_the_image.py\", line 26, in test_navigation_to_the_first_address_in_the_image\n    initial_status, final_status, response_text = self.simple_command_test(\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "执行命令: navigation to the first address in the image", "status": "passed", "start": 1754500934869, "stop": 1754500949801}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9595bf0d-2d82-4b77-9cde-991c3703678e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ef43442c-89cd-4d57-9b02-a89db27d5289-attachment.png", "type": "image/png"}], "start": 1754500949801, "stop": 1754500949981}], "start": 1754500934869, "stop": 1754500949981}], "attachments": [{"name": "stdout", "source": "86518667-97c1-48f4-a4ae-33437d8bc12f-attachment.txt", "type": "text/plain"}], "start": 1754500934869, "stop": 1754500949982, "uuid": "86993b72-8377-4d9a-b845-8fd6bdc1ff37", "historyId": "9c84b087eb7d9fde94ed5bb5370b275b", "testCaseId": "9c84b087eb7d9fde94ed5bb5370b275b", "fullName": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image.TestEllaNavigationFirstAddressImage#test_navigation_to_the_first_address_in_the_image", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigation_to_the_first_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaNavigationFirstAddressImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigation_to_the_first_address_in_the_image"}]}