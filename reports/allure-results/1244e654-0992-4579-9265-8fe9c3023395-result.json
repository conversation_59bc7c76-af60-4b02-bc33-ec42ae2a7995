{"name": "测试take a joke能正常执行", "status": "passed", "description": "take a joke", "steps": [{"name": "执行命令: take a joke", "status": "passed", "steps": [{"name": "执行命令: take a joke", "status": "passed", "start": 1754496174444, "stop": 1754496189239}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "93503b38-9bce-40c6-b717-e4a693430d15-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ba1a99ce-9d39-4103-9c72-6028874b7763-attachment.png", "type": "image/png"}], "start": 1754496189239, "stop": 1754496189452}], "start": 1754496174444, "stop": 1754496189452}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496189452, "stop": 1754496189453}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e1d34eeb-fe3c-437d-b017-a36e6a15973d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e3a841f9-b569-40e9-a3a3-f5e78a3876f5-attachment.png", "type": "image/png"}], "start": 1754496189453, "stop": 1754496189654}], "attachments": [{"name": "stdout", "source": "72080579-ec5a-4262-9d4c-a289049cd11a-attachment.txt", "type": "text/plain"}], "start": 1754496174444, "stop": 1754496189655, "uuid": "25e7b380-5f42-4070-93c1-0ada8f84e387", "historyId": "543965b4120af95548616c95b1b70ef1", "testCaseId": "543965b4120af95548616c95b1b70ef1", "fullName": "testcases.test_ella.dialogue.test_take_a_joke.TestEllaTakeJoke#test_take_a_joke", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_joke"}, {"name": "subSuite", "value": "TestEllaTakeJoke"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_joke"}]}