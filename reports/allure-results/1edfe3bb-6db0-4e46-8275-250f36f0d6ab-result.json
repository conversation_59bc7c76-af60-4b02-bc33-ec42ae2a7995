{"name": "测试play jay chou's music", "status": "passed", "description": "测试play jay chou's music指令", "steps": [{"name": "执行命令: play jay chou's music", "status": "passed", "steps": [{"name": "执行命令: play jay chou's music", "status": "passed", "start": 1754494530744, "stop": 1754494554547}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ba4e968d-3cb7-4707-8365-4e8298c9ac24-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e669d983-559e-41a2-8424-49273e0d7746-attachment.png", "type": "image/png"}], "start": 1754494554547, "stop": 1754494554717}], "start": 1754494530744, "stop": 1754494554717}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754494554717, "stop": 1754494554718}, {"name": "验证music已打开", "status": "passed", "start": 1754494554718, "stop": 1754494554718}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9b57aaca-7274-49fa-b523-fdc5519f3df1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1c98e324-cda8-462c-9d70-fb752bb8a873-attachment.png", "type": "image/png"}], "start": 1754494554718, "stop": 1754494554891}], "attachments": [{"name": "stdout", "source": "4ca12932-9690-417e-96a3-6421a62c61c2-attachment.txt", "type": "text/plain"}], "start": 1754494530744, "stop": 1754494554891, "uuid": "886c5546-be5f-4bfc-b952-eca7e4451f77", "historyId": "b4e75f584d82368436f820de28f92cfd", "testCaseId": "b4e75f584d82368436f820de28f92cfd", "fullName": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music.TestEllaOpenMusic#test_play_jay_chou_s_music", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_jay_chou_s_music"}, {"name": "subSuite", "value": "TestEllaOpenMusic"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_jay_chou_s_music"}]}