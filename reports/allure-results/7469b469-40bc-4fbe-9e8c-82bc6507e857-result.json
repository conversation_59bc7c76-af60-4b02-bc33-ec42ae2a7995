{"name": "测试how's the weather today?返回正确的不支持响应", "status": "passed", "description": "验证how's the weather today?指令返回预期的不支持响应", "steps": [{"name": "执行命令: how's the weather today?", "status": "passed", "steps": [{"name": "执行命令: how's the weather today?", "status": "passed", "start": 1754495439797, "stop": 1754495460537}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6de3f540-be94-4749-819b-aa6c928efcf7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d5c2f860-9a8b-4ac1-a070-a8682d7392a2-attachment.png", "type": "image/png"}], "start": 1754495460537, "stop": 1754495460696}], "start": 1754495439797, "stop": 1754495460696}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1754495460696, "stop": 1754495460698}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c7971b6a-04b5-4f81-827f-6dc2ddd3c456-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "872d972b-f46e-4096-a527-b6e4ae2a74cf-attachment.png", "type": "image/png"}], "start": 1754495460698, "stop": 1754495460875}], "attachments": [{"name": "stdout", "source": "327e1c6f-073d-4c48-8349-8c63705b58f6-attachment.txt", "type": "text/plain"}], "start": 1754495439797, "stop": 1754495460876, "uuid": "2282d4f7-1361-47f4-8ebe-72fe92f36580", "historyId": "f2f6762c5ec83e110ace25b47e3112d5", "testCaseId": "f2f6762c5ec83e110ace25b47e3112d5", "fullName": "testcases.test_ella.dialogue.test_how_s_the_weather_today.TestEllaHowSWeatherToday#test_how_s_the_weather_today", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_s_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowSWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_s_the_weather_today"}]}