{"name": "测试disable all ai magic box features返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_disable_all_ai_magic_box_features.TestEllaDisableAllAiMagicBoxFeatures object at 0x000001BE0D2D1450>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0F65BF10>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_disable_all_ai_magic_box_features(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\unsupported_commands\\test_disable_all_ai_magic_box_features.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.unsupported_commands.test_disable_all_ai_magic_box_features.TestEllaDisableAllAiMagicBoxFeatures object at 0x000001BE0D2D1450>, expected_text = ['Sorry']\nresponse_text = ['disable all ai magic box features', '', '', '', '很抱歉，目前暂不支持帮你禁用所有AI魔盒功能。你可以手动在手机设置中查找与AI魔盒相关的选项，尝试进行相应的关闭操作。要是在操作过程中...设置中查找与AI魔盒相关的选项，尝试进行相应的关闭操作。要是在操作过程中遇到问题，随时跟我说，我来尽力提供帮助。 AI生成，仅作参考 AI魔盒的常见问题 AI魔盒的详细设置 AI魔盒的功能介绍 DeepSeek-R1 有问题尽管问我…']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['Sorry']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:923: AssertionError"}, "description": "验证disable all ai magic box features指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable all ai magic box features", "status": "passed", "steps": [{"name": "执行命令: disable all ai magic box features", "status": "passed", "start": 1754499535241, "stop": 1754499550317}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b95d3c98-93f9-466d-bbf9-9f848a8f06b1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "9f8d2aca-4f84-470d-9bdd-df68038f84f1-attachment.png", "type": "image/png"}], "start": 1754499550317, "stop": 1754499550550}], "start": 1754499535241, "stop": 1754499550550}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_disable_all_ai_magic_box_features.py\", line 32, in test_disable_all_ai_magic_box_features\n    result = self.verify_expected_in_response(expected_text, response_text)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 923, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n"}, "start": 1754499550550, "stop": 1754499550552}], "attachments": [{"name": "stdout", "source": "275f9435-**************-1a4a0f5ee7e8-attachment.txt", "type": "text/plain"}], "start": 1754499535241, "stop": 1754499550553, "uuid": "7cd3ae9d-5173-44d7-959c-879041ffaa2e", "historyId": "fa8d6ac5c42acf5f644e6f5370a9a773", "testCaseId": "fa8d6ac5c42acf5f644e6f5370a9a773", "fullName": "testcases.test_ella.unsupported_commands.test_disable_all_ai_magic_box_features.TestEllaDisableAllAiMagicBoxFeatures#test_disable_all_ai_magic_box_features", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_all_ai_magic_box_features"}, {"name": "subSuite", "value": "TestEllaDisableAllAiMagicBoxFeatures"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_all_ai_magic_box_features"}]}