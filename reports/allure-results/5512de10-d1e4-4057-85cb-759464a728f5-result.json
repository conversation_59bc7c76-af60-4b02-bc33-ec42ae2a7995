{"name": "测试redial", "status": "failed", "statusDetails": {"message": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', '没有通话记录', '', '']'\nassert False", "trace": "self = <testcases.test_ella.unsupported_commands.test_redial.TestEllaOpenPlayPoliticalNews object at 0x000001BE0D36CB10>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0F9CDFD0>\n\n    @allure.title(\"测试redial\")\n    @allure.description(\"测试redial指令\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_play_political_news(self, ella_app):\n        \"\"\"测试redial命令\"\"\"\n        command = \"redial\"\n        app_name = 'contacts'\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = []\n            result = self.verify_expected_in_response(expected_text, response_text)\n            assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证{app_name}已打开\"):\n>           assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', '没有通话记录', '', '']'\nE           assert False\n\ntestcases\\test_ella\\unsupported_commands\\test_redial.py:34: AssertionError"}, "description": "测试redial指令", "steps": [{"name": "执行命令: redial", "status": "passed", "steps": [{"name": "执行命令: redial", "status": "passed", "start": 1754501375013, "stop": 1754501397243}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d6356332-28a3-4f3e-b8d4-64e99d52dcf6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "aca2469c-6dd2-4dff-b001-5a9cb3e0f56d-attachment.png", "type": "image/png"}], "start": 1754501397243, "stop": 1754501397447}], "start": 1754501375013, "stop": 1754501397447}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754501397447, "stop": 1754501397448}, {"name": "验证contacts已打开", "status": "failed", "statusDetails": {"message": "AssertionError: contacts: 初始=False, 最终=False, 响应='['redial', '没有通话记录', '', '']'\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_redial.py\", line 34, in test_play_political_news\n    assert final_status, f\"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1754501397448, "stop": 1754501397448}], "attachments": [{"name": "stdout", "source": "470fbfbf-a2f9-4968-8ecb-77dc8fc777a2-attachment.txt", "type": "text/plain"}], "start": 1754501375013, "stop": 1754501397449, "uuid": "ca5208dc-4988-4520-a056-8f91ed02a953", "historyId": "4b05cca85015ddfc6c9bfc65934f9bcc", "testCaseId": "4b05cca85015ddfc6c9bfc65934f9bcc", "fullName": "testcases.test_ella.unsupported_commands.test_redial.TestEllaOpenPlayPoliticalNews#test_play_political_news", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_redial"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_redial"}]}