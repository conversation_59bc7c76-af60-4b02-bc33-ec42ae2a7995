{"name": "测试make a call能正常执行", "status": "passed", "description": "make a call", "steps": [{"name": "执行命令: make a call", "status": "passed", "steps": [{"name": "执行命令: make a call", "status": "passed", "start": 1754495701953, "stop": 1754495724552}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3dd78022-9ecd-4c41-adad-85f0d1e1b055-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d9063b09-b833-4369-9355-9307e1c29497-attachment.png", "type": "image/png"}], "start": 1754495724552, "stop": 1754495724702}], "start": 1754495701953, "stop": 1754495724702}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495724702, "stop": 1754495724703}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7611f645-c7ed-471e-b627-57c1d2f5773e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e4737891-3742-4a45-86cb-1c8cf82fbf06-attachment.png", "type": "image/png"}], "start": 1754495724703, "stop": 1754495724877}], "attachments": [{"name": "stdout", "source": "b3f3beae-9cfe-44d3-ba0d-c8e1beb9d84d-attachment.txt", "type": "text/plain"}], "start": 1754495701953, "stop": 1754495724877, "uuid": "ac5ba132-b16d-4fe2-9519-58fe799bd647", "historyId": "2428ad915810150c12838b88ee13f49c", "testCaseId": "2428ad915810150c12838b88ee13f49c", "fullName": "testcases.test_ella.dialogue.test_make_a_call.TestEllaMakeCall#test_make_a_call", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_make_a_call"}, {"name": "subSuite", "value": "TestEllaMakeCall"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_make_a_call"}]}