{"name": "测试close phonemaster能正常执行", "status": "passed", "description": "close phonemaster", "steps": [{"name": "执行命令: close phonemaster", "status": "passed", "steps": [{"name": "执行命令: close phonemaster", "status": "passed", "start": 1754493960091, "stop": 1754493973549}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "0a9a3699-f7cb-4bc6-b198-8d4e54a30b60-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3cd6fe7e-5266-42ba-acb7-e505f830f688-attachment.png", "type": "image/png"}], "start": 1754493973549, "stop": 1754493973762}], "start": 1754493960091, "stop": 1754493973762}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754493973762, "stop": 1754493973763}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "f4c13aff-1e13-4d9a-a4d1-d6de5c19e494-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "7ab46f42-6ef3-46f0-8ad1-8b5ce3345983-attachment.png", "type": "image/png"}], "start": 1754493973763, "stop": 1754493973975}], "attachments": [{"name": "stdout", "source": "45864e1a-3c6c-4fc4-99b8-d1b8060f9579-attachment.txt", "type": "text/plain"}], "start": 1754493960091, "stop": 1754493973975, "uuid": "846d80fa-a90b-4ce1-ba2b-2ac25c646e87", "historyId": "bc062eca91b16841cac5c9865921b5c1", "testCaseId": "bc062eca91b16841cac5c9865921b5c1", "fullName": "testcases.test_ella.component_coupling.test_close_phonemaster.TestEllaClosePhonemaster#test_close_phonemaster", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_phonemaster"}, {"name": "subSuite", "value": "TestEllaClosePhonemaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_phonemaster"}]}