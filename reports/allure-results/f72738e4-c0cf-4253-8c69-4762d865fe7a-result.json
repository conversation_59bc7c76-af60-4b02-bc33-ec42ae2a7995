{"name": "测试take a note on how to build a treehouse能正常执行", "status": "passed", "description": "take a note on how to build a treehouse", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "status": "passed", "steps": [{"name": "执行命令: take a note on how to build a treehouse", "status": "passed", "start": 1754496203706, "stop": 1754496217211}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "908c4741-6f41-4c26-b5e0-323e298b9522-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8955b3a5-2916-4675-ba96-012baaefd840-attachment.png", "type": "image/png"}], "start": 1754496217211, "stop": 1754496217407}], "start": 1754496203706, "stop": 1754496217407}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496217407, "stop": 1754496217408}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5236e2b8-fd8b-4e6d-93b8-c0ca4b79bdd2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "53bc36ba-d6bc-42d6-b106-71434854b8c2-attachment.png", "type": "image/png"}], "start": 1754496217408, "stop": 1754496217575}], "attachments": [{"name": "stdout", "source": "ae427685-6013-453a-af2e-2d5214ae4a4a-attachment.txt", "type": "text/plain"}], "start": 1754496203706, "stop": 1754496217576, "uuid": "33bf6369-ff51-4d93-b9c6-bf29f399462c", "historyId": "c45aa63628fe12b375ba7e65c39d93b1", "testCaseId": "c45aa63628fe12b375ba7e65c39d93b1", "fullName": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse.TestEllaTakeNoteHowBuildTreehouse#test_take_a_note_on_how_to_build_a_treehouse", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_take_a_note_on_how_to_build_a_treehouse"}, {"name": "subSuite", "value": "TestEllaTakeNoteHowBuildTreehouse"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_take_a_note_on_how_to_build_a_treehouse"}]}