{"name": "测试i want to watch fireworks能正常执行", "status": "passed", "description": "i want to watch fireworks", "steps": [{"name": "执行命令: i want to watch fireworks", "status": "passed", "steps": [{"name": "执行命令: i want to watch fireworks", "status": "passed", "start": 1754495640945, "stop": 1754495656912}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e7cbfe64-d8af-4507-b44e-6231d4c1229e-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "88db741f-8135-4b73-ba26-666bd40d1317-attachment.png", "type": "image/png"}], "start": 1754495656912, "stop": 1754495657078}], "start": 1754495640945, "stop": 1754495657078}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495657078, "stop": 1754495657078}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "547993c5-f010-44ee-928e-51df061cc145-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "456acaa6-cc2e-4e67-8fa8-de8d6a07df55-attachment.png", "type": "image/png"}], "start": 1754495657079, "stop": 1754495657264}], "attachments": [{"name": "stdout", "source": "50b31e7d-35af-4a28-a9f2-7ab3a4e936c1-attachment.txt", "type": "text/plain"}], "start": 1754495640945, "stop": 1754495657264, "uuid": "6b6e79bb-5430-4a8d-9ee7-618c0d4839ba", "historyId": "4ae696581fe41611547bc10ddba4f526", "testCaseId": "4ae696581fe41611547bc10ddba4f526", "fullName": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks.TestEllaIWantWatchFireworks#test_i_want_to_watch_fireworks", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_i_want_to_watch_fireworks"}, {"name": "subSuite", "value": "TestEllaIWantWatchFireworks"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_i_want_to_watch_fireworks"}]}