{"name": "测试navigation to the lucky能正常执行", "status": "passed", "description": "navigation to the lucky", "steps": [{"name": "执行命令: navigation to the lucky", "status": "passed", "steps": [{"name": "执行命令: navigation to the lucky", "status": "passed", "start": 1754498975381, "stop": 1754498990656}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d4183e63-002b-4592-af99-c6600e53706d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fbe0e297-0afb-4e4c-b8e5-e8b2c9d54d88-attachment.png", "type": "image/png"}], "start": 1754498990656, "stop": 1754498990873}], "start": 1754498975381, "stop": 1754498990873}, {"name": "验证应用已打开", "status": "passed", "start": 1754498990873, "stop": 1754498990873}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d8fbc3da-32cd-4f61-9c28-4c348dd7eac1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8c75bedf-62d9-4e49-b76c-bc57c3c7dd2d-attachment.png", "type": "image/png"}], "start": 1754498990873, "stop": 1754498991066}], "attachments": [{"name": "stdout", "source": "cce7401d-9001-4eb1-85ca-57ea74634b08-attachment.txt", "type": "text/plain"}], "start": 1754498975381, "stop": 1754498991066, "uuid": "318faffb-ce90-4d77-a751-8550f65b6b3d", "historyId": "75c56947a7b1061f7ec858fb20919b50", "testCaseId": "75c56947a7b1061f7ec858fb20919b50", "fullName": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky.TestEllaNavigationToTheLucky#test_navigation_to_the_lucky", "labels": [{"name": "severity", "value": "critical"}, {"name": "story", "value": "Ella语音助手基础指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.third_coupling"}, {"name": "suite", "value": "test_navigation_to_the_lucky"}, {"name": "subSuite", "value": "TestEllaNavigationToTheLucky"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.third_coupling.test_navigation_to_the_lucky"}]}