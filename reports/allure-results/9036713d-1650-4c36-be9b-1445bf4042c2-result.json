{"name": "测试open contact命令", "status": "failed", "statusDetails": {"message": "AssertionError: 联系人应用未打开: 初始=False, 最终=False\nassert False", "trace": "self = <testcases.test_ella.component_coupling.test_open_contact.TestEllaContactCommandConcise object at 0x000001BE0D5F9950>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0D98BF10>\n\n    @allure.title(\"测试open contact命令\")\n    @allure.description(\"使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_open_contact(self, ella_app):\n        \"\"\"测试open contact命令 - 简洁版本\"\"\"\n        command = \"open contact\"\n    \n        with allure.step(f\"执行命令: {command}\"):\n>           initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command\n            )\n\ntestcases\\test_ella\\component_coupling\\test_open_contact.py:26: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\ntestcases\\test_ella\\base_ella_test.py:1069: in simple_command_test\n    initial_status, final_status, response_text, files_status = self.execute_command_and_verify(\ntestcases\\test_ella\\base_ella_test.py:260: in execute_command_and_verify\n    self._verify_status_change(initial_status, final_status, command)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.component_coupling.test_open_contact.TestEllaContactCommandConcise object at 0x000001BE0D5F9950>, initial_status = False, final_status = False, command = 'open contact'\n\n    def _verify_status_change(self, initial_status, final_status, command: str):\n        \"\"\"验证状态变化\"\"\"\n        if \"bluetooth\" in command.lower():\n            if \"open\" in command.lower():\n                assert final_status, f\"蓝牙未开启: 初始={initial_status}, 最终={final_status}\"\n            elif \"close\" in command.lower():\n                assert not final_status, f\"蓝牙未关闭: 初始={initial_status}, 最终={final_status}\"\n        elif (\"contact\" in command.lower() or \"contacts\" in command.lower()) and \"open\" in command.lower():\n>           assert final_status, f\"联系人应用未打开: 初始={initial_status}, 最终={final_status}\"\nE           AssertionError: 联系人应用未打开: 初始=False, 最终=False\nE           assert False\n\ntestcases\\test_ella\\base_ella_test.py:826: AssertionError"}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "steps": [{"name": "执行命令: open contact", "status": "failed", "statusDetails": {"message": "AssertionError: 联系人应用未打开: 初始=False, 最终=False\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\component_coupling\\test_open_contact.py\", line 26, in test_open_contact\n    initial_status, final_status, response_text, files_status = self.simple_command_test(\n                                                                ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 1069, in simple_command_test\n    initial_status, final_status, response_text, files_status = self.execute_command_and_verify(\n                                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 260, in execute_command_and_verify\n    self._verify_status_change(initial_status, final_status, command)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 826, in _verify_status_change\n    assert final_status, f\"联系人应用未打开: 初始={initial_status}, 最终={final_status}\"\n"}, "steps": [{"name": "执行命令: open contact", "status": "failed", "statusDetails": {"message": "AssertionError: 联系人应用未打开: 初始=False, 最终=False\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 1069, in simple_command_test\n    initial_status, final_status, response_text, files_status = self.execute_command_and_verify(\n                                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 260, in execute_command_and_verify\n    self._verify_status_change(initial_status, final_status, command)\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 826, in _verify_status_change\n    assert final_status, f\"联系人应用未打开: 初始={initial_status}, 最终={final_status}\"\n"}, "start": 1754494234373, "stop": 1754494257360}], "start": 1754494234373, "stop": 1754494257360}], "attachments": [{"name": "stdout", "source": "43a26da0-206d-4e11-88de-9ccb0740b3ac-attachment.txt", "type": "text/plain"}], "start": 1754494234373, "stop": 1754494257361, "uuid": "e99c646f-4651-48f1-a087-9744ce11d901", "historyId": "7c32e753573a480d7d5c09abab43469e", "testCaseId": "7c32e753573a480d7d5c09abab43469e", "fullName": "testcases.test_ella.component_coupling.test_open_contact.TestEllaContactCommandConcise#test_open_contact", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "联系人控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_contact"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_contact"}]}