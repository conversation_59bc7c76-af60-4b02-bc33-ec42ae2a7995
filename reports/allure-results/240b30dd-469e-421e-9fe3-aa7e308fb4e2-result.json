{"name": "测试close aivana能正常执行", "status": "passed", "description": "close aivana", "steps": [{"name": "执行命令: close aivana", "status": "passed", "steps": [{"name": "执行命令: close aivana", "status": "passed", "start": 1754493814312, "stop": 1754493848960}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1600ad41-4417-484f-a145-764e6873a239-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "61646a87-73bd-44fd-81f9-49a81c846e03-attachment.png", "type": "image/png"}], "start": 1754493848960, "stop": 1754493849588}], "start": 1754493814312, "stop": 1754493849588}, {"name": "验证已打开", "status": "passed", "start": 1754493849588, "stop": 1754493849588}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d12b53ba-80bc-4d6d-8fc9-b8e5a1f76729-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6238c00c-6c59-468d-ae1d-06589631dece-attachment.png", "type": "image/png"}], "start": 1754493849588, "stop": 1754493849738}], "attachments": [{"name": "stdout", "source": "ccb39694-a561-47d5-9b7d-91b32b97f226-attachment.txt", "type": "text/plain"}], "start": 1754493814312, "stop": 1754493849738, "uuid": "2c138169-2bbc-4738-9b3a-c4d8320b54fc", "historyId": "d9f01ef1af79559082ce9e9b2e40295f", "testCaseId": "d9f01ef1af79559082ce9e9b2e40295f", "fullName": "testcases.test_ella.component_coupling.test_close_aivana.TestEllaCloseAivana#test_close_aivana", "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_aivana"}, {"name": "subSuite", "value": "TestEllaCloseAivana"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_aivana"}]}