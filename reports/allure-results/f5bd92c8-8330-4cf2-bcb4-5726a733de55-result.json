{"name": "open clock", "status": "passed", "description": "使用open clock命令，验证响应包含Done且实际打开clock命令", "steps": [{"name": "执行命令: open clock", "status": "passed", "steps": [{"name": "执行命令: open clock", "status": "passed", "start": 1754494199095, "stop": 1754494219535}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a0dd1105-3bc6-477f-8ad3-c59e316cfb17-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "125b728a-2baf-43b0-931a-748351c76078-attachment.png", "type": "image/png"}], "start": 1754494219535, "stop": 1754494219754}], "start": 1754494199095, "stop": 1754494219754}, {"name": "验证响应包含Done", "status": "passed", "start": 1754494219754, "stop": 1754494219755}, {"name": "验证clock已打开", "status": "passed", "start": 1754494219755, "stop": 1754494219755}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "27587fc6-25e4-4f0d-a3e4-37b1043d5749-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "07666a6f-752b-46bb-b9db-15b20967c472-attachment.png", "type": "image/png"}], "start": 1754494219755, "stop": 1754494219955}], "attachments": [{"name": "stdout", "source": "6b5e3cc2-39bb-4d83-a97c-d3637cab8ed4-attachment.txt", "type": "text/plain"}], "start": 1754494199095, "stop": 1754494219955, "uuid": "26856d3c-fdfa-4e8b-81be-736869848f88", "historyId": "169e5b613c0fec2cebd053175998bf17", "testCaseId": "169e5b613c0fec2cebd053175998bf17", "fullName": "testcases.test_ella.component_coupling.test_open_clock.TestEllaCommandConcise#test_open_clock", "labels": [{"name": "story", "value": "open clock"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_open_clock"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_open_clock"}]}