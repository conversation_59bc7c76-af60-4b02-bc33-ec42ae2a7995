{"name": "测试help me write an email能正常执行", "status": "passed", "description": "help me write an email", "steps": [{"name": "执行命令: help me write an email", "status": "passed", "steps": [{"name": "执行命令: help me write an email", "status": "passed", "start": 1754500328572, "stop": 1754500343385}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dd440327-203f-405a-bad1-7e1fad51101c-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6f4796b3-84c4-45ff-a536-e18439c0fdda-attachment.png", "type": "image/png"}], "start": 1754500343385, "stop": 1754500343577}], "start": 1754500328571, "stop": 1754500343578}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754500343578, "stop": 1754500343579}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6e62c6af-8419-4a81-94f6-c0aa169ae7d9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cf4b3539-a30e-4101-aa62-69ca05190e62-attachment.png", "type": "image/png"}], "start": 1754500343579, "stop": 1754500343772}], "attachments": [{"name": "stdout", "source": "70c7a9db-ff73-4708-9f4e-940251ca0cd6-attachment.txt", "type": "text/plain"}], "start": 1754500328571, "stop": 1754500343772, "uuid": "2ccf62b1-e8be-404d-b135-86d3dd570ea3", "historyId": "70c9bd8c4aab57e96eb06acb93ca2223", "testCaseId": "70c9bd8c4aab57e96eb06acb93ca2223", "fullName": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email.TestEllaHelpMeWriteAnEmail#test_help_me_write_an_email", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_help_me_write_an_email"}, {"name": "subSuite", "value": "TestEllaHelpMeWriteAnEmail"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_help_me_write_an_email"}]}