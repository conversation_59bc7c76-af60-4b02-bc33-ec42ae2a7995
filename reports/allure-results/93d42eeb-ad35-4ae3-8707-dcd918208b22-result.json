{"name": "测试hello hello能正常执行", "status": "passed", "description": "hello hello", "steps": [{"name": "执行命令: hello hello", "status": "passed", "steps": [{"name": "执行命令: hello hello", "status": "passed", "start": 1754495286512, "stop": 1754495302181}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5c4b21e5-c56a-465b-b30e-d7626dce3b75-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ca4f9ccc-03f2-473e-9943-e96bc561b3c6-attachment.png", "type": "image/png"}], "start": 1754495302181, "stop": 1754495302437}], "start": 1754495286512, "stop": 1754495302437}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495302437, "stop": 1754495302437}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d27a2e33-287a-4262-addd-d91a90d63ffa-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "31001cd4-41fe-4596-84ae-9a42691a928b-attachment.png", "type": "image/png"}], "start": 1754495302437, "stop": 1754495302621}], "attachments": [{"name": "stdout", "source": "8d9a634a-f5dc-406a-a2ff-ff13a512be70-attachment.txt", "type": "text/plain"}], "start": 1754495286512, "stop": 1754495302622, "uuid": "0410a1d4-8464-4605-a9e6-c08fa0cfda0b", "historyId": "b3fa1d22b59def5f059cd9b0eefbe2b0", "testCaseId": "b3fa1d22b59def5f059cd9b0eefbe2b0", "fullName": "testcases.test_ella.dialogue.test_hello_hello.TestEllaHelloHello#test_hello_hello", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_hello_hello"}, {"name": "subSuite", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_hello_hello"}]}