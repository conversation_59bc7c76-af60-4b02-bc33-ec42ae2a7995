{"name": "测试how to say hello in french能正常执行", "status": "passed", "description": "how to say hello in french", "steps": [{"name": "执行命令: how to say hello in french", "status": "passed", "steps": [{"name": "执行命令: how to say hello in french", "status": "passed", "start": 1754495510559, "stop": 1754495523060}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c67499b3-4c4e-4045-91d7-ecc673557532-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "3718caac-05cb-4161-99f9-ffe4b643240d-attachment.png", "type": "image/png"}], "start": 1754495523060, "stop": 1754495523249}], "start": 1754495510559, "stop": 1754495523249}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754495523249, "stop": 1754495523251}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bcd3309c-355d-4e08-993a-73f53f2d9e45-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fcdfe2aa-3388-4bf8-a6db-43a7ccfbaf2b-attachment.png", "type": "image/png"}], "start": 1754495523251, "stop": 1754495523424}], "attachments": [{"name": "stdout", "source": "68894af4-3b11-4257-a3b3-914d8d0ef73e-attachment.txt", "type": "text/plain"}], "start": 1754495510559, "stop": 1754495523424, "uuid": "c6d90fd8-4d04-42b4-adfd-17781170018e", "historyId": "cc44ad4097a589726631a345e0cd01ad", "testCaseId": "cc44ad4097a589726631a345e0cd01ad", "fullName": "testcases.test_ella.dialogue.test_how_to_say_hello_in_french.TestEllaHowSayHelloFrench#test_how_to_say_hello_in_french", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.dialogue"}, {"name": "suite", "value": "test_how_to_say_hello_in_french"}, {"name": "subSuite", "value": "TestEllaHowSayHelloFrench"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.dialogue.test_how_to_say_hello_in_french"}]}