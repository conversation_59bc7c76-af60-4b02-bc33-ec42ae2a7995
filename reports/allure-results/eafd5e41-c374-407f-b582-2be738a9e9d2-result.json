{"name": "测试search the address in the image能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', '以下是一些可以用来搜索图片中地址的方法：\\n\\n利用搜索引擎的图片搜索功能 ：如果照片清晰且包含足够的地理位置信息，如路标、店铺招牌、标志性建筑等，可将照片上传至搜索引擎的图片搜索功能，如百度图片搜索、谷歌图片搜索等，系统会自动比对数据库中的图片，可能直接给出地址信息或相似图片，从而间接获取地址1234568。\\n使用专业的反向图像搜索工具 ：这些工具能更精确地识别图片中的信息并进行定位。需先下载并安装合适的工具，上传照片后，工具会自动分析图片中的特征，并在数据库中搜索匹配信息，根据工具提供的定位结果，结合地图服务，可确定照片的大致位置16。\\n借助地图应用的图片搜索功能 ：若照片中有标志性建筑或景观，', '1:31 对话 发现 8 篇参考资料 以下是一些可以用来搜索图片中地址的方法：&#10;&#10;利用搜索引擎的图片搜索功能 ：如果照片清晰且包含足够的地理位置信息，如路标、店铺招牌、标志性建筑等，可将照片上传至搜索引擎的图片搜索功能，如百度图片搜索、谷歌图片搜索等，系统会自动比对数据库中的图片，可能直接给出地址信息或相似图片，从而间接获取地址1234568。&#10;使用专业的反向图像搜索工具 ：这些工具能更精确地识别图片中的信息并进行定位。需先下载并安装合适的工具，上传照片后，工具会自动分析图片中的特征，并在数据库中搜索匹配信息，根据工具提供的定位结果，结合地图服务，可确定照片的大致位置16。&#10;借助地图应用的图片搜索功能 ：若照片中有标志性建筑或景观，可利用地图应用中的“图片搜索”功能，上传照片进行识 AI生成，仅作参考 DeepSeek-R1 有问题尽管问我…']'\nassert None", "trace": "self = <testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image.TestEllaSearchAddressImage object at 0x000001BE0D38DF10>\nella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0F8F0D90>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_search_the_address_in_the_image(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        # with allure.step(\"验证响应包含期望内容\"):\n        #     expected_text = self.expected_text\n        #     result = self.verify_expected_in_response(expected_text, response_text)\n        #     assert result, f\"响应文本应包含{expected_text}，实际响应: '{response_text}'\"\n    \n        with allure.step(f\"验证GoogleMap应用已打开\"):\n>           assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\nE           AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', '以下是一些可以用来搜索图片中地址的方法：\\n\\n利用搜索引擎的图片搜索功能 ：如果照片清晰且包含足够的地理位置信息，如路标、店铺招牌、标志性建筑等，可将照片上传至搜索引擎的图片搜索功能，如百度图片搜索、谷歌图片搜索等，系统会自动比对数据库中的图片，可能直接给出地址信息或相似图片，从而间接获取地址1234568。\\n使用专业的反向图像搜索工具 ：这些工具能更精确地识别图片中的信息并进行定位。需先下载并安装合适的工具，上传照片后，工具会自动分析图片中的特征，并在数据库中搜索匹配信息，根据工具提供的定位结果，结合地图服务，可确定照片的大致位置16。\\n借助地图应用的图片搜索功能 ：若照片中有标志性建筑或景观，', '1:31 对话 发现 8 篇参考资料 以下是一些可以用来搜索图片中地址的方法：&#10;&#10;利用搜索引擎的图片搜索功能 ：如果照片清晰且包含足够的地理位置信息，如路标、店铺招牌、标志性建筑等，可将照片上传至搜索引擎的图片搜索功能，如百度图片搜索、谷歌图片搜索等，系统会自动比对数据库中的图片，可能直接给出地址信息或相似图片，从而间接获取地址1234568。&#10;使用专业的反向图像搜索工具 ：这些工具能更精确地识别图片中的信息并进行定位。需先下载并安装合适的工具，上传照片后，工具会自动分析图片中的特征，并在数据库中搜索匹配信息，根据工具提供的定位结果，结合地图服务，可确定照片的大致位置16。&#10;借助地图应用的图片搜索功能 ：若照片中有标志性建筑或景观，可利用地图应用中的“图片搜索”功能，上传照片进行识 AI生成，仅作参考 DeepSeek-R1 有问题尽管问我…']'\nE           assert None\n\ntestcases\\test_ella\\unsupported_commands\\test_search_the_address_in_the_image.py:36: AssertionError"}, "description": "search the address in the image", "steps": [{"name": "执行命令: search the address in the image", "status": "passed", "steps": [{"name": "执行命令: search the address in the image", "status": "passed", "start": 1754501502990, "stop": 1754501518390}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "61ec825b-e460-4012-86d5-cb21496517e8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c3555dea-24c8-4d0d-8361-30a2f3ebab79-attachment.png", "type": "image/png"}], "start": 1754501518390, "stop": 1754501518584}], "start": 1754501502990, "stop": 1754501518585}, {"name": "验证GoogleMap应用已打开", "status": "failed", "statusDetails": {"message": "AssertionError: GoogleMap应用未打开: 初始=None, 最终=None, 响应='['search the address in the image', '', '', '', '以下是一些可以用来搜索图片中地址的方法：\\n\\n利用搜索引擎的图片搜索功能 ：如果照片清晰且包含足够的地理位置信息，如路标、店铺招牌、标志性建筑等，可将照片上传至搜索引擎的图片搜索功能，如百度图片搜索、谷歌图片搜索等，系统会自动比对数据库中的图片，可能直接给出地址信息或相似图片，从而间接获取地址1234568。\\n使用专业的反向图像搜索工具 ：这些工具能更精确地识别图片中的信息并进行定位。需先下载并安装合适的工具，上传照片后，工具会自动分析图片中的特征，并在数据库中搜索匹配信息，根据工具提供的定位结果，结合地图服务，可确定照片的大致位置16。\\n借助地图应用的图片搜索功能 ：若照片中有标志性建筑或景观，', '1:31 对话 发现 8 篇参考资料 以下是一些可以用来搜索图片中地址的方法：&#10;&#10;利用搜索引擎的图片搜索功能 ：如果照片清晰且包含足够的地理位置信息，如路标、店铺招牌、标志性建筑等，可将照片上传至搜索引擎的图片搜索功能，如百度图片搜索、谷歌图片搜索等，系统会自动比对数据库中的图片，可能直接给出地址信息或相似图片，从而间接获取地址1234568。&#10;使用专业的反向图像搜索工具 ：这些工具能更精确地识别图片中的信息并进行定位。需先下载并安装合适的工具，上传照片后，工具会自动分析图片中的特征，并在数据库中搜索匹配信息，根据工具提供的定位结果，结合地图服务，可确定照片的大致位置16。&#10;借助地图应用的图片搜索功能 ：若照片中有标志性建筑或景观，可利用地图应用中的“图片搜索”功能，上传照片进行识 AI生成，仅作参考 DeepSeek-R1 有问题尽管问我…']'\nassert None\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_search_the_address_in_the_image.py\", line 36, in test_search_the_address_in_the_image\n    assert final_status, f\"GoogleMap应用未打开: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'\"\n"}, "start": 1754501518585, "stop": 1754501518585}], "attachments": [{"name": "stdout", "source": "dc46b598-7b4a-4fb0-9510-c39704110712-attachment.txt", "type": "text/plain"}], "start": 1754501502990, "stop": 1754501518585, "uuid": "1cb43cbe-3331-4420-ac3b-aa42872a92de", "historyId": "c50847e2010bac3c5a9bb7ff0b690fb6", "testCaseId": "c50847e2010bac3c5a9bb7ff0b690fb6", "fullName": "testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image.TestEllaSearchAddressImage#test_search_the_address_in_the_image", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_search_the_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaSearchAddressImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_search_the_address_in_the_image"}]}