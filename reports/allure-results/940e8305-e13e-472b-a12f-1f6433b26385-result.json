{"name": "测试Adjustment the brightness to 50%能正常执行", "status": "passed", "description": "Adjustment the brightness to 50%", "steps": [{"name": "执行命令: Adjustment the brightness to 50%", "status": "passed", "steps": [{"name": "执行命令: Adjustment the brightness to 50%", "status": "passed", "start": 1754496766774, "stop": 1754496780551}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5bd8341b-a8af-4cae-a240-bd554fa44673-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "23983b3e-d3b9-4d74-93cd-dccf9901d117-attachment.png", "type": "image/png"}], "start": 1754496780551, "stop": 1754496780726}], "start": 1754496766774, "stop": 1754496780726}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754496780726, "stop": 1754496780727}, {"name": "验证应用已打开", "status": "passed", "start": 1754496780727, "stop": 1754496780727}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d9d13c9d-e1b6-4d78-9063-a0d8746831b0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8348f83b-f5fc-4f31-b21e-aa75833b1ff5-attachment.png", "type": "image/png"}], "start": 1754496780727, "stop": 1754496780888}], "attachments": [{"name": "stdout", "source": "068dc3f4-586d-4b07-96a0-c1d45fcb9f69-attachment.txt", "type": "text/plain"}], "start": 1754496766774, "stop": 1754496780888, "uuid": "baa6f5cb-a093-40d1-95ab-ffd010ed9ac7", "historyId": "ad18e983dce31052b87b7404f3b347ce", "testCaseId": "ad18e983dce31052b87b7404f3b347ce", "fullName": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to.TestEllaAdjustmentBrightness#test_adjustment_the_brightness_to", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_adjustment_the_brightness_to"}, {"name": "subSuite", "value": "TestEllaAdjustmentBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_adjustment_the_brightness_to"}]}