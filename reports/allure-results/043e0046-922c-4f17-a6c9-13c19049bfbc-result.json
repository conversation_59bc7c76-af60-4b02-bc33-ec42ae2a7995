{"name": "测试play rock music", "status": "passed", "description": "测试play rock music指令", "steps": [{"name": "执行命令: play rock music", "status": "passed", "steps": [{"name": "执行命令: play rock music", "status": "passed", "start": 1754494636509, "stop": 1754494658905}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7afed8fc-718e-4cad-b699-605ff9eb71c3-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "635b4212-2fba-4afc-b368-001d7060f8bb-attachment.png", "type": "image/png"}], "start": 1754494658905, "stop": 1754494659100}], "start": 1754494636509, "stop": 1754494659100}, {"name": "验证响应包含期望内容", "status": "passed", "start": 1754494659100, "stop": 1754494659102}, {"name": "验证visha已打开", "status": "passed", "start": 1754494659102, "stop": 1754494659102}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dcdcda83-f723-49d7-9fbf-c89e2ba0f39f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "21ab2ac7-0bbd-4316-8b3b-75598800a250-attachment.png", "type": "image/png"}], "start": 1754494659102, "stop": 1754494659265}], "attachments": [{"name": "stdout", "source": "5df24e8f-a2b5-4f99-b491-dce37b8dc91b-attachment.txt", "type": "text/plain"}], "start": 1754494636509, "stop": 1754494659265, "uuid": "c266a1ea-fad8-4ea7-bdd3-94346540d15e", "historyId": "6075008522e5d0ae1667c4ac4be759eb", "testCaseId": "6075008522e5d0ae1667c4ac4be759eb", "fullName": "testcases.test_ella.component_coupling.test_play_rock_music.TestEllaOpenVisha#test_play_rock_music", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_play_rock_music"}, {"name": "subSuite", "value": "TestEllaOpen<PERSON>a"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_play_rock_music"}]}