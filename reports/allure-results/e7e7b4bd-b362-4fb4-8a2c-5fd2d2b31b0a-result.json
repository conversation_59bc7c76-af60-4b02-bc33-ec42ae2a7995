{"name": "测试display the route go company", "status": "passed", "description": "测试display the route go company指令", "steps": [{"name": "执行命令: display the route go company", "status": "passed", "steps": [{"name": "执行命令: display the route go company", "status": "passed", "start": 1754494079206, "stop": 1754494094445}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2099af22-78c8-4953-be06-b03ab070f9a4-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f556f159-bb54-48e6-8676-8858d9ab710d-attachment.png", "type": "image/png"}], "start": 1754494094445, "stop": 1754494094636}], "start": 1754494079206, "stop": 1754494094636}, {"name": "验证maps已打开", "status": "passed", "start": 1754494094636, "stop": 1754494094636}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9874bd2c-7752-4d9a-a587-e658d434afc7-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "201222a3-f85d-49e4-93c1-ab6816d6e3fa-attachment.png", "type": "image/png"}], "start": 1754494094636, "stop": 1754494094833}], "attachments": [{"name": "stdout", "source": "21de10f0-e9cd-4ab1-9941-dce8fdf16dec-attachment.txt", "type": "text/plain"}], "start": 1754494079206, "stop": 1754494094833, "uuid": "8ee19b49-b833-4779-938f-fbac935e536e", "historyId": "f4d12b1367b35df96178a58e48fe8f5e", "testCaseId": "f4d12b1367b35df96178a58e48fe8f5e", "fullName": "testcases.test_ella.component_coupling.test_display_the_route_go_company.TestEllaOpenMaps#test_display_the_route_go_company", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_display_the_route_go_company"}, {"name": "subSuite", "value": "TestEllaOpenMaps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_display_the_route_go_company"}]}