{"name": "测试navigation to the address in thie image能正常执行", "status": "passed", "description": "navigation to the address in thie image", "steps": [{"name": "执行命令: navigation to the address in thie image", "status": "passed", "steps": [{"name": "执行命令: navigation to the address in thie image", "status": "passed", "start": 1754500903911, "stop": 1754500919828}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ffc95171-2784-4d43-ad67-e674a5b632a2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6be705a5-54ff-4aa0-acf3-0fff59c420b3-attachment.png", "type": "image/png"}], "start": 1754500919828, "stop": 1754500920097}], "start": 1754500903911, "stop": 1754500920097}, {"name": "验证GoogleMap应用已打开", "status": "passed", "start": 1754500920097, "stop": 1754500920097}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9b513bbd-7e3e-4f44-b4f3-2d4993703e23-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "34c29aaf-add5-4321-b4ea-ad21fe3a1b5f-attachment.png", "type": "image/png"}], "start": 1754500920097, "stop": 1754500920277}], "attachments": [{"name": "stdout", "source": "6a181a20-5fff-41a5-ac51-7102bfc15111-attachment.txt", "type": "text/plain"}], "start": 1754500903911, "stop": 1754500920277, "uuid": "c228ae4d-1fae-4381-8c43-2041b37bab67", "historyId": "488c24d02f5d5348f35881280e505f32", "testCaseId": "488c24d02f5d5348f35881280e505f32", "fullName": "testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image.TestEllaNavigationAddressTheImage#test_navigation_to_the_address_in_the_image", "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_navigation_to_the_address_in_the_image"}, {"name": "subSuite", "value": "TestEllaNavigationAddressTheImage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_navigation_to_the_address_in_the_image"}]}