{"name": "测试turn on the screen record能正常执行", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Screen recording started']\nassert False", "trace": "self = <testcases.test_ella.system_coupling.test_end_screen_recording.TestEllaTurnScreenRecord object at 0x000001BE0D557810>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x000001BE0DE603D0>\n\n    @allure.title(f\"测试{command}能正常执行\")\n    @allure.description(f\"{command}\")\n    @allure.severity(allure.severity_level.CRITICAL)\n    @pytest.mark.smoke\n    def test_turn_on_the_screen_record(self, ella_app):\n        f\"\"\"{self.command}\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text, files_status = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望内容\"):\n            expected_text = self.expected_text\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\system_coupling\\test_end_screen_recording.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <testcases.test_ella.system_coupling.test_end_screen_recording.TestEllaTurnScreenRecord object at 0x000001BE0D557810>, expected_text = ['Screen recording started']\nresponse_text = ['turn on the screen record', '屏幕录制开始。', '', '']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['Screen recording started']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:923: AssertionError"}, "description": "turn on the screen record", "steps": [{"name": "执行命令: turn on the screen record", "status": "passed", "steps": [{"name": "执行命令: turn on the screen record", "status": "passed", "start": 1754497047940, "stop": 1754497067562}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "da5fe597-2be1-4b41-a6c2-f92b6ab446fe-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d5ddd358-7d05-4cef-9112-7c373d403971-attachment.png", "type": "image/png"}], "start": 1754497067562, "stop": 1754497067723}], "start": 1754497047940, "stop": 1754497067724}, {"name": "验证响应包含期望内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Screen recording started']\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\system_coupling\\test_end_screen_recording.py\", line 32, in test_turn_on_the_screen_record\n    result = self.verify_expected_in_response(expected_text, response_text)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 923, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n"}, "start": 1754497067724, "stop": 1754497067725}], "attachments": [{"name": "stdout", "source": "7b8a6377-216a-4b21-912f-4d3b635d8605-attachment.txt", "type": "text/plain"}], "start": 1754497047940, "stop": 1754497067726, "uuid": "f4d058af-b193-485e-895b-91e6208909bc", "historyId": "2ead070abca949d328b727d85148a577", "testCaseId": "2ead070abca949d328b727d85148a577", "fullName": "testcases.test_ella.system_coupling.test_end_screen_recording.TestEllaTurnScreenRecord#test_turn_on_the_screen_record", "labels": [{"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.system_coupling"}, {"name": "suite", "value": "test_end_screen_recording"}, {"name": "subSuite", "value": "TestEllaTurnScreenRecord"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "28436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.system_coupling.test_end_screen_recording"}]}